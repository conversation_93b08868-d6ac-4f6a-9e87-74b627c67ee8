@use '_malou_menus' as *;
@use '_malou_tooltips' as *;
@use '_malou_accordion' as *;
@use '_malou_animations' as *;
@use '_malou_avatars' as *;
@use '_malou_backdrops' as *;
@use '_malou_borders' as *;
@use '_malou_boxshadows' as *;
@use '_malou_buttons' as *;
@use '_malou_card' as *;
@use '_malou_checkbox' as *;
@use '_malou_chips' as *;
@use '_malou_colors' as *;
@use '_malou_components' as *;
@use '_malou_date-picker' as *;
@use '_malou_dialog' as *;
@use '_malou_fonts' as *;
@use '_malou_lists' as *;
@use '_malou_logo' as *;
@use '_malou_mixins' as *;
@use '_malou_overflow' as *;
@use '_malou_paginator' as *;
@use '_malou_posts.scss' as *;
@use '_malou_radios' as *;
@use '_malou_scrollbar' as *;
@use '_malou_select' as *;
@use '_malou_slide_toggles' as *;
@use '_malou_slider' as *;
@use '_malou_snackbar' as *;
@use '_malou_statuses' as *;
@use '_malou_stepper' as *;
@use '_malou_table' as *;
@use '_malou_tabs' as *;
@use '_malou_variables' as *;
@use '_malou_flex' as *;
@use '_malou_form' as *;
@use '_malou_typography' as *;
@use '_percentage_circle.scss' as *;

.malou-spinner__container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.malou-platform-logo {
    // use on img tag
    border-radius: 50%;
    margin-right: 5px;
    align-self: center;
}

.malou-shadow {
    @include malou-shadow;
}

.malou-break-word {
    overflow-wrap: break-word;
    word-break: break-word;
}
