@use '_malou_typography.scss' as *;

.malou-toast__message {
    @extend .malou-text-12--semibold;
    width: 70%;
}

.malou-toast__content {
    height: 100%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    padding: 20px 0;
}

.malou-toast__content-prefix {
    width: 60px;
    height: 60px;
}

.malou-toast__close {
    float: right;
    margin-top: 15px;
    margin-right: 15px;
    width: 16px;
    height: 16px;
    cursor: pointer;

    mat-icon {
        width: 16px;
        height: 16px;
    }
}

.progress-bar--short {
    animation: full-width 3s linear;
}

.progress-bar--medium {
    animation: full-width 6s linear;
}

.progress-bar--long {
    animation: full-width 9s linear;
}

@keyframes full-width {
    0% {
        width: 100%;
    }
    100% {
        width: 0%;
    }
}
