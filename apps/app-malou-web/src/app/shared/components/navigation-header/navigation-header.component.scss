@use '_malou_scrollbar.scss' as *;
@use '_malou_functions.scss' as *;
@use '_malou_typography.scss' as *;
@use '_malou_variables.scss' as *;

:host ::ng-deep {
    a.mat-mdc-tab-link {
        margin-right: toRem(40px);
        padding: 0;
        @extend .malou-text-14--regular;
        color: $malou-color-text-1;
        min-width: fit-content;

        &.mdc-tab--active {
            @extend .malou-text-14--semibold;
        }

        i {
            @extend .malou-text-14;
            font-style: italic;
            margin: 0 toRem(4px);
        }
    }
    .mdc-tab-indicator__content--underline {
        border-radius: 20px !important;
    }
}

.mobile-navbar {
    border-bottom: 2px solid $malou-color-background-dark;
    padding: toRem(20px) 0;

    a {
        white-space: nowrap;
        @extend .malou-text-14--regular;
        color: $malou-color-text-1;

        &.tab-active {
            @extend .malou-text-14--semibold;
        }

        i {
            @extend .malou-text-14;
            font-style: italic;
            margin: 0 toRem(4px);
        }
    }
}

.ink-bar {
    height: 2px;
    width: 0;
    background-color: $malou-color-primary;
    transition: all 0.5s ease-in-out;
}

.mat-mdc-tab-header {
    @extend .hide-scrollbar;
    overflow-x: auto !important;

    .mat-mdc-tab-header-pagination {
        display: none !important;
    }
}
