@use '_malou_functions.scss' as *;
@use '_malou_typography.scss' as *;
@use '_malou_variables.scss' as *;

.malou-search__parent-container {
    height: toRem(50px);

    border-radius: 10px;
    border: 1px solid $malou-color-border-primary;

    background-color: white;

    &.focused {
        border: 1px solid $malou-color-border-secondary;
    }

    .malou-search__input {
        border-radius: 10px;
        border: 0;
        outline: none;

        background-color: white;

        @extend .malou-text-12--medium;

        &.empty-value {
            @extend .malou-text-12;
            font-style: italic;
        }
    }

    .malou-search__icon {
        color: $malou-color-primary;
    }
}
