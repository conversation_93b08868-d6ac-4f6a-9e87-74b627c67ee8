import { ChangeDetectionStrategy, Component, computed, input, OnInit, Signal } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Chart, ChartDataset, ChartOptions, ChartType, Plugin, TooltipItem } from 'chart.js';
import { EmptyObject } from 'chart.js/dist/types/basic';
import { NgChartsModule } from 'ng2-charts';

import { ReviewAnalysisChartDataSentiment, ReviewAnalysisChartDataTag } from '@malou-io/package-utils';

import { ReviewAnalysesChartDataByRestaurantId } from ':shared/components/review-analyses-v2/review-analyses-chart-data-by-restaurant-id/review-analyses-chart-data-by-restaurant-id';
import { ChartDataArray, malouChartColorGreen, malouChartColorRed, malouChartColorText1, malouChartColorText2 } from ':shared/helpers';
import { SegmentAnalyses } from ':shared/models';

type DoughnutChartType = Extract<ChartType, 'doughnut'>;

const colors = [malouChartColorGreen, malouChartColorRed];

enum Quarter {
    TOP_RIGHT,
    TOP_LEFT,
    BOTTOM_RIGHT,
    BOTTOM_LEFT,
}

enum ChartDataIndex {
    POSITIVE,
    NEGATIVE,
}

interface CustomChartLabel {
    value: number;
    percentageValue: number;
    subText: string[];
}

export enum DoughnutChartSize {
    DEFAULT,
    SMALL,
}

@Component({
    selector: 'app-tags-doughnut-chart',
    templateUrl: './tags-doughnut-chart.component.html',
    styleUrls: ['./tags-doughnut-chart.component.scss'],
    imports: [NgChartsModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagsDoughnutChartComponent implements OnInit {
    chartData = input.required<ReviewAnalysesChartDataByRestaurantId>();
    readonly size = input<DoughnutChartSize>(DoughnutChartSize.DEFAULT);

    readonly CHART_TYPE: DoughnutChartType = 'doughnut';
    readonly CENTER_TEXT_PLUGIN: Plugin = this._getCenterTextPlugin();
    readonly DOUGHNUT_LABEL_LINE: Plugin = this._getDoughnutLabelLinePlugin();

    readonly chartDataSets: Signal<ChartDataset<DoughnutChartType, ChartDataArray>[]> = computed(() => {
        const chartData = this.chartData();
        return this._computeChartData(chartData);
    });
    readonly chartLabels: Signal<CustomChartLabel[]> = computed(() => {
        const chartData = this.chartData();
        return this._computeChartLabels(chartData);
    });
    chartOption: ChartOptions<DoughnutChartType>;

    allSegmentAnalyses: SegmentAnalyses[];

    constructor(private readonly _translate: TranslateService) {}

    ngOnInit(): void {
        this.chartOption = this._computeChartOptions();
    }

    private _computeChartData(chartData: ReviewAnalysesChartDataByRestaurantId): ChartDataset<DoughnutChartType, ChartDataArray>[] {
        const { positiveSentimentsPercentage, negativeSentimentsPercentage } = chartData.getSentimentPercentage(
            ReviewAnalysisChartDataTag.TOTAL
        );

        return [
            {
                backgroundColor: colors,
                borderColor: colors,
                data: [positiveSentimentsPercentage, negativeSentimentsPercentage],
                borderWidth: 0,
            },
        ];
    }

    private _computeChartLabels(chartData: ReviewAnalysesChartDataByRestaurantId): CustomChartLabel[] {
        const positiveSentimentsCount = chartData.getCount(ReviewAnalysisChartDataTag.TOTAL, ReviewAnalysisChartDataSentiment.POSITIVE);
        const negativeSentimentsCount = chartData.getCount(ReviewAnalysisChartDataTag.TOTAL, ReviewAnalysisChartDataSentiment.NEGATIVE);
        const { positiveSentimentsPercentage, negativeSentimentsPercentage } = chartData.getSentimentPercentage(
            ReviewAnalysisChartDataTag.TOTAL
        );

        return [
            {
                value: positiveSentimentsCount,
                percentageValue: positiveSentimentsPercentage,
                subText: [
                    this._translate.instant('aggregated_statistics.e_reputation.reviews_analysis.feelings'),
                    this._translate.instant('aggregated_statistics.e_reputation.reviews_analysis.positive'),
                ],
            },
            {
                value: negativeSentimentsCount,
                percentageValue: negativeSentimentsPercentage,
                subText: [
                    this._translate.instant('aggregated_statistics.e_reputation.reviews_analysis.feelings'),
                    this._translate.instant('aggregated_statistics.e_reputation.reviews_analysis.negative'),
                ],
            },
        ];
    }

    private _computeChartOptions(): ChartOptions<DoughnutChartType> {
        return {
            plugins: {
                tooltip: {
                    callbacks: {
                        title: () => '',
                        label: (tooltipItem: TooltipItem<any>): string => {
                            const labels = tooltipItem.chart.data.labels as CustomChartLabel[];
                            const { value } = labels[tooltipItem.dataIndex];
                            if (tooltipItem.dataIndex === ChartDataIndex.POSITIVE) {
                                return ` ${this._translate.instant(
                                    'statistics.e_reputation.reviews_analysis.positive_sentiments'
                                )}: ${value}`;
                            }
                            return ` ${this._translate.instant('statistics.e_reputation.reviews_analysis.negative_sentiments')}: ${value}`;
                        },
                    },
                },
                legend: {
                    display: false,
                },
            },
            cutout: '80%',
            layout: {
                padding: {
                    top: this.size() === DoughnutChartSize.SMALL ? 20 : 40,
                    bottom: this.size() === DoughnutChartSize.SMALL ? 20 : 50,
                    left: this.size() === DoughnutChartSize.SMALL ? 60 : 150,
                    right: this.size() === DoughnutChartSize.SMALL ? 60 : 150,
                },
            },
            maintainAspectRatio: false,
            responsive: true,
        };
    }

    private _getCenterTextPlugin(): Plugin {
        return {
            id: 'centerText',
            afterDraw: (chart: Chart, _args: EmptyObject): void => {
                const { ctx } = chart;
                ctx.save();
                const x = chart.getDatasetMeta(0).data[0].x;
                const y = chart.getDatasetMeta(0).data[0].y;

                ctx.font = this.size() === DoughnutChartSize.SMALL ? '600 14px Poppins' : '600 16px Poppins';
                ctx.fillStyle = malouChartColorText2;

                if (this.size() === DoughnutChartSize.SMALL) {
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    const firstLineText = this.chartData()
                        .getCount(ReviewAnalysisChartDataTag.TOTAL, ReviewAnalysisChartDataSentiment.TOTAL)
                        .toString();
                    const secondLineText = this._translate.instant('aggregated_statistics.e_reputation.reviews_analysis.feelings');
                    ctx.fillText(firstLineText, x, y - 5);
                    ctx.fillText(secondLineText, x, y + 10);
                } else {
                    const text = `${this.chartData().getCount(ReviewAnalysisChartDataTag.TOTAL, ReviewAnalysisChartDataSentiment.TOTAL)} ${this._translate.instant('aggregated_statistics.e_reputation.reviews_analysis.feelings')}`;
                    const textWidth = ctx.measureText(text);
                    ctx.fillText(text, x - textWidth.width / 2, y + 10);
                }
                ctx.restore();
            },
        };
    }

    private _getDoughnutLabelLinePlugin(): Plugin {
        return {
            id: 'doughnutLabelLine',
            afterDraw: (chart: Chart, _args: EmptyObject): void => {
                const { ctx } = chart;
                const centerX = chart.getDatasetMeta(0).data[0].x;
                const centerY = chart.getDatasetMeta(0).data[0].y;
                const labels = chart.data.labels as CustomChartLabel[];
                const isSmallSize = this.size() === DoughnutChartSize.SMALL;

                chart.data.datasets.forEach((dataset, i) => {
                    chart.getDatasetMeta(i).data.forEach((dataPoint, index) => {
                        ctx.save();
                        const { x, y } = dataPoint.tooltipPosition(true);
                        if (dataset.borderColor) {
                            ctx.strokeStyle = dataset.borderColor[index];
                        }

                        if (isSmallSize) {
                            this._handleLabelsForSmallChart({
                                ctx,
                                centerX,
                                chartWidth: chart.width,
                                chartHeight: chart.height,
                                labels,
                                index,
                            });
                        } else {
                            this._handleLabelsForDefaultChart({
                                ctx,
                                centerX,
                                centerY,
                                x,
                                y,
                                labels,
                                index,
                            });
                        }
                    });
                });
            },
        };
    }

    private _handleLabelsForSmallChart({ ctx, centerX, chartWidth, chartHeight, labels, index }): void {
        const isTopLabel = index === 1;

        const leftSideRatio = 0.25;
        const rightSideRatio = 0.75;
        const labelX = isTopLabel ? chartWidth * leftSideRatio : chartWidth * rightSideRatio;

        const topPadding = 20;
        const bottomPadding = 20;
        const labelY = isTopLabel ? topPadding : chartHeight - bottomPadding;

        const centerY = chartHeight / 2;
        const radius = Math.min(chartHeight, chartWidth) * 0.3;

        const fixedY = isTopLabel ? centerY - radius + 10 : centerY + radius - 10;
        const fixedX = isTopLabel ? centerX : centerX + 1;

        const forcedCenterY = isTopLabel ? centerY + 100 : centerY - 100;

        this._drawLabelsLines({
            ctx,
            centerX,
            centerY: forcedCenterY,
            x: fixedX,
            y: fixedY,
        });

        ctx.stroke();

        const label = labels[index];

        ctx.textAlign = 'center';
        ctx.textBaseline = isTopLabel ? 'top' : 'bottom';

        ctx.font = '600 12px Poppins';
        ctx.fillStyle = malouChartColorText1;
        ctx.fillText(`${Math.round(label.percentageValue)} %`, labelX, isTopLabel ? labelY + 2 : labelY - 24);

        ctx.font = 'italic 400 10px Poppins';
        ctx.fillStyle = malouChartColorText2;
        ctx.fillText(`${label.subText[0]}`, labelX, isTopLabel ? labelY + 16 : labelY - 12);

        if (label.subText[1]) {
            ctx.fillText(`${label.subText[1]}`, labelX, isTopLabel ? labelY + 28 : labelY);
        }

        ctx.restore();
    }

    private _handleLabelsForDefaultChart({ ctx, centerX, centerY, x, y, labels, index }): void {
        const { currentXPos, currentYPos } = this._drawLabelsLines({
            ctx,
            centerX,
            centerY,
            x,
            y,
        });
        ctx.stroke();
        const label = labels[index];

        ctx.textBaseline = 'middle';

        ctx.font = this.size() === DoughnutChartSize.SMALL ? '600 12px Poppins' : '600 14px Poppins';
        ctx.fillStyle = malouChartColorText1;
        ctx.fillText(
            `${Math.round(label.percentageValue)} %`,
            currentXPos,
            currentYPos - (this.size() === DoughnutChartSize.SMALL ? 6 : 8)
        );

        ctx.font = this.size() === DoughnutChartSize.SMALL ? 'italic 400 10px Poppins' : 'italic 400 12px Poppins';
        ctx.fillStyle = malouChartColorText2;
        ctx.fillText(`${label.subText[0]}`, currentXPos, currentYPos + (this.size() === DoughnutChartSize.SMALL ? 6 : 8));
        ctx.fillText(`${label.subText[1]}`, currentXPos, currentYPos + (this.size() === DoughnutChartSize.SMALL ? 16 : 20));

        ctx.restore();
    }

    private _drawLabelsLines({ ctx, centerX, centerY, x, y }): { currentXPos: number; currentYPos: number } {
        const quarter = this._getQuarter(centerX, centerY, x, y);
        ctx.beginPath();
        ctx.moveTo(x, y);

        const gap = this.size() === DoughnutChartSize.SMALL ? 25 : 30;
        const gap_2x = this.size() === DoughnutChartSize.SMALL ? 35 : 50;
        let currentXPos: number;
        let currentYPos: number;

        switch (quarter) {
            case Quarter.TOP_RIGHT:
                currentXPos = x + gap_2x;
                currentYPos = y + gap;
                ctx.lineTo(x + gap, y + gap);
                ctx.lineTo(x + gap_2x, y + gap);
                break;
            case Quarter.BOTTOM_RIGHT:
                currentXPos = x + gap_2x;
                currentYPos = y - gap;
                ctx.lineTo(x + gap, y - gap);
                ctx.lineTo(x + gap_2x, y - gap);
                break;
            case Quarter.BOTTOM_LEFT:
                currentXPos = x - gap_2x;
                currentYPos = y - gap;
                ctx.lineTo(x - gap, y - gap);
                ctx.lineTo(x - gap_2x, y - gap);
                ctx.textAlign = 'end';
                break;
            case Quarter.TOP_LEFT:
                currentXPos = x - gap_2x;
                currentYPos = y + gap;
                ctx.lineTo(x - gap, y + gap);
                ctx.lineTo(x - gap_2x, y + gap);
                ctx.textAlign = 'end';
                break;
            default:
                currentXPos = x;
                currentYPos = y;
                break;
        }
        return { currentXPos, currentYPos };
    }

    private _getQuarter(centerX: number, centerY: number, x: number, y: number): Quarter {
        if (x > centerX) {
            if (y > centerY) {
                return Quarter.TOP_RIGHT;
            } else {
                return Quarter.BOTTOM_RIGHT;
            }
        } else {
            if (y > centerY) {
                return Quarter.TOP_LEFT;
            } else {
                return Quarter.BOTTOM_LEFT;
            }
        }
    }
}
