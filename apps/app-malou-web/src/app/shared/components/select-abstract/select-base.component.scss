@use '_malou_boxshadows.scss' as *;
@use '_malou_variables.scss' as *;
@use '_malou_typography.scss' as *;

mat-icon {
    transition: transform 0.1s linear;

    &.panel-opened {
        transform: rotate(180deg);
    }
}

::ng-deep .select-base__mat-autocomplete {
    @apply rounded-lg bg-white #{!important};

    mat-option {
        height: unset;
        min-height: 48px !important;
        @apply malou-text-15--regular #{!important};

        &:hover {
            background-color: $malou-color-border-primary !important;
            font-weight: 500 !important;
        }

        &.selected {
            margin-bottom: 1px;
            background-color: $malou-color-border-primary !important;
            @apply malou-text-14--semibold #{!important};
        }

        &.mat-mdc-selected {
            color: $malou-color-text-2 !important;
        }
    }

    .mdc-checkbox {
        margin-top: 4px;
    }

    .mdc-label {
        line-height: 14px;
    }
}

::ng-deep .mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) {
    &:not(.mat-mdc-option-multiple) {
        background-color: $malou-color-background-dark !important;
    }

    .mdc-list-item__primary-text {
        color: $malou-color-text-2 !important;
        font-weight: 500;
    }
}
