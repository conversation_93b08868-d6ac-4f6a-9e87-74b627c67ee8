@use '_malou_typography.scss' as *;
@use '_malou_variables.scss' as *;
@use '_malou_borders.scss' as *;
@use '_malou_animations.scss' as *;

.date-picker {
    width: 300px;
    font-size: 13px;
    padding: 0 10px;
}

:host::ng-deep.mat-mdc-menu-panel {
    max-width: fit-content !important;
}

.mat-mdc-button {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
    color: $malou-color-grey-light;
    background: $malou-color-white;
    border-radius: 10px;
}

::ng-deep .mdc-list {
    padding: 0 !important;

    .selected-period {
        background-color: $malou-color-background-dark !important;
    }
}

.calendar-container {
    @media (max-width: 650px) {
        flex-wrap: wrap;
    }
}
