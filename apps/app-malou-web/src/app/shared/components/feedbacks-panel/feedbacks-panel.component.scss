@use '_malou_typography.scss' as *;
@use '_malou_variables.scss' as *;

.feedback-message:not(:last-child) {
    border-bottom: 1px solid $malou-color-background-dark !important;
}

#chat-bar {
    border-top: 1px solid $malou-color-background-dark;
}

.feedback-message:hover .hidden {
    display: block !important;
}

.overflow-y-scroll::-webkit-scrollbar {
    display: none;
}

:host::ng-deep.mention-item {
    @extend .malou-text-10--semibold;
}
:host::ng-deep.mention-active {
    .mention-item {
        background-color: $malou-color-primary !important;
    }
}
