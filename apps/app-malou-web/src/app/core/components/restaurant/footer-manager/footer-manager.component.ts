import { NgClass } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject, takeUntil } from 'rxjs';

import { PostPublicationStatusFooterComponent } from ':shared/components-v3/post-publication-status-footer/post-publication-status-footer.component';
import { ReviewSynchronizationFooterComponent } from ':shared/components-v3/review-synchronization-footer/review-synchronization-footer.component';
import { CampaignFooterComponent } from ':shared/components/campaign-footer/campaign-footer.component';
import { AutoUnsubscribeOnDestroy } from ':shared/decorators/auto-unsubscribe-on-destroy.decorator';
import { KillSubscriptions } from ':shared/interfaces';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';

import { AvailableFooterType } from './store/footer-manager.interface';
import { selectOpenedFootersOrder } from './store/footer-manager.reducer';

@Component({
    selector: 'app-footer-manager',
    templateUrl: './footer-manager.component.html',
    styleUrls: ['./footer-manager.component.scss'],
    imports: [
        NgClass,
        CampaignFooterComponent,
        PostPublicationStatusFooterComponent,
        ReviewSynchronizationFooterComponent,
        ApplySelfPurePipe,
    ],
})
@AutoUnsubscribeOnDestroy()
export class FooterManagerComponent implements OnInit, KillSubscriptions {
    AvailableFooterType = AvailableFooterType;

    openedFootersOrder: AvailableFooterType[] = [];
    readonly killSubscriptions$: Subject<void> = new Subject();

    constructor(private readonly _store: Store) {}

    ngOnInit(): void {
        this._store
            .select(selectOpenedFootersOrder)
            .pipe(takeUntil(this.killSubscriptions$))
            .subscribe((openedFootersOrder) => {
                this.openedFootersOrder = openedFootersOrder;
            });
    }
}
