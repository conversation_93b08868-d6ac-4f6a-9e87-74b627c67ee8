@use '_malou_variables.scss' as *;
@use '_malou_typography.scss' as *;
@use '_malou_functions.scss' as *;
@use '_malou_scrollbar.scss' as *;

::ng-deep .mat-mdc-tab-body-content {
    margin-top: 10px;
    @extend .hide-scrollbar;
}

::ng-deep mat-tab-body.mat-mdc-tab-body.mat-mdc-tab-body-active {
    overflow-y: hidden;
}

:host ::ng-deep mat-tab-header {
    .mdc-tab {
        height: 30px;
    }
}

::ng-deep #hoursContainer .mat-mdc-tab-body-wrapper {
    height: 100%;
}
