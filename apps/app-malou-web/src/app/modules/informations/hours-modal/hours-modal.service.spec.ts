import { TestBed } from '@angular/core/testing';
import { TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';

import { HoursModalService } from ':modules/informations/hours-modal/hours-modal.service';
import { MyDate, Period, SpecialDatePeriod, SpecialTimePeriod, TIME_24 } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';

describe('HoursModalService', () => {
    let service: HoursModalService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                TranslateModule.forRoot({
                    loader: {
                        provide: TranslateLoader,
                        useClass: TranslateFakeLoader,
                    },
                }),
            ],
            providers: [TranslateService, EnumTranslatePipe],
        }).compileComponents();
        service = TestBed.inject(HoursModalService);
    });

    fdescribe('getSpecialHoursFromSpecialHoursState', () => {
        it('should return empty array when given empty input', () => {
            const result = service.getSpecialHoursFromSpecialHoursState([]);
            expect(result).toEqual([]);
        });

        it('should map closed long special date periods to multiple closed special time periods', () => {
            const startDate = new MyDate({ day: 1, month: 5, year: 2024 });
            const endDate = new MyDate({ day: 3, month: 5, year: 2024 });
            const specialDatePeriod = new SpecialDatePeriod({
                name: 'Holiday',
                startDate,
                endDate,
                periods: [],
                isClosed: true,
                isFromCalendarEvent: false,
            });

            const result = service.getSpecialHoursFromSpecialHoursState([specialDatePeriod]);

            expect(result).toEqual([
                new SpecialTimePeriod({
                    isClosed: true,
                    openTime: null,
                    closeTime: null,
                    name: 'Holiday',
                    startDate,
                    endDate: startDate,
                    isFromCalendarEvent: false,
                }),
                new SpecialTimePeriod({
                    isClosed: true,
                    openTime: null,
                    closeTime: null,
                    name: 'Holiday',
                    startDate: new MyDate({ day: 2, month: 5, year: 2024 }),
                    endDate: new MyDate({ day: 2, month: 5, year: 2024 }),
                    isFromCalendarEvent: false,
                }),
                new SpecialTimePeriod({
                    isClosed: true,
                    openTime: null,
                    closeTime: null,
                    name: 'Holiday',
                    startDate: endDate,
                    endDate,
                    isFromCalendarEvent: false,
                }),
            ]);
        });

        it('should map open special date periods with periods to special time periods', () => {
            const date = new MyDate({ day: 1, month: 5, year: 2024 });
            const period = new Period({ openTime: '10:00', closeTime: '18:00' });
            const specialDatePeriod = new SpecialDatePeriod({
                name: 'Special Open',
                startDate: date,
                endDate: date,
                periods: [period],
                isClosed: false,
                isFromCalendarEvent: false,
            });

            const result = service.getSpecialHoursFromSpecialHoursState([specialDatePeriod]);

            expect(result).toEqual([
                new SpecialTimePeriod({
                    isClosed: false,
                    openTime: '10:00',
                    closeTime: '18:00',
                    name: 'Special Open',
                    startDate: date,
                    endDate: date,
                    isFromCalendarEvent: false,
                }),
            ]);
        });

        it('should handle and clean full day periods', () => {
            const date = new MyDate({ day: 1, month: 5, year: 2024 });
            const period = new Period({ openTime: TIME_24 });
            const specialDatePeriod = new SpecialDatePeriod({
                name: 'Full Day',
                startDate: date,
                endDate: date,
                periods: [period],
                isClosed: false,
                isFromCalendarEvent: false,
            });

            const result = service.getSpecialHoursFromSpecialHoursState([specialDatePeriod]);

            expect(result[0].openTime).toBe('00:00');
            expect(result[0].closeTime).toBe('24:00');
        });

        it('should handle overlapping periods correctly', () => {
            const date = new MyDate({ day: 1, month: 5, year: 2024 });
            const period1 = new Period({ openTime: '10:00', closeTime: '15:00' });
            const period2 = new Period({ openTime: '14:00', closeTime: '16:00' });
            const specialDatePeriod1 = new SpecialDatePeriod({
                name: 'Overlapping Periods',
                startDate: date,
                endDate: date,
                periods: [period1, period2],
                isClosed: false,
                isFromCalendarEvent: false,
            });

            const result = service.getSpecialHoursFromSpecialHoursState([specialDatePeriod1]);

            expect(result).toEqual([
                new SpecialTimePeriod({
                    isClosed: false,
                    openTime: '10:00',
                    closeTime: '16:00',
                    name: 'Overlapping Periods',
                    startDate: date,
                    endDate: date,
                    isFromCalendarEvent: false,
                }),
            ]);
        });

        it('should handle a single period on multiple days', () => {
            const date1 = new MyDate({ day: 1, month: 5, year: 2024 });
            const date2 = new MyDate({ day: 2, month: 5, year: 2024 });
            const period1 = new Period({ openTime: '10:00', closeTime: '01:00' });
            const specialDatePeriod1 = new SpecialDatePeriod({
                name: 'Overlapping Periods',
                startDate: date1,
                endDate: date2,
                periods: [period1],
                isClosed: false,
                isFromCalendarEvent: false,
            });

            const result = service.getSpecialHoursFromSpecialHoursState([specialDatePeriod1]);

            expect(result).toEqual([
                new SpecialTimePeriod({
                    isClosed: false,
                    openTime: '10:00',
                    closeTime: '01:00',
                    name: 'Overlapping Periods',
                    startDate: date1,
                    endDate: date2,
                    isFromCalendarEvent: false,
                }),
            ]);
        });

        it('should handle multiple periods on multiple days', () => {
            const date1 = new MyDate({ day: 1, month: 5, year: 2024 });
            const date2 = new MyDate({ day: 3, month: 5, year: 2024 });
            const period1 = new Period({ openTime: '11:00', closeTime: '15:00' });
            const period2 = new Period({ openTime: '18:00', closeTime: '01:00' });
            const specialDatePeriod1 = new SpecialDatePeriod({
                name: 'Overlapping Periods',
                startDate: date1,
                endDate: date2,
                periods: [period1, period2],
                isClosed: false,
                isFromCalendarEvent: false,
            });

            const result = service.getSpecialHoursFromSpecialHoursState([specialDatePeriod1]);

            expect(result).toEqual([
                new SpecialTimePeriod({
                    isClosed: false,
                    openTime: '11:00',
                    closeTime: '15:00',
                    name: 'Overlapping Periods',
                    startDate: date1,
                    endDate: date1,
                    isFromCalendarEvent: false,
                }),
                new SpecialTimePeriod({
                    isClosed: false,
                    openTime: '18:00',
                    closeTime: '01:00',
                    name: 'Overlapping Periods',
                    startDate: date1,
                    endDate: new MyDate({ day: 2, month: 5, year: 2024 }),
                    isFromCalendarEvent: false,
                }),
                new SpecialTimePeriod({
                    isClosed: false,
                    openTime: '11:00',
                    closeTime: '15:00',
                    name: 'Overlapping Periods',
                    startDate: new MyDate({ day: 2, month: 5, year: 2024 }),
                    endDate: new MyDate({ day: 2, month: 5, year: 2024 }),
                    isFromCalendarEvent: false,
                }),
                new SpecialTimePeriod({
                    isClosed: false,
                    openTime: '18:00',
                    closeTime: '01:00',
                    name: 'Overlapping Periods',
                    startDate: new MyDate({ day: 2, month: 5, year: 2024 }),
                    endDate: date2,
                    isFromCalendarEvent: false,
                }),
                new SpecialTimePeriod({
                    isClosed: false,
                    openTime: '11:00',
                    closeTime: '15:00',
                    name: 'Overlapping Periods',
                    startDate: date2,
                    endDate: date2,
                    isFromCalendarEvent: false,
                }),
                new SpecialTimePeriod({
                    isClosed: false,
                    openTime: '18:00',
                    closeTime: '01:00',
                    name: 'Overlapping Periods',
                    startDate: date2,
                    endDate: new MyDate({ day: 4, month: 5, year: 2024 }),
                    isFromCalendarEvent: false,
                }),
            ]);
        });
    });
});
