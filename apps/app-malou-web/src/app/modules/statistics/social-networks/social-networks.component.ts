import { As<PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, Component, computed, ElementRef, inject, Signal, signal, ViewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuItem } from '@angular/material/menu';
import { Sort } from '@angular/material/sort';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';
import { combineLatest, EMPTY, Observable, switchMap, take } from 'rxjs';

import { InsightsChart, InsightsTab, PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { ToastService } from ':core/services/toast.service';
import { FiltersComponent } from ':modules/statistics/filters/filters.component';
import { CommunityV2Component } from ':modules/statistics/social-networks/community-v2/community.component';
import { EngagementV2Component } from ':modules/statistics/social-networks/engagement-v2/engagement.component';
import { PostsInsightsTableComponent } from ':modules/statistics/social-networks/posts-insights-table/posts-insights-table.component';
import { CommunityChartData } from ':modules/statistics/social-networks/social-networks.interfaces';
import * as StatisticsSelectors from ':modules/statistics/store/statistics.selectors';
import { selectFollowersData, selectFollowersDataV2 } from ':modules/statistics/store/statistics.selectors';
import {
    DownloadInsightsModalComponent,
    DownloadInsightsModalData,
} from ':shared/components/download-insights-modal/download-insights-modal.component';
import { ChartOptions } from ':shared/components/download-insights-modal/download-insights.interface';
import { DownloadInsightsSummaryModalComponent } from ':shared/components/download-insights-summary-modal/download-insights-summary-modal.component';
import { DownloadInsightsSummaryModalParams } from ':shared/components/download-insights-summary-modal/download-insights-summary.interface';
import { MenuButtonV2Component } from ':shared/components/menu-button-v2/menu-button-v2.component';
import { MenuButtonSize } from ':shared/components/menu-button-v2/menu-button-v2.interface';
import { ViewBy } from ':shared/enums/view-by.enum';
import { TimeScaleToMetricToDataValues } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-statistics-social-networks',
    templateUrl: './social-networks.component.html',
    styleUrls: ['./social-networks.component.scss'],
    imports: [
        NgTemplateOutlet,
        PostsInsightsTableComponent,
        FiltersComponent,
        AsyncPipe,
        IllustrationPathResolverPipe,
        TranslateModule,
        MatButtonModule,
        LazyLoadImageModule,
        CommunityV2Component,
        EngagementV2Component,
        MenuButtonV2Component,
        EnumTranslatePipe,
        MatMenuItem,
    ],
})
export class SocialNetworksComponent implements AfterViewInit {
    @ViewChild('topOfComponent') topOfComponent: ElementRef<HTMLElement>;

    public readonly screenSizeService = inject(ScreenSizeService);
    private readonly _store = inject(Store);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _experimentationService = inject(ExperimentationService);

    readonly isCommunityLoading = signal(true);
    readonly isEngagementLoading = signal(true);
    readonly isPostInsightsTableLoading = signal(true);

    readonly isLoading = computed(() => this.isCommunityLoading() || this.isEngagementLoading() || this.isPostInsightsTableLoading());

    private readonly _followersV2$ = this._store.select(selectFollowersDataV2);
    readonly followersV2 = toSignal<CommunityChartData>(this._followersV2$);
    private readonly _followers$ = this._store.select(selectFollowersData);
    readonly followers = toSignal<Partial<Record<PlatformKey, TimeScaleToMetricToDataValues>>>(this._followers$);

    readonly followersData = computed<CommunityChartData>(() => this.followersV2() ?? {});

    readonly PlatformFilterPage = PlatformFilterPage;
    readonly Illustration = Illustration;
    readonly InsightsChart = InsightsChart;
    readonly InsightsTab = InsightsTab;
    readonly MenuButtonSize = MenuButtonSize;

    readonly platformKeys$: Observable<PlatformKey[]> = this._store.select(
        StatisticsSelectors.selectPlatformsFilter({ page: PlatformFilterPage.SOCIAL_NETWORKS })
    );
    readonly comparisonPeriod$ = this._store.select(StatisticsSelectors.selectComparisonPeriodFilter);

    readonly platformKeys = toSignal(this.platformKeys$, { initialValue: [] });

    chartOptions: ChartOptions = {
        [InsightsChart.COMMUNITY]: {
            viewBy: ViewBy.DAY,
            hiddenDatasetIndexes: [],
        },
        [InsightsChart.ENGAGEMENT]: {
            viewBy: ViewBy.DAY,
            hiddenDatasetIndexes: [],
        },
        [InsightsChart.POST_INSIGHTS]: {
            tableSortOptions: undefined,
        },
        [InsightsChart.REEL_INSIGHTS]: {
            tableSortOptions: undefined,
        },
        [InsightsChart.STORY_INSIGHTS]: {
            tableSortOptions: undefined,
        },
    };

    readonly isDownloadStatisticsResumeEnabled: Signal<boolean> = toSignal(
        this._experimentationService.isFeatureEnabled$('release-download-statistics-resume'),
        {
            initialValue: false,
        }
    );

    ngAfterViewInit(): void {
        setTimeout(() => this.topOfComponent?.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' }));
    }

    openStatisticsDownload(isSummary: boolean = false): void {
        if (isSummary) {
            this._customDialogService
                .open<DownloadInsightsSummaryModalComponent, DownloadInsightsSummaryModalParams>(DownloadInsightsSummaryModalComponent, {
                    height: undefined,
                    data: {
                        tab: InsightsTab.SUMMARY,
                    },
                })
                .afterClosed();
            return;
        }
        combineLatest([this._store.select(StatisticsSelectors.selectDatesFilter), this.platformKeys$, this.comparisonPeriod$])
            .pipe(
                take(1),
                switchMap(([{ startDate, endDate }, platforms, comparisonPeriod]) => {
                    if (!startDate || !endDate) {
                        this._toastService.openErrorToast(
                            this._translateService.instant('aggregated_statistics.download_insights_modal.please_select_dates')
                        );
                        return EMPTY;
                    }
                    return this._customDialogService
                        .open<DownloadInsightsModalComponent, DownloadInsightsModalData>(DownloadInsightsModalComponent, {
                            height: undefined,
                            data: {
                                tab: InsightsTab.SOCIAL_NETWORKS,
                                filters: {
                                    dates: { startDate, endDate },
                                    comparisonPeriod,
                                    platforms,
                                },
                                chartOptions: this.chartOptions,
                            },
                        })
                        .afterClosed();
                })
            )
            .subscribe();
    }

    onViewByChange(chart: InsightsChart, value: ViewBy): void {
        this.chartOptions = {
            ...this.chartOptions,
            [chart]: {
                ...this.chartOptions[chart],
                viewBy: value,
            },
        };
    }

    onHiddenDatasetIndexesChange(chart: InsightsChart, value: number[]): void {
        this.chartOptions = {
            ...this.chartOptions,
            [chart]: {
                ...this.chartOptions[chart],
                hiddenDatasetIndexes: value,
            },
        };
    }

    onTableSortOptionsChange(event: { chart: InsightsChart; value: Sort }): void {
        this.chartOptions = {
            ...this.chartOptions,
            [event.chart]: {
                ...this.chartOptions[event.chart],
                tableSortOptions: event.value,
            },
        };
    }
}
