<div class="malou-simple-card flex flex-col gap-6 p-5">
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
            <span class="malou-text-22">⭐️</span>
            <div class="flex flex-col gap-1">
                <span class="malou-text-14--bold text-malou-color-text-1">
                    {{ 'automations.review_intelligent_subject.title' | translate }}
                </span>
                <span class="malou-text-12--regular text-malou-color-text-2">
                    {{ 'automations.review_intelligent_subject.subtitle' | translate }}
                </span>
            </div>
        </div>
        <div>
            <button
                class="malou-btn-icon--secondary"
                mat-icon-button
                [disabled]="!isFormValid(automationsFormArray)"
                (click)="onDuplicateAutomations()">
                <mat-icon color="primary" [svgIcon]="SvgIcon.DUPLICATE"></mat-icon>
            </button>
        </div>
    </div>

    <form class="flex flex-col gap-4" [formGroup]="automationsForm">
        @for (automation of automationsFormArray.controls; track automation.value.subject; let i = $index) {
            <div
                class="rounded-[10px] border border-malou-color-border-primary bg-malou-color-background-light p-4"
                formArrayName="automationsFormArray">
                <div class="flex items-center gap-3 md:flex-col" [formGroupName]="i">
                    <div class="flex w-1/3 items-center gap-2 md:w-full">
                        <app-slide-toggle [checked]="!!automation.value.active" (onToggle)="onToggleAutomationActive(i)"></app-slide-toggle>

                        <div class="flex flex-col gap-1" [ngClass]="{ 'opacity-50': !automation.value.active }">
                            <span class="malou-text-12--semibold text-malou-color-text-1">
                                {{ subjectDisplayWith | applyPure: automation.value.subject }}
                            </span>
                            <span class="malou-text-10--regular italic text-malou-color-text-2">
                                {{ subjectSubtextDisplayWith | applyPure: automation.value.subject }}
                            </span>
                        </div>
                    </div>

                    <div class="flex w-2/3 md:w-full">
                        <app-select-recipients
                            class="w-full"
                            formControlName="recipientEmails"
                            [ngClass]="{ 'opacity-50': !automation.value.active }"
                            [values]="allRecipients()"
                            [selectedValues]="automation.value.recipientEmails ?? []"
                            [buildValueFromText]="buildRecipientFromText()"
                            [onAddValue]="onAddRecipient()"
                            [errorMessage]="
                                !!automation.value.active && !automation.value.recipientEmails?.length
                                    ? ('automations.review_intelligent_subject.recipients_error_message' | translate)
                                    : ''
                            "
                            [disabled]="!automation.value.active"
                            [multiSelectionElementWrap]="true"
                            [shouldSwitchToWrapModeOnClick]="false"
                            [shouldUpdateValuesToDisplayAfterSelection]="true"
                            [showValuesSelectedCount]="false">
                        </app-select-recipients>
                    </div>
                </div>
            </div>
        }
    </form>
</div>
