@use '_malou_variables.scss' as *;
@use '_malou_typography.scss' as *;

::ng-deep .pac-container {
    @apply rounded-lg bg-white py-2.5;

    .pac-item {
        @apply flex h-12 items-center border-0 px-4;
        font-family: $malou-font-family;

        .pac-icon {
            display: none;
        }

        .pac-item-query {
            @apply malou-text-15--regular;

            .pac-matched {
                @apply malou-text-15--semibold;
            }
        }

        span {
            @apply malou-text-15--regular overflow-hidden;
        }

        &:hover {
            background-color: $malou-color-border-primary;
        }
    }

    .pac-item-selected {
        @apply malou-text-14--semibold;
        background-color: $malou-color-border-primary;
    }

    &::after {
        display: none;
    }
}
