@use '_malou_mixins.scss' as *;
@use '_malou_functions.scss' as *;
@use '_malou_typography.scss' as *;
@use '_malou_variables.scss' as *;

@include malou-respond-to('medium') {
    .mat-mdc-row {
        margin: toRem(15px) auto !important;
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: repeat(2, 1fr);
        grid-column-gap: 0px;
        grid-row-gap: 0px;
    }
    .mat-column-text,
    .mat-column-volume,
    .mat-column-language {
        flex: 1;
    }

    .mat-mdc-cell:nth-child(2) {
        width: toRem(160px);
        grid-area: 1 / 2 / 2 / 3 !important;
    }
    .mat-mdc-cell:nth-child(4) {
        grid-area: 1 / 3 / 2 / 4 !important;
    }
    .mat-mdc-cell:nth-child(3) {
        grid-area: 2 / 2 / 3 / 4 !important;
    }
    .mat-mdc-cell:nth-child(5) {
        grid-area: 1 / 4 / 3 / 6 !important;
        text-align: end;
        padding-right: 20px !important;
    }

    .mat-mdc-header-cell:nth-child(2) {
        display: flex !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        color: $malou-color-text-1 !important;
    }

    .mat-mdc-header-cell:nth-child(5) {
        display: flex !important;
    }

    .mat-column-move {
        flex: 1;
    }
}
