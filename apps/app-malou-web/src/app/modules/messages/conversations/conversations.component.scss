@use '_malou_typography.scss' as *;
@use '_malou_variables.scss' as *;
@use '_malou_functions.scss' as *;

:host ::ng-deep mat-tab-header {
    padding: 1rem;
    padding-bottom: 0;
    margin-bottom: 1rem;

    .mdc-tab {
        height: toRem(30px);
    }

    .mat-mdc-tab-body-wrapper {
        margin-top: toRem(10px);
    }
}

:host ::ng-deep .mat-mdc-tab-labels {
    justify-content: space-between;
}

:host ::ng-deep mat-tab-group {
    max-height: 100%;
}

:host ::ng-deep .mat-mdc-tab-body-content {
    overflow-y: auto !important;

    &::-webkit-scrollbar {
        display: 'none';
        width: 0;
    }

    -ms-oveflow-style: 'none';

    scrollbar-width: 'none';
}

:host ::ng-deep div[role='tab'] {
    margin: 0;
}

:host ::ng-deep .mdc-tab {
    @extend .malou-text-14--regular;

    padding: 0;
    margin: 0;
    min-width: fit-content;
    color: $malou-color-text-1;
}

:host ::ng-deep {
    .mat-mdc-tab-list {
        transform: inherit !important;
    }

    .mat-mdc-tab-label-container {
        overflow-x: scroll !important;

        &::-webkit-scrollbar {
            display: 'none';
            width: 0;
            height: 0;
        }

        -ms-oveflow-style: 'none';

        scrollbar-width: 'none';
    }
}
