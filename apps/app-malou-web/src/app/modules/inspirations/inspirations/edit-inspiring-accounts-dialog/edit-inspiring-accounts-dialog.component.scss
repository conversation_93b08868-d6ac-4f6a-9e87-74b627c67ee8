@use '_malou_mixins.scss' as *;
@use '_malou_variables.scss' as *;
@use '_malou_typography.scss' as *;
@use '_malou_functions.scss' as *;

.edit-modal-container {
    transition: all 0.3s ease-in-out;

    @include malou-respond-to('medium') {
        transition: none;
    }
}

.custom-dialog-background {
    background-color: $malou-color-background-light;
}

.ig-account-card {
    display: flex;
    flex: 1 1 auto;
    border: 1px solid $malou-color-background-dark;
    background-color: $malou-color-white;
    border-radius: 10px;
    position: relative;
    padding: 13px 13px 16px 13px;
}

.no-search-result-card__border {
    border: solid 1px $malou-color-background-dark;
}

:host ::ng-deep {
    mat-tab-header .mdc-tab {
        height: 30px;
    }

    .mat-mdc-tab-body-wrapper {
        margin-top: toRem(10px);
    }
}
