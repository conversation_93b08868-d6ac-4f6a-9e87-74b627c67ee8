import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    inject,
    input,
    Signal,
    signal,
    ViewChild,
    WritableSignal,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { isNumber } from 'lodash';

import { SegmentAnalysesTopicDto } from '@malou-io/package-dto';
import { HeapEventName, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { HeapService } from ':core/services/heap.service';
import { RestaurantSemanticAnalysisInsights } from ':modules/aggregated-statistics/e-reputation/semantic-analysis/aggregated-semantic-analysis-table/aggregated-semantic-analysis-insights.mapper';
import { NumberEvolutionComponent } from ':shared/components/number-evolution/number-evolution.component';
import { ReviewAnalysesChartFilter } from ':shared/components/review-analyses-v2/review-analyses.interface';
import {
    TabIndex,
    TopicSegmentAnalysisModalComponent,
} from ':shared/components/review-analyses-v2/topic-segment-analysis-modal/topic-segment-analysis-modal.component';
import { TypeSafeMatCellDefDirective } from ':shared/directives/type-safe-mat-cell-def.directive';
import { TypeSafeMatRowDefDirective } from ':shared/directives/type-safe-mat-row-def.directive';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Emoji, EmojiPathResolverPipe } from ':shared/pipes/emojis-path-resolver.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

enum SemanticAnalysisColumns {
    RESTAURANT = 'restaurant',
    SENTIMENT_NUMBER = 'sentimentNumber',
    SENTIMENT = 'sentiment',
    FOOD = ReviewAnalysisTag.FOOD,
    SERVICE = ReviewAnalysisTag.SERVICE,
    ATMOSPHERE = ReviewAnalysisTag.ATMOSPHERE,
    PRICE = ReviewAnalysisTag.PRICE,
    HYGIENE = ReviewAnalysisTag.HYGIENE,
    TOP_TOPICS = 'topTopics',
    EXPANDED_DETAIL = 'expandedDetail',
}

@Component({
    selector: 'app-aggregated-semantic-analysis-table',
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatTableModule,
        MatIconModule,
        MatSortModule,
        MatTooltipModule,
        TranslateModule,
        NumberEvolutionComponent,
        TypeSafeMatCellDefDirective,
        TypeSafeMatRowDefDirective,
        ShortNumberPipe,
        EnumTranslatePipe,
        ImagePathResolverPipe,
        EmojiPathResolverPipe,
    ],
    templateUrl: './aggregated-semantic-analysis-table.component.html',
    styleUrl: './aggregated-semantic-analysis-table.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AggregatedSemanticAnalysisTableComponent implements AfterViewInit {
    readonly tableData = input<RestaurantSemanticAnalysisInsights[] | null>();
    readonly currentFilter = input<ReviewAnalysesChartFilter | null>();
    readonly isPdfDownload = input<boolean>(false);

    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _heapService = inject(HeapService);
    private readonly _router = inject(Router);

    readonly SvgIcon = SvgIcon;
    readonly Emoji = Emoji;
    readonly SemanticAnalysisColumns = SemanticAnalysisColumns;
    readonly ReviewAnalysisTag = ReviewAnalysisTag;
    readonly ReviewAnalysisSentiment = ReviewAnalysisSentiment;
    readonly isNumber = isNumber;
    readonly ALL_RESTAURANTS_ID = 'ALL';

    sort: Sort = { active: SemanticAnalysisColumns.RESTAURANT, direction: ChartSortBy.ASC };
    readonly expandedRow = signal<RestaurantSemanticAnalysisInsights | null>(null);

    private _dataSource = new MatTableDataSource<RestaurantSemanticAnalysisInsights>([]);
    readonly dataSource: Signal<MatTableDataSource<RestaurantSemanticAnalysisInsights>> = computed(() => {
        this._dataSource.data = this.tableData() ?? [];
        return this._dataSource;
    });

    readonly displayedColumns: WritableSignal<SemanticAnalysisColumns[]> = signal(
        Object.values(SemanticAnalysisColumns).filter((column) => column !== SemanticAnalysisColumns.EXPANDED_DETAIL)
    );

    @ViewChild(MatSort, { static: false }) matSort: MatSort;

    constructor() {
        effect(() => {
            const data = this.tableData() ?? [];
            this._dataSource.data = data;
        });
    }

    ngAfterViewInit(): void {
        if (this.matSort) {
            this._dataSource.sortingDataAccessor = (item, property): string | number => {
                switch (property) {
                    case SemanticAnalysisColumns.RESTAURANT:
                        return item.restaurantName ?? '';
                    case SemanticAnalysisColumns.SENTIMENT_NUMBER:
                        return item.sentimentNumber.value ?? 0;
                    case SemanticAnalysisColumns.SENTIMENT:
                        return item.positiveSentimentPercentage.value ?? 0;
                    case SemanticAnalysisColumns.FOOD:
                        return item.foodPositiveSentimentPercentage ?? 0;
                    case SemanticAnalysisColumns.SERVICE:
                        return item.servicePositiveSentimentPercentage ?? 0;
                    case SemanticAnalysisColumns.ATMOSPHERE:
                        return item.atmospherePositiveSentimentPercentage ?? 0;
                    case SemanticAnalysisColumns.PRICE:
                        return item.pricePositiveSentimentPercentage ?? 0;
                    case SemanticAnalysisColumns.HYGIENE:
                        return item.hygienePositiveSentimentPercentage ?? 0;
                    default:
                        return '';
                }
            };

            this._dataSource.sortData = (
                data: RestaurantSemanticAnalysisInsights[],
                sort: MatSort
            ): RestaurantSemanticAnalysisInsights[] => {
                if (!sort.active || sort.direction === '') {
                    return data;
                }

                return data.sort((a, b) => {
                    if (a.restaurantId === this.ALL_RESTAURANTS_ID && b.restaurantId !== this.ALL_RESTAURANTS_ID) {
                        return -1;
                    } else if (a.restaurantId !== this.ALL_RESTAURANTS_ID && b.restaurantId === this.ALL_RESTAURANTS_ID) {
                        return 1;
                    } else {
                        const property = sort.active;
                        const direction = sort.direction === 'asc' ? 1 : -1;
                        const valueA = this._dataSource.sortingDataAccessor(a, property);
                        const valueB = this._dataSource.sortingDataAccessor(b, property);

                        if (valueA < valueB) {
                            return -1 * direction;
                        } else if (valueA > valueB) {
                            return 1 * direction;
                        } else {
                            return 0;
                        }
                    }
                });
            };

            this._dataSource.sort = this.matSort;

            if (this._dataSource.data.length > 0) {
                this.matSort.sort({
                    id: SemanticAnalysisColumns.SENTIMENT_NUMBER,
                    start: 'desc',
                    disableClear: false,
                });
            }
        }
    }

    redirectToRestaurantEReputationStatsPage(restaurantId: string): void {
        if (restaurantId === this.ALL_RESTAURANTS_ID) {
            return;
        }
        const urlTree = ['/restaurants', restaurantId, 'statistics', 'e-reputation'];
        const url = this._router.serializeUrl(
            this._router.createUrlTree(urlTree, {
                queryParams: { tab: 1 },
            })
        );
        window.open(url, '_blank');
    }

    toggleExpandedRow(row: RestaurantSemanticAnalysisInsights): void {
        if (row.restaurantId === this.ALL_RESTAURANTS_ID) {
            return;
        }
        if (this.expandedRow() === row) {
            this.expandedRow.set(null);
        } else {
            this._heapService.track(HeapEventName.TRACKING_AGGREGATED_SEMANTIC_ANALYSIS_ROW_CLICK, {
                restaurantId: row.restaurantId,
            });
            this.expandedRow.set(row);
            setTimeout(() => {
                this.scrollToRow(row.restaurantId);
            }, 100);
        }
    }

    scrollToRow(restaurantId: string): void {
        const rowElement = document.getElementById(`row_${restaurantId}`);

        if (rowElement) {
            rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    openModalForTopic(topic: SegmentAnalysesTopicDto, sentiment: ReviewAnalysisSentiment, restaurantId: string): void {
        this._customDialogService.open(TopicSegmentAnalysisModalComponent, {
            width: '80vw',
            height: '80vh',
            disableClose: true,
            data: {
                topic: topic.name,
                topicTranslations: topic.translations,
                reviewAnalysesFilter: { ...this.currentFilter(), restaurantIds: [restaurantId] },
                isFromAggregatedStatistics: false,
                isMainCategory: false,
                shouldShowSubcategories: false,
                tabIndex: sentiment === ReviewAnalysisSentiment.NEGATIVE ? TabIndex.NEGATIVE : TabIndex.POSITIVE,
            },
        });
    }
}
