import { TranslateService } from '@ngx-translate/core';
import { capitalize, chunk, sum, sumBy } from 'lodash';

import {
    GetSegmentAnalysesTopTopicsResponseDto,
    SegmentAnalysesTopicDto,
    SegmentAnalysisInsightsForRestaurantDto,
} from '@malou-io/package-dto';
import { ApplicationLanguage, isNotNil, ReviewAnalysisTag } from '@malou-io/package-utils';

import { Restaurant } from ':shared/models';
import { AppInjectorService } from ':shared/services/app-injector.service';

interface DataWithEvolution {
    value: number | null;
    evolution: number | null;
}

export interface RestaurantSemanticAnalysisInsights {
    restaurantId: string;
    restaurantName?: string;
    restaurantLogo?: string;
    sentimentNumber: DataWithEvolution;
    positiveSentimentPercentage: DataWithEvolution;
    foodPositiveSentimentPercentage: number | null;
    servicePositiveSentimentPercentage: number | null;
    atmospherePositiveSentimentPercentage: number | null;
    pricePositiveSentimentPercentage: number | null;
    hygienePositiveSentimentPercentage: number | null;
    topTopics: {
        positiveTopics: SegmentAnalysesTopicDto[][];
        negativeTopics: SegmentAnalysesTopicDto[][];
    };
}

export namespace SemanticAnalysisInsightsMapper {
    export const ALL_RESTAURANTS_ID = 'ALL';

    export const createSegmentAnalysisInsightsFromDtoArray = (
        data: SegmentAnalysisInsightsForRestaurantDto[],
        restaurants: Restaurant[],
        lang: ApplicationLanguage
    ): RestaurantSemanticAnalysisInsights[] => {
        const mappedRestaurants: RestaurantSemanticAnalysisInsights[] = data.map((insight: SegmentAnalysisInsightsForRestaurantDto) => {
            const associatedRestaurant = restaurants.find((restaurant) => restaurant._id === insight.restaurantId);
            return _buildSingleRestaurantSemanticAnalysisInsights(insight, lang, associatedRestaurant);
        });

        const allRestaurantsData = _getCombinedRestaurantSemanticAnalysisInsights(data);
        return [allRestaurantsData, ...mappedRestaurants];
    };

    const _buildSingleRestaurantSemanticAnalysisInsights = (
        data: SegmentAnalysisInsightsForRestaurantDto,
        lang: ApplicationLanguage,
        restaurant?: Restaurant
    ): RestaurantSemanticAnalysisInsights => {
        const { totalSentiment, totalSentimentEvolution, totalPositiveSentiment, totalPositiveSentimentEvolution } = _getTotalValues(data);
        const currentPositiveSentimentPercentage = totalSentiment ? Math.round((totalPositiveSentiment * 100) / totalSentiment) : null;
        const previousPositiveSentimentPercentage = totalSentimentEvolution
            ? Math.round((totalPositiveSentimentEvolution * 100) / totalSentimentEvolution)
            : null;

        return {
            restaurantId: data.restaurantId,
            restaurantName: restaurant?.internalName || restaurant?.name,
            restaurantLogo: restaurant?.logo?.getMediaUrl('small'),
            sentimentNumber: {
                value: totalSentiment,
                evolution: totalSentimentEvolution,
            },
            positiveSentimentPercentage: {
                value: currentPositiveSentimentPercentage,
                evolution:
                    isNotNil(currentPositiveSentimentPercentage) && isNotNil(previousPositiveSentimentPercentage)
                        ? currentPositiveSentimentPercentage - previousPositiveSentimentPercentage
                        : null,
            },
            foodPositiveSentimentPercentage: _getPositiveSentimentPercentageForCategory({
                totalCount: totalSentiment,
                insights: data,
                category: ReviewAnalysisTag.FOOD,
            }),
            servicePositiveSentimentPercentage: _getPositiveSentimentPercentageForCategory({
                totalCount: totalSentiment,
                insights: data,
                category: ReviewAnalysisTag.SERVICE,
            }),
            atmospherePositiveSentimentPercentage: _getPositiveSentimentPercentageForCategory({
                totalCount: totalSentiment,
                insights: data,
                category: ReviewAnalysisTag.ATMOSPHERE,
            }),
            pricePositiveSentimentPercentage: _getPositiveSentimentPercentageForCategory({
                totalCount: totalSentiment,
                insights: data,
                category: ReviewAnalysisTag.PRICE,
            }),
            hygienePositiveSentimentPercentage: _getPositiveSentimentPercentageForCategory({
                totalCount: totalSentiment,
                insights: data,
                category: ReviewAnalysisTag.HYGIENE,
            }),
            topTopics: _separateTopicsInTwoColumns(data.topTopics, lang),
        };
    };

    const _separateTopicsInTwoColumns = (
        topics: GetSegmentAnalysesTopTopicsResponseDto,
        lang: ApplicationLanguage
    ): {
        positiveTopics: SegmentAnalysesTopicDto[][];
        negativeTopics: SegmentAnalysesTopicDto[][];
    } => {
        const positiveTopics = topics.positiveTopics.map((topic) => ({
            ...topic,
            displayedName: capitalize(topic.name),
            displayedNameInCurrentLang: capitalize(topic.translations[lang] ?? ''),
        }));
        const negativeTopics = topics.negativeTopics.map((topic) => ({
            ...topic,
            displayedName: capitalize(topic.name),
            displayedNameInCurrentLang: capitalize(topic.translations[lang] ?? ''),
        }));
        return {
            positiveTopics: _splitArrayInHalf<SegmentAnalysesTopicDto>(positiveTopics),
            negativeTopics: _splitArrayInHalf<SegmentAnalysesTopicDto>(negativeTopics),
        };
    };

    const _splitArrayInHalf = <T>(list: T[]): T[][] => {
        const middleIndex = Math.ceil(list.length / 2);
        return chunk(list, middleIndex);
    };

    const _getTotalValues = (
        data: SegmentAnalysisInsightsForRestaurantDto
    ): {
        totalSentiment: number;
        totalSentimentEvolution: number;
        totalPositiveSentiment: number;
        totalPositiveSentimentEvolution: number;
    } => {
        const totalPositiveSentiment = sumBy(Object.values(data.sentimentsByCategory), (category) => category.positiveCount);
        const totalPositiveSentimentEvolution = sumBy(
            Object.values(data.sentimentsByCategory),
            (category) => category.positiveCountEvolution
        );
        const totalNegativeSentiment = sumBy(Object.values(data.sentimentsByCategory), (category) => category.negativeCount);
        const totalNegativeSentimentEvolution = sumBy(
            Object.values(data.sentimentsByCategory),
            (category) => category.negativeCountEvolution
        );
        const totalSentiment = totalPositiveSentiment + totalNegativeSentiment;
        const totalSentimentEvolution = totalPositiveSentimentEvolution + totalNegativeSentimentEvolution;

        return {
            totalSentiment,
            totalSentimentEvolution,
            totalPositiveSentiment,
            totalPositiveSentimentEvolution,
        };
    };

    const _getSentimentTotalCountForCategory = (
        insights: SegmentAnalysisInsightsForRestaurantDto,
        category: ReviewAnalysisTag
    ): number | null => {
        const categoryInsights = insights.sentimentsByCategory[category];
        if (!categoryInsights) {
            return null;
        }
        return categoryInsights.positiveCount + categoryInsights.negativeCount;
    };

    const _getPositiveSentimentPercentageForCategory = ({
        totalCount,
        insights,
        category,
    }: {
        totalCount: number;
        insights: SegmentAnalysisInsightsForRestaurantDto;
        category: ReviewAnalysisTag;
    }): number | null => {
        if (!totalCount) {
            return null;
        }
        const categoryInsights = insights.sentimentsByCategory[category];
        if (!categoryInsights) {
            return null;
        }
        const total = _getSentimentTotalCountForCategory(insights, category);
        if (!total) {
            return null;
        }
        return Math.round((categoryInsights.positiveCount * 100) / total);
    };

    const _getCombinedRestaurantSemanticAnalysisInsights = (
        rawData: SegmentAnalysisInsightsForRestaurantDto[]
    ): RestaurantSemanticAnalysisInsights => {
        const translateService = AppInjectorService.getInjector().get(TranslateService);

        const totalSentiment = sum(
            rawData.map((data) =>
                sumBy(Object.values(data.sentimentsByCategory), (category) => category.positiveCount + category.negativeCount)
            )
        );
        const totalSentimentEvolution = sum(
            rawData.map((data) =>
                sumBy(
                    Object.values(data.sentimentsByCategory),
                    (category) => category.positiveCountEvolution + category.negativeCountEvolution
                )
            )
        );
        const totalPositiveSentiment = sum(
            rawData.map((data) => sumBy(Object.values(data.sentimentsByCategory), (category) => category.positiveCount))
        );
        const positiveSentimentPercentage = totalSentiment ? Math.round((totalPositiveSentiment * 100) / totalSentiment) : 0;

        return {
            restaurantId: ALL_RESTAURANTS_ID,
            restaurantName: translateService.instant('admin.notifications.all_businesses'),
            restaurantLogo: '',
            sentimentNumber: {
                value: Math.round(totalSentiment),
                evolution: Math.round(totalSentimentEvolution),
            },
            positiveSentimentPercentage: {
                value: Math.round(positiveSentimentPercentage ?? 0),
                evolution: null,
            },
            foodPositiveSentimentPercentage: _getCombinedPercentageDataForCategory(rawData, ReviewAnalysisTag.FOOD),
            servicePositiveSentimentPercentage: _getCombinedPercentageDataForCategory(rawData, ReviewAnalysisTag.SERVICE),
            atmospherePositiveSentimentPercentage: _getCombinedPercentageDataForCategory(rawData, ReviewAnalysisTag.ATMOSPHERE),
            pricePositiveSentimentPercentage: _getCombinedPercentageDataForCategory(rawData, ReviewAnalysisTag.PRICE),
            hygienePositiveSentimentPercentage: _getCombinedPercentageDataForCategory(rawData, ReviewAnalysisTag.HYGIENE),
            topTopics: {
                positiveTopics: [],
                negativeTopics: [],
            },
        };
    };

    const _getCombinedPercentageDataForCategory = (
        rawData: SegmentAnalysisInsightsForRestaurantDto[],
        categoryKey: ReviewAnalysisTag
    ): number | null => {
        const positiveCount = rawData
            .map((data) => data.sentimentsByCategory[categoryKey])
            .filter(isNotNil)
            .reduce((acc, category) => acc + category.positiveCount, 0);
        const negativeCount = rawData
            .map((data) => data.sentimentsByCategory[categoryKey])
            .filter(isNotNil)
            .reduce((acc, category) => acc + category.negativeCount, 0);
        const totalCount = positiveCount + negativeCount;
        if (!totalCount) {
            return null;
        }
        return Math.round((positiveCount * 100) / totalCount);
    };
}
