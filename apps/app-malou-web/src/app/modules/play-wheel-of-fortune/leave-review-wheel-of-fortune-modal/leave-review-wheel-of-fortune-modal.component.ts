import { Ng<PERSON>tyle, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { PlatformKey, WheelOfFortuneRedirectionPlatformKey } from '@malou-io/package-utils';

import { LocalStorage } from ':core/storage/local-storage';
import { LocalStorageKey } from ':shared/enums/local-storage-key';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-leave-review-wheel-of-fortune-modal',
    templateUrl: './leave-review-wheel-of-fortune-modal.component.html',
    styleUrls: ['./leave-review-wheel-of-fortune-modal.component.scss'],
    imports: [NgStyle, NgTemplateOutlet, MatIconModule, MatButtonModule, TranslateModule, ImagePathResolverPipe, EnumTranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LeaveReviewWheelOfFortuneModalComponent {
    readonly SvgIcon = SvgIcon;
    readonly PlatformKey = PlatformKey;

    constructor(
        private readonly _dialogRef: MatDialogRef<LeaveReviewWheelOfFortuneModalComponent>,
        @Inject(MAT_DIALOG_DATA)
        public data: {
            wheelOfFortuneId: string;
            restaurantId: string;
            primaryColor: string;
            secondaryColor: string;
            redirectionLink: string;
            redirectionPlatform: WheelOfFortuneRedirectionPlatformKey;
        }
    ) {}

    openRedirectionLink(): void {
        if (this.data.redirectionLink) {
            // eslint-disable-next-line max-len
            const queryParams = `primaryColor=${encodeURIComponent(this.data.primaryColor)}&secondaryColor=${encodeURIComponent(
                this.data.secondaryColor
            )}&redirectionPlatform=${encodeURIComponent(this.data.redirectionPlatform)}&redirectionLink=${encodeURIComponent(
                this.data.redirectionLink
            )}`;
            window.open(`${window.location.origin}/wheel-of-fortune-redirect?${queryParams}`, '_blank');
            this._pushLeavedReviewWheelOfFortuneInLocalStorage();
            this.close(true);
        }
    }

    close(hasLeftReview = false): void {
        this._dialogRef.close({ hasLeftReview });
    }

    private _pushLeavedReviewWheelOfFortuneInLocalStorage(): void {
        const leavedReviewRestaurantWheelsOfFortune = JSON.parse(
            LocalStorage.getItem(LocalStorageKey.LEAVED_REVIEW_RESTAURANT_WHEELS_OF_FORTUNE) || '[]'
        );
        leavedReviewRestaurantWheelsOfFortune.push({
            wheelOfFortuneId: this.data.wheelOfFortuneId,
            restaurantId: this.data.restaurantId,
            platformKey: this.data.redirectionPlatform,
        });
        LocalStorage.setItem(
            LocalStorageKey.LEAVED_REVIEW_RESTAURANT_WHEELS_OF_FORTUNE,
            JSON.stringify(leavedReviewRestaurantWheelsOfFortune)
        );
    }
}
