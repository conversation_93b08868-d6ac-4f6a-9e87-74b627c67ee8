import 'reflect-metadata';

import ':env';

import { autoInjectable, container } from 'tsyringe';

import { PlatformModel } from '@malou-io/package-models';

import PlatformsRepository from ':modules/platforms/platforms.repository';
import ':plugins/db';

@autoInjectable()
class InitPlatformDefaultSpecialHoursEndDateTask {
    constructor(private readonly _platformsRepository: PlatformsRepository) {}

    async execute() {
        console.log('InitPlatformDefaultSpecialHoursEndDateTask started');

        const platforms = await this._platformsRepository.find({
            filter: { specialHours: { $exists: true, $ne: [] }, 'specialHours.endDate': null },
            options: { lean: true },
        });

        let bulkOperations: any[] = [];

        for (const platform of platforms) {
            bulkOperations.push({
                updateOne: {
                    filter: { _id: platform._id },
                    update: {
                        $set: {
                            specialHours: platform.specialHours?.map((hour) => ({
                                ...hour,
                                endDate: hour.startDate,
                            })),
                        },
                    },
                },
            });
        }

        await PlatformModel.bulkWrite(bulkOperations);

        console.log('InitPlatformDefaultSpecialHoursEndDateTask finished');
        console.log('Number of platforms updated:', platforms.length);
    }
}

const task = container.resolve(InitPlatformDefaultSpecialHoursEndDateTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
