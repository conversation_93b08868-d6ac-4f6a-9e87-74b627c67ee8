import 'reflect-metadata';

// Required for tsyringe
import ':env';

import { container, singleton } from 'tsyringe';

import PostsRepository from ':modules/posts/posts.repository';
import ':plugins/db';

@singleton()
class InitKeysRequiredTask {
    constructor(private readonly _postsRepository: PostsRepository) {}
    async execute() {
        console.log('InitKeysRequiredTask started');
        const cursor = this._postsRepository.model.find({ keys: null }).lean(true).select({ _id: 1 }).cursor();
        let i = 0;

        for await (const doc of cursor) {
            i++;
            console.log('Updating post #', i);
            await this._postsRepository.updateOne({
                filter: { _id: doc._id },
                update: { $set: { keys: [] } },
            });
        }
        console.log('InitKeysRequiredTask finished');
    }
}

const task = container.resolve(InitKeysRequiredTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
