import 'reflect-metadata';

// Required for tsyringe
import ':env';

import { DateTime } from 'luxon';
import { container, singleton } from 'tsyringe';

import { PostPublicationStatus } from '@malou-io/package-utils';

import PostsRepository from ':modules/posts/posts.repository';
import ':plugins/db';

@singleton()
class DeleteEmptyDraftsTask {
    constructor(private readonly _postsRepository: PostsRepository) {}
    async execute() {
        const startOfDay = DateTime.now().startOf('day').toJSDate();
        const cursor = this._postsRepository.model
            .find({
                $and: [
                    { published: PostPublicationStatus.DRAFT, createdAt: { $lt: startOfDay } },
                    { $or: [{ text: null }, { text: { $eq: '' } }] },
                    { $or: [{ attachments: null }, { attachments: { $eq: [] } }] },
                ],
            })
            .lean(true)
            .select({ _id: 1 })
            .cursor();
        let i = 0;

        for await (const doc of cursor) {
            i++;
            console.log('Deleting post #', i);
            await this._postsRepository.deleteOne({
                filter: { _id: doc._id },
            });
        }
        console.log('DeleteEmptyDraftsTask finished');
    }
}

const task = container.resolve(DeleteEmptyDraftsTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
