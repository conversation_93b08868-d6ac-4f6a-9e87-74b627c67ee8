import 'reflect-metadata';

import ':env';

import { autoInjectable, container } from 'tsyringe';

import { RestaurantModel } from '@malou-io/package-models';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@autoInjectable()
class InitRestaurantDefaultSpecialHoursEndDateTask {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute() {
        console.log('InitRestaurantDefaultSpecialHoursEndDateTask started');

        const restaurants = await this._restaurantsRepository.find({
            filter: { specialHours: { $exists: true, $ne: [] }, 'specialHours.endDate': null },
            options: { lean: true },
        });

        let bulkOperations: any[] = [];

        for (const restaurant of restaurants) {
            bulkOperations.push({
                updateOne: {
                    filter: { _id: restaurant._id },
                    update: {
                        $set: {
                            specialHours: restaurant.specialHours.map((hour) => ({
                                ...hour,
                                endDate: hour.startDate,
                            })),
                        },
                    },
                },
            });
        }

        await RestaurantModel.bulkWrite(bulkOperations);

        console.log('InitRestaurantDefaultSpecialHoursEndDateTask finished');
        console.log('Number of restaurants updated:', restaurants.length);
    }
}

const task = container.resolve(InitRestaurantDefaultSpecialHoursEndDateTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
