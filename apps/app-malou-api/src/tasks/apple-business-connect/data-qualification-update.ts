import 'reflect-metadata';

import ':env';

import { createObjectCsvWriter as createCsvWriter } from 'csv-writer';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { container, singleton } from 'tsyringe';

import { isNotNil, waitFor } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { AppleBusinessConnectLocationDetails } from ':modules/platforms/platforms/apple-business-connect/apple-business-connect.interfaces';
import { AppleBusinessConnectMapperService } from ':modules/platforms/platforms/apple-business-connect/apple-business-connect.mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';
import AppleBusinessConnectApiProvider from ':providers/apple-business-connect/apple-business-connect.provider';
import { AppleBusinessConnectPagination } from ':providers/apple-business-connect/apple-business-connect.provider.interfaces';
import { GoogleSheetsService } from ':services/google-sheets/google-sheets.service';

@singleton()
class AbcDataQualificationTaskUpdate {
    accessToken: string | undefined;
    executionStartedDate = new Date();
    mappingLocationRestaurant = {};
    failingRestaurantIds = [];

    constructor(
        private readonly _appleBusinessApiProvider: AppleBusinessConnectApiProvider,
        private readonly _appleBusinessConnectMapperService: AppleBusinessConnectMapperService,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _googleSheetsService: GoogleSheetsService
    ) {}

    async execute(): Promise<void> {
        console.log('***** Starting the execution of the ABC Data Qualification task *****');
        this.executionStartedDate = new Date();

        const finalLocations = await this._getExistingLocations();

        // const googleDoc = await this._googleSheetsService.loadGoogleSheet('1-vZejADey-h6hMIZ-PShiwsbiPxQgsneUZVzVnAcw4M');
        // const googleSheet = googleDoc.sheetsByTitle['ABC locations final'];
        // const rows = await googleSheet.getRows();

        // const newLocationsMapping = [];
        // let rqs = 0;
        // for (const location of abcLocations) {
        //     const { etag, id: appleId } = await this._appleBusinessApiProvider.createLocation({
        //         data: {
        //             locationDetails: location.locationDetails as Partial<AppleBusinessConnectLocationDetails>,
        //         },
        //     });

        //     const partnersLocationId = location.locationDetails.partnersLocationId;
        //     const dbId = rows.find((row) => row['malouId'] === partnersLocationId)?.malouDbId;
        //     newLocationsMapping.push({
        //         appleId,
        //         etag,
        //         partnersLocationId,
        //         dbId,
        //     });
        //     rqs++;

        //     // To avoid rate limit (15RQS)
        //     if (rqs === 10) {
        //         await waitFor(1000);
        //         rqs = 0;
        //     }
        // }

        // for (const restaurantId of existingLocationRestaurantIds) {
        //     const restaurant = (await this._restaurantsRepository.getRestaurantByIdPopulated(restaurantId)) as any;
        //     if (!restaurant) {
        //         logger.warn('[ABC_CREATE_LOCATION_ERROR] Restaurant not found', { restaurantId });
        //         return;
        //     }

        //     const locationDetails = await this._appleBusinessConnectMapperService.execute(restaurant);
        //     const requiredFields = ['displayNames', 'mainAddress', 'displayPoint', 'locationStatus', 'categories', 'partnersLocationId'];
        //     const missingRequiredFields = requiredFields.filter((field) => isNil(locationDetails[field]));
        //     console.log(missingRequiredFields);
        // }

        // const attributesIds = await AttributeModel.distinct('attributeId');
        // const withoutMatch = [];
        // attributesIds.forEach((attributeId) => {
        //     if (!appleBusinessConnectAttributesMapping[attributeId]) {
        //         withoutMatch.push(attributeId);
        //     }
        // });

        // const ids = await this._getExistingLocationIds();

        // Create a new googlesheet with the mapping
        // const googleDoc = await this._googleSheetsService.loadGoogleSheet('1-vZejADey-h6hMIZ-PShiwsbiPxQgsneUZVzVnAcw4M');
        // const googleSheet = googleDoc.sheetsByTitle['ABC locations data'];
        // const rows = await googleSheet.getRows();
        // const googleSheetUpdated = googleDoc.sheetsByTitle['ABC locations data updated'];
        // const rowsUpdated = await googleSheetUpdated.getRows();
        // const googleSheetFinal = googleDoc.sheetsByTitle['ABC locations final'];
        // const rowsFinal = await googleSheetFinal.getRows();

        // await this._writeCsvMapping(googleDoc);

        // const firstbatchIds = rowsUpdated.map((row) => row['malouId_v2'].trim());
        // const lastBatchIds = rowsFinal.map((row) => row['malouDbId'].trim());
        // const unmatchedIds = firstbatchIds.filter((id) => !lastBatchIds.includes(id));

        // await this._updateFinalLocations(googleDoc);

        // DELETE LOCATIONS FROM 1ST BATCHES
        // for (const chunkedRows of chunk(rowsUpdated, 20)) {
        //     await Promise.all(
        //         chunkedRows.map(async (row) => {
        //             try {
        //                 const { etag } = await this._appleBusinessApiProvider.getLocationById({
        //                     locationId: row['appleId'].trim(),
        //                 });

        //                 if (etag !== row['etag'].trim()) {
        //                     logger.warn('[ABC_UPDATE_LOCATION_ERROR] Location weird state', { locationId: row['appleId'] });
        //                 }

        //                 // row.etag = etag;
        //                 // await row.save();

        //                 await this._appleBusinessApiProvider.deleteLocation({ locationId: row['appleId'].trim(), etag });
        //             } catch (err) {
        //                 logger.error('[ABC_UPDATE_LOCATION_ERROR] Error deleting location', { locationId: row['appleId'], error: err });
        //             }
        //         })
        //     );
        // }

        // DELETE FAKE LOCATIONS
        // const fakeAccounts = rowsFinal.filter((row) => row['fake'] === 'TRUE');
        // for (const chunkedRows of chunk(fakeAccounts, 20)) {
        //     await Promise.all(
        //         chunkedRows.map(async (row) => {
        //             const locationId = row['appleId'].trim();
        //             try {
        //                 const { etag } = await this._appleBusinessApiProvider.getLocationById({
        //                     locationId,
        //                 });

        //                 if (etag !== row['etag'].trim()) {
        //                     logger.warn('[ABC_UPDATE_LOCATION_ERROR] Location weird state', { locationId });
        //                 }

        //                 // row.etag = etag;
        //                 // await row.save();

        //                 await this._appleBusinessApiProvider.deleteLocation({ locationId, etag });
        //                 logger.info('[ABC_UPDATE_LOCATION_ERROR] Location deleted', { locationId });
        //             } catch (err) {
        //                 logger.error('[ABC_UPDATE_LOCATION_ERROR] Error deleting location', { locationId, error: err });
        //             }
        //         })
        //     );
        // }

        // await this._createLocations(googleDoc);

        // let locations = undefined;
        // let pagination = undefined;
        // let abcResponse = await this._appleBusinessApiProvider.getExistingLocations();
        // locations = abcResponse.data;
        // pagination = abcResponse.pagination;

        // const locationsToSet = locations.filter((location) => {
        //     const row = rows.find((row) => row['appleId'] === location.id);
        //     return row && !row['malouId'];
        // });

        // for (const locationsChunk of chunk(locationsToSet, 2)) {
        //     await Promise.all(
        //         locationsChunk.map(async (location: any) => {
        //             const row = rows.find((row) => row['appleId'] === location.id);
        //             if (!row) {
        //                 return logger.warn('[ABC_UPDATE_LOCATION_ERROR] Row not found', { locationId: location.id });
        //             }

        //             row['malouId'] = location.locationDetails.partnersLocationId;

        //             await row.save();
        //         })
        //     );
        //     await waitFor(1000);
        // }

        // for (const locationsChunk of chunk(locations, 20)) {
        //     await Promise.all(
        //         locationsChunk.map((location: any) => {
        //             const row = rows.find((row) => row['appleId'] === location.id);
        //             const rowUpdated = rowsUpdated.find((row) => row['appleId'] === location.id);

        //             if (!row || !rowUpdated) {
        //                 return logger.warn('[ABC_UPDATE_LOCATION_ERROR] Row not found', { locationId: location.id });
        //             }

        //             return this._updateLocationV2(location, row, rowUpdated);
        //         })
        //     );

        //     await waitFor(1000);
        // }

        // Write apple business locations data to google sheet
        // await this._writeDataToGoogleSheet(googleSheet, locations);

        // while (pagination.next) {
        //     abcResponse = await this._appleBusinessApiProvider.getExistingLocations(pagination.next);
        //     locations = abcResponse.data;
        //     pagination = abcResponse.pagination;

        //     for (const locationsChunk of chunk(locations, 20)) {
        //         await Promise.all(
        //             locationsChunk.map((location: any) => {
        //                 const row = rows.find((row) => row['appleId'] === location.id);
        //                 const rowUpdated = rowsUpdated.find((row) => row['appleId'] === location.id);

        //                 if (!row || !rowUpdated) {
        //                     return logger.warn('[ABC_UPDATE_LOCATION_ERROR] Row not found', { locationId: location.id });
        //                 }

        //                 return this._updateLocationV2(location, row, rowUpdated);
        //             })
        //         );
        //         await waitFor(1000);
        //     }

        //     // locations = locations.filter((location) => {
        //     //     const row = rows.find((row) => row['appleId'] === location.id);
        //     //     return row && !row['categories_v3'];
        //     // });

        //     // for (const locationChunk of chunk(locations, 1)) {
        //     //     await Promise.all(
        //     //         locationChunk.map(async (location: any) => {
        //     //             const row = rows.find((row) => row['appleId'] === location.id);
        //     //             if (!row) {
        //     //                 return logger.warn('[ABC_UPDATE_LOCATION_ERROR] Row not found', { locationId: location.id });
        //     //             }

        //     //             // if (row['malouId_v2'] === 'to define') {
        //     //             //     return;
        //     //             // }

        //     //             const restaurant = await this._restaurantsRepository.findOne({
        //     //                 filter: {
        //     //                     _id: toDbId(row['malouId_v3']),
        //     //                 },
        //     //                 options: {
        //     //                     lean: true,
        //     //                     populate: [{ path: 'category' }, { path: 'categoryList' }],
        //     //                 },
        //     //             });
        //     //             const categories = this._mapRestaurantCategoriesToAppleBusinessConnectCategories(restaurant);

        //     //             // // const restaurants = await this._restaurantsRepository.find({
        //     //             // //     filter: {
        //     //             // //         name: { $regex: `^${location.locationDetails.displayNames[0].name}$`, $options: 'i' },
        //     //             // //         'address.locality': location.locationDetails.mainAddress.structuredAddress.locality,
        //     //             // //         'address.postalCode': location.locationDetails.mainAddress.structuredAddress.postCode,
        //     //             // //         'address.formattedAddress': location.locationDetails.mainAddress.structuredAddress.fullThoroughfare,
        //     //             // //     },
        //     //             // //     options: {
        //     //             // //         lean: true,
        //     //             // //         populate: [{ path: 'category' }],
        //     //             // //     },
        //     //             // // });

        //     //             // // if (!restaurants || restaurants.length === 0 || restaurants.length > 1) {
        //     //             // //     return logger.warn('[ABC_UPDATE_LOCATION_ERROR] Restaurant error', {
        //     //             // //         restaurantName: location.locationDetails.displayNames[0].name,
        //     //             // //         restaurants: restaurants?.map((restaurant) => restaurant._id),
        //     //             // //     });
        //     //             // // }

        //     //             // // const [restaurant] = restaurants;

        //     //             // if (row['malouId_v2'] && row['malouId_v2'] === restaurant._id.toString()) {
        //     //             //     return logger.warn('[ABC_UPDATE_LOCATION_ERROR] Restaurant id match', {
        //     //             //         restaurantName: location.locationDetails.displayNames[0].name,
        //     //             //         restaurantId: restaurant._id,
        //     //             //         rowMalouId: row['malouId_v2'],
        //     //             //     });
        //     //             // }

        //     //             // if (row['malouId_v2']) {
        //     //             //     logger.warn('[ABC_UPDATE_LOCATION_ERROR] Restaurant id mismatch', {
        //     //             //         restaurantName: location.locationDetails.displayNames[0].name,
        //     //             //         restaurantId: restaurant._id,
        //     //             //         rowMalouId: row['malouId_v2'],
        //     //             //     });
        //     //             // }

        //     //             // const appleBusinessConnectMainCategory = appleBusinessConnectCategoriesMapping[restaurant.category?.categoryId];

        //     //             // row['malouId'] = restaurant._id;
        //     //             // row['malouId_v3'] = restaurant._id;
        //     //             row['categories_v3'] = categories.join(', ');
        //     //             await row.save();
        //     //             logger.info('ROW UPDATED', { locationId: location.id });
        //     //         })
        //     //     );
        //     //     await waitFor(1000);
        //     // }

        //     // for (const locationsChunk of chunk(locations, 20)) {
        //     //     // await Promise.all(locationsChunk.map((location) => this._deleteLocation(location)));
        //     //     await Promise.all(locationsChunk.map((location) => this._updateLocation(location)));
        //     //     await waitFor(1000);
        //     // }

        //     await waitFor(1000);
        // }

        // await this._createLocations();
        // console.log(this.mappingLocationRestaurant);
    }

    private async _getExistingLocations() {
        const finalLocations: AppleBusinessConnectLocationDetails[] = [];
        let pagination: AppleBusinessConnectPagination | undefined = undefined;
        let abcResponse = await this._appleBusinessApiProvider.getLocations();
        finalLocations.push(...abcResponse.data);
        pagination = abcResponse.pagination;

        while (pagination.next) {
            abcResponse = await this._appleBusinessApiProvider.getLocations({ next: pagination.next });
            finalLocations.push(...abcResponse.data);
            pagination = abcResponse.pagination;

            await waitFor(1000);
        }

        return finalLocations;
    }

    private async _writeCsvMapping(googleDoc: GoogleSpreadsheet) {
        const googleSheetUpdated = googleDoc.sheetsByTitle['ABC locations data updated'];
        const rowsUpdated = (await googleSheetUpdated.getRows()).filter((row) => row['Batch'] === '1');
        const googleSheetFinal = googleDoc.sheetsByTitle['ABC locations final'];
        const rowsFinal = await googleSheetFinal.getRows();

        const csvWriter = createCsvWriter({
            path: `${__dirname}/mapping.csv`,
            header: [
                { id: 'formerId', title: 'Former ID' },
                { id: 'formerPartnersLocationId', title: 'Former Partner Location ID' },
                { id: 'id', title: 'New ID' },
                { id: 'partnersLocationId', title: 'New Partner Location ID' },
            ],
        });

        const records = rowsUpdated
            .map((row) => {
                const newRow = rowsFinal.find((r) => r['malouDbId'] === row['malouId_v2']);

                if (!newRow) {
                    logger.error('Row not found', { malouId: row['malouId_v2'] });
                    return undefined;
                }

                return {
                    formerId: row['appleId'].trim(),
                    formerPartnersLocationId: row['malouId'].trim(),
                    id: newRow['appleId'].trim(),
                    partnersLocationId: newRow['malouId'].trim(),
                };
            })
            .filter(isNotNil);

        await csvWriter.writeRecords(records); // returns a promise
        console.log('...Done');
    }

    // private _getFirstBatchExistingLocationIds = async (): Promise<Record<string, string>> => {
    //     const googleDoc = await this._googleSheetsService.loadGoogleSheet('1-vZejADey-h6hMIZ-PShiwsbiPxQgsneUZVzVnAcw4M');
    //     const googleSheet = googleDoc.sheetsByTitle['ABC locations data updated'];
    //     const rows = await googleSheet.getRows();

    //     let locations = undefined;
    //     let pagination = undefined;
    //     let abcResponse = await this._appleBusinessApiProvider.getLocations();
    //     locations = abcResponse.data;
    //     pagination = abcResponse.pagination;
    //     const ids = {};

    //     for (const location of locations) {
    //         const row = rows.find((row) => row['appleId'] === location.id);
    //         if (!row) {
    //             continue;
    //         }

    //         ids[location.id] = row['malouId_v2'];
    //     }

    //     while (pagination.next) {
    //         abcResponse = await this._appleBusinessApiProvider.getLocations({ next: pagination.next });
    //         locations = abcResponse.data;
    //         pagination = abcResponse.pagination;

    //         for (const location of locations) {
    //             const row = rows.find((row) => row['appleId'] === location.id);
    //             if (!row) {
    //                 continue;
    //             }

    //             ids[location.id] = row['malouId_v2'];
    //         }
    //     }

    //     return ids;
    // };

    // private _createLocations = async (googleSheet: GoogleSpreadsheet) => {
    //     let restaurants = await this._restaurantsRepository.find({
    //         filter: {
    //             category: { $ne: null, $exists: true },
    //             name: { $exists: true },
    //             address: { $exists: true },
    //             latlng: { $exists: true },
    //             active: true,
    //             isClosedTemporarily: false,
    //             // categoryList: { $ne: [] },
    //             // regularHours: { $exists: true, $ne: [] },
    //             // descriptions: { $exists: true, $ne: [] },
    //             // website: { $exists: true },
    //             // phone: { $exists: true },
    //             // // Optional (if more than 500 restaurants matching this query, add this to get locations with more data)
    //             // otherHours: { $exists: true, $ne: [] },
    //             // specialHours: { $exists: true, $ne: [] },
    //         },
    //         projection: {
    //             _id: 1,
    //         },
    //         options: {
    //             lean: true,
    //         },
    //     });
    //     // restaurants = restaurants.filter(({ _id }) => !Object.values(existingLocations).includes(_id.toString()));

    //     for (const restaurant of restaurants) {
    //         await this._createLocation(restaurant._id.toString(), googleSheet);
    //         await waitFor(250);
    //     }
    // };

    private _writeDataToGoogleSheet = async (googleSheet, appleBusinessLocationData) => {
        await googleSheet.addRows(
            appleBusinessLocationData.map((location) => ({
                appleId: location.id,
                companyId: location.companyId,
                etag: location.etag,
                malouId: location.locationDetails.partnersLocationId,
                name: location.locationDetails.displayNames[0]?.name,
                categories: location.locationDetails.categories.join(', '),
                website: location.locationDetails.urls?.find(({ type }) => type === 'HOMEPAGE')?.url,
                Monday: JSON.stringify(location.locationDetails.openingHoursByDay?.find(({ day }) => day === 'MONDAY')?.times[0]),
                Tuesday: JSON.stringify(location.locationDetails.openingHoursByDay?.find(({ day }) => day === 'TUESDAY')?.times[0]),
                Wednesday: JSON.stringify(location.locationDetails.openingHoursByDay?.find(({ day }) => day === 'WEDNESDAY')?.times[0]),
                Thursday: JSON.stringify(location.locationDetails.openingHoursByDay?.find(({ day }) => day === 'THURSDAY')?.times[0]),
                Friday: JSON.stringify(location.locationDetails.openingHoursByDay?.find(({ day }) => day === 'FRIDAY')?.times[0]),
                Saturday: JSON.stringify(location.locationDetails.openingHoursByDay?.find(({ day }) => day === 'SATURDAY')?.times[0]),
                Sunday: JSON.stringify(location.locationDetails.openingHoursByDay?.find(({ day }) => day === 'SUNDAY')?.times[0]),
            }))
        );
    };

    // private _createLocation = async (restaurantId: string, googleDoc: GoogleSpreadsheet) => {
    //     try {
    //         const restaurant = (await this._restaurantsRepository.getRestaurantByIdPopulated(restaurantId)) as any;
    //         if (!restaurant) {
    //             logger.warn('[ABC_CREATE_LOCATION_ERROR] Restaurant not found', { restaurantId });
    //             return;
    //         }

    //         const locationDetails = await this._appleBusinessConnectMapperService.execute(restaurant);
    //         const requiredFields = ['displayNames', 'mainAddress', 'displayPoint', 'locationStatus', 'categories', 'partnersLocationId'];
    //         if (!requiredFields.every((field) => isNotNil(locationDetails[field]))) {
    //             const missingRequiredFields = requiredFields.filter((field) => isNil(locationDetails[field]));
    //             logger.warn('[ABC_CREATE_LOCATION_ERROR] Missing required fields', { restaurantId, missingRequiredFields });
    //             return;
    //         }

    //         const googleSheetManualUpdate = googleDoc.sheetsByTitle['ABC locations data updated'];
    //         const rowsUpdated = await googleSheetManualUpdate.getRows();
    //         const restaurantInRow = rowsUpdated.find((row) => row['malouId_v2'].trim() === restaurantId.trim());

    //         const data = {
    //             locationDetails: {
    //                 ...pick(locationDetails, [
    //                     'partnersLocationId',
    //                     'displayNames',
    //                     'mainAddress',
    //                     'displayPoint',
    //                     'locationStatus',
    //                     'categories',
    //                     'phoneNumbers',
    //                     'urls',
    //                     'internalNicknames',
    //                     'locationDescriptions',
    //                     'paymentMethods',
    //                 ]),
    //                 ...(restaurantInRow?.['name'] && {
    //                     displayNames: [
    //                         { name: restaurantInRow['name'], locale: this._getLocaleFromRestaurantCountry(restaurant), primary: true },
    //                     ],
    //                 }),
    //                 ...(restaurantInRow?.['website_updated'] && {
    //                     urls: [{ url: restaurantInRow['website_updated'], type: AppleBusinessConnectLocationUrlType.HOMEPAGE }],
    //                 }),
    //             },
    //         };

    //         const { companyId, etag, id: appleId } = await this._appleBusinessApiProvider.createLocation({ data });
    //         const googleSheet = googleDoc.sheetsByTitle['ABC locations final'];
    //         await googleSheet.addRow({
    //             appleId,
    //             companyId,
    //             etag,
    //             malouId: data.locationDetails.partnersLocationId,
    //             malouDbId: restaurantId,
    //             app_name: restaurant?.name,
    //             name: data.locationDetails.displayNames?.[0]?.name,
    //             categories: data.locationDetails.categories.join(', '),
    //             website: restaurant?.website,
    //             website_updated: data.locationDetails.urls?.find(({ type }) => type === 'HOMEPAGE')?.url,
    //             ...(isNil(restaurantInRow) && {
    //                 no_match: 'TRUE',
    //             }),
    //             from_first_batch: 'TRUE',
    //             ...(!restaurant.active && {
    //                 inactive: 'TRUE',
    //             }),
    //         });

    //         logger.info('LOCATION CREATED', { appleId, companyId, restaurantId });
    //     } catch (error) {
    //         logger.warn('[ABC_CREATE_LOCATION_ERROR]', { error: error.response?.data, restaurantId });
    //         this.failingRestaurantIds.push(restaurantId);
    //     }
    // };

    // private async _updateFinalLocations(googleDoc: GoogleSpreadsheet) {
    //     const googleSheetFinal = googleDoc.sheetsByTitle['ABC locations final'];
    //     const rowsFinal = await googleSheetFinal.getRows();
    //     const rowsToUpdate = rowsFinal.filter((row) => row['no_match'] === 'TRUE');

    //     for (const chunkedRows of chunk(rowsToUpdate, 20)) {
    //         await Promise.all(chunkedRows.map((row) => this._updateFinalLocation(row)));
    //     }
    // }

    // private async _updateFinalLocation(row) {
    //     const restaurantId = row['malouDbId'].trim();
    //     try {
    //         const restaurant = (await this._restaurantsRepository.getRestaurantByIdPopulated(restaurantId)) as any;
    //         if (!restaurant) {
    //             logger.warn('[ABC_CREATE_LOCATION_ERROR] Restaurant not found', { restaurantId });
    //             return;
    //         }

    //         const locationDetails = await this._appleBusinessConnectMapperService.execute(restaurant);
    //         const requiredFields = ['displayNames', 'mainAddress', 'displayPoint', 'locationStatus', 'categories', 'partnersLocationId'];
    //         if (!requiredFields.every((field) => isNotNil(locationDetails[field]))) {
    //             const missingRequiredFields = requiredFields.filter((field) => isNil(locationDetails[field]));
    //             logger.warn('[ABC_CREATE_LOCATION_ERROR] Missing required fields', { restaurantId, missingRequiredFields });
    //             return;
    //         }

    //         const data = {
    //             locationDetails: {
    //                 ...pick(locationDetails, [
    //                     'partnersLocationId',
    //                     'displayNames',
    //                     'mainAddress',
    //                     'displayPoint',
    //                     'locationStatus',
    //                     'categories',
    //                     'phoneNumbers',
    //                     'urls',
    //                     'internalNicknames',
    //                     'locationDescriptions',
    //                     'paymentMethods',
    //                 ]),
    //                 ...(row?.['malouId'] && {
    //                     partnersLocationId: row['malouId'],
    //                 }),
    //                 ...(row?.['name'] && {
    //                     displayNames: [{ name: row['name'], locale: this._getLocaleFromRestaurantCountry(restaurant), primary: true }],
    //                 }),
    //                 ...(row?.['website_updated'] && {
    //                     urls: [{ url: row['website_updated'], type: AppleBusinessConnectLocationUrlType.HOMEPAGE }],
    //                 }),
    //             },
    //         };

    //         await this._appleBusinessApiProvider.updateLocation({
    //             data: {
    //                 id: row['appleId'],
    //                 locationDetails: data.locationDetails,
    //             },
    //             locationId: row['appleId'],
    //             etag: row['etag'],
    //         });

    //         logger.info('LOCATION UPDATED', { appleId: row['appleId'], restaurantId });
    //     } catch (error) {
    //         logger.warn('[ABC_UPDATE_LOCATION_ERROR]', { error: error.response?.data, restaurantId });
    //     }
    // }

    private _deleteLocation = async (location) => {
        await this._appleBusinessApiProvider.deleteLocation({ locationId: location.id, etag: location.etag });
        logger.info('LOCATION DELETED', { locationId: location.id });
    };

    // private _updateLocationV2 = async (location, row, rowUpdated) => {
    //     const restaurantId = rowUpdated['malouId_v2'] ?? row['malouId'];
    //     const restaurant = (await this._restaurantsRepository.getRestaurantByIdPopulated(restaurantId)) as any;
    //     if (!restaurant) {
    //         logger.warn('[ABC_CREATE_LOCATION_ERROR] Restaurant not found', { restaurantId });
    //         return;
    //     }

    //     const locationDetails = await this._appleBusinessConnectMapperService.execute(restaurant);

    //     let openingHoursByDay = undefined;
    //     if (rowUpdated['Inactive'] === 'TRUE' || rowUpdated['Ajout manuel'] === 'TRUE') {
    //         // Get from google sheet
    //         openingHoursByDay = [];

    //         if (rowUpdated['Monday']) {
    //             openingHoursByDay.push({
    //                 day: 'MONDAY',
    //                 times: JSON.parse(rowUpdated['Monday']),
    //             });
    //         }

    //         if (rowUpdated['Tuesday']) {
    //             openingHoursByDay.push({
    //                 day: 'TUESDAY',
    //                 times: JSON.parse(rowUpdated['Tuesday']),
    //             });
    //         }

    //         if (rowUpdated['Wednesday']) {
    //             openingHoursByDay.push({
    //                 day: 'WEDNESDAY',
    //                 times: JSON.parse(rowUpdated['Wednesday']),
    //             });
    //         }

    //         if (rowUpdated['Thursday']) {
    //             openingHoursByDay.push({
    //                 day: 'THURSDAY',
    //                 times: JSON.parse(rowUpdated['Thursday']),
    //             });
    //         }

    //         if (rowUpdated['Friday']) {
    //             openingHoursByDay.push({
    //                 day: 'FRIDAY',
    //                 times: JSON.parse(rowUpdated['Friday']),
    //             });
    //         }

    //         if (rowUpdated['Saturday']) {
    //             openingHoursByDay.push({
    //                 day: 'SATURDAY',
    //                 times: JSON.parse(rowUpdated['Saturday']),
    //             });
    //         }

    //         if (rowUpdated['Sunday']) {
    //             openingHoursByDay.push({
    //                 day: 'SUNDAY',
    //                 times: JSON.parse(rowUpdated['Sunday']),
    //             });
    //         }
    //     } else {
    //         openingHoursByDay = locationDetails.openingHoursByDay; // Get updated opening hours
    //     }

    //     const data = {
    //         id: location.id,
    //         locationDetails: {
    //             ...location.locationDetails,
    //             categories: locationDetails.categories,
    //             ...(rowUpdated['name'] && {
    //                 displayNames: [{ name: rowUpdated['name'], locale: this._getLocaleFromRestaurantCountry(restaurant), primary: true }],
    //             }),
    //             ...this._getLocationDetailsAddress(restaurant),
    //             ...(rowUpdated['website'] && {
    //                 urls: [{ url: rowUpdated['website'], type: AppleBusinessConnectLocationUrlType.HOMEPAGE }],
    //             }),
    //             openingHoursByDay: undefined,
    //         },
    //     };

    //     await this._appleBusinessApiProvider.updateLocation({
    //         locationId: location.id,
    //         etag: location.etag,
    //         data,
    //     });
    // };

    // private _updateLocation = async (location) => {
    //     const restaurantId = location.locationDetails.partnersLocationId;

    //     const data = {
    //         id: location.id,
    //         locationDetails: {
    //             ...location.locationDetails,
    //             // TODO, update openingHoursByDay, categories, websites, names (add upperCase first letter) and add coordinates
    //         },
    //     };

    //     try {
    //         // const restaurant = (await this._restaurantsRepository.getRestaurantByIdPopulated(toDbId(partnersLocationId))) as any;

    //         const restaurant = await this._restaurantsRepository.findOne({
    //             filter: {
    //                 _id: toDbId(restaurantId),
    //                 category: { $ne: null },
    //                 categoryList: { $ne: null },
    //             },
    //             options: {
    //                 lean: true,
    //                 populate: [{ path: 'category' }, { path: 'categoryList' }],
    //             },
    //         });

    //         if (!restaurant) {
    //             throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: `Restaurant with id ${restaurantId} not found` });
    //         }

    //         // const categories = this._mapRestaurantCategoriesToAppleBusinessConnectCategories(restaurant);

    //         const data = {
    //             id: location.id,
    //             locationDetails: {
    //                 ...location.locationDetails,
    //                 // TODO, update openingHoursByDay, categories, websites, names (add upperCase first letter) and add coordinates
    //             },
    //         };

    //         // const res = await this._appleBusinessApiProvider.updateLocation({ locationId: location.id, etag: location.etag, data });

    //         // const accessTokenResponse = await this._getAccessToken();
    //         // const accessToken = accessTokenResponse.data.access_token;

    //         // const body = await this._appleBusinessConnectMapperService.mapRestaurantToCreateAbcLocationRequest(restaurant);
    //         // // eslint-disable-next-line max-len
    //         // const url = `${Config.platforms.appleBusinessConnect.api.url}/api/${Config.platforms.appleBusinessConnect.api.version}/companies/${Config.platforms.appleBusinessConnect.api.companyId}/locations`;
    //         // const options: AxiosRequestConfig = {
    //         //     headers: {
    //         //         'content-type': 'application/json',
    //         //         authorization: `Bearer ${accessToken}`,
    //         //     },
    //         // };

    //         // const apiResponse = await axios.post(url, body, options);
    //         // logger.info('[ABC_CREATE_LOCATION] New ABC location', { location: apiResponse.data });
    //         // TODO mapper ? Ou juste on dit OK ?
    //         // logger.info('LOCATION UPDATED', { locationId: location.id, categories });
    //     } catch (error) {
    //         logger.warn('[ABC_CREATE_LOCATION_ERROR]', { error, restaurantId, locationId: location.id });
    //     }
    // };

    // private _getLocationDetailsAddress(restaurant: any): AppleBusinessConnectLocationPoint | undefined {
    //     if (!restaurant.latlng || !restaurant.address) {
    //         logger.warn('[Apple Business Connect] No address or coordinates found for restaurant', { restaurantId: restaurant._id });
    //         return undefined;
    //     }

    //     const hasLatitudeAndLongitude = isNotNil(restaurant.latlng.lat) && isNotNil(restaurant.latlng.lng);

    //     return {
    //         ...(restaurant.address && {
    //             mainAddress: {
    //                 // TODO "VALIDATION__StructuredAddressMustIncludeComponent" field: locality must be present / Wild & The Moon – Al Serkal Avenue
    //                 structuredAddress: {
    //                     locality: restaurant.address.locality,
    //                     countryCode: restaurant.address.regionCode.toUpperCase(), // must be uppercase, ISO 3166 Country Codes: https://www.iso.org/iso-3166-country-codes.html https://www.iso.org/obp/ui/#search
    //                     administrativeArea: restaurant.address.administrativeArea ?? undefined, // required when defined by ISO for the Country
    //                     postCode: restaurant.address.postalCode,
    //                     fullThoroughfare: restaurant.address.formattedAddress,
    //                 },
    //                 locale: this._getLocaleFromRestaurantCountry(restaurant),
    //             },
    //         }),
    //         ...(hasLatitudeAndLongitude && {
    //             displayPoint: {
    //                 coordinates: {
    //                     latitude: restaurant.latlng.lat.toString(),
    //                     longitude: restaurant.latlng.lng.toString(),
    //                 },
    //                 source: AppleBusinessConnectLocationPointSource.CALCULATED,
    //             },
    //         }),
    //     };
    // }

    // private _getLocaleFromRestaurantCountry(restaurant: any): string {
    //     const regionCode = restaurant?.address.regionCode;

    //     if (!regionCode) {
    //         logger.warn('[Apple Business Connect] No locale found for restaurant', { restaurantId: restaurant._id });

    //         return 'fr-FR';
    //     }

    //     // https://www.npmjs.com/package/country-locale-map
    //     return clm.getLocaleByAlpha2(regionCode.toUpperCase()).replace('_', '-');
    // }
}

const task = container.resolve(AbcDataQualificationTaskUpdate);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
