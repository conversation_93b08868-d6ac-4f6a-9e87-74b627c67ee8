import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { waitFor } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { newLocationsMapping } from ':tasks/apple-business-connect/mapping';

@singleton()
class UpdateRestaurantsAbcIdsTask {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(): Promise<void> {
        for (const locationMapping of newLocationsMapping) {
            const { appleId, dbId, partnersLocationId } = locationMapping;
            const { matchedCount, modifiedCount } = await this._restaurantsRepository.updateOne({
                filter: { _id: toDbId(dbId) },
                update: {
                    appleBusinessConnect: {
                        locationId: appleId,
                        malouLocationId: partnersLocationId,
                    },
                },
            });
            logger.info(
                `Updated restaurant ${dbId} with ABC locationId ${appleId}, matchedCount: ${matchedCount}, modifiedCount: ${modifiedCount}`
            );
            await waitFor(200);
        }
    }
}

const task = container.resolve(UpdateRestaurantsAbcIdsTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
