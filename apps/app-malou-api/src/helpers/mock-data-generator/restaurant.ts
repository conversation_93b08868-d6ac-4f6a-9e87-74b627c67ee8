import { IRestaurant, newDbId } from '@malou-io/package-models';
import { BusinessCategory } from '@malou-io/package-utils';

export function getMockRestaurant(update: Partial<IRestaurant> = {}): IRestaurant {
    const mock = {
        _id: newDbId(),
        access: [],
        active: true,
        ai: { monthlyCallCount: 0 },
        bookmarkedPosts: [],
        boosterPack: { activated: true },
        bricks: [],
        calendarEvents: [],
        categoryList: [],
        coverChanged: false,
        createdAt: new Date(),
        descriptions: [],
        logoChanged: false,
        name: 'restaurant name',
        organizationId: newDbId(),
        placeId: 'placeId',
        relatedUrls: [],
        specialHours: [],
        type: BusinessCategory.LOCAL_BUSINESS,
        isClosedTemporarily: false,
        uniqueKey: 'unique key',
        updatedAt: new Date(),
    };
    return { ...mock, ...update };
}
