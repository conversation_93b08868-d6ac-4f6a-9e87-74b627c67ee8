import { Ability } from '@casl/ability';
import { NextFunction, Response } from 'express';
import { Session } from 'express-session';
import assert from 'node:assert';
import { container } from 'tsyringe';

import { MalouErrorCode, Role } from '@malou-io/package-utils';

import { defineRulesForUser, defineRulesForUserRestaurant } from ':helpers/casl/app-ability';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { RequestWithUser } from ':helpers/utils.types';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';

const usersRepository = container.resolve(UsersRepository);
const userRestaurantsRepository = container.resolve(UserRestaurantsRepository);

const isAbilitySet = (session): boolean => {
    if (!session?.abilityRules) return false;
    if (!session.abilityRules?.userRestaurants || !session.abilityRules?.user) return false;
    return true;
};

export const userHasAccessToRestaurant = async (req: RequestWithUser, _res: Response, next: NextFunction): Promise<void> => {
    try {
        const { restaurant_id: restaurantId } = req.params;
        assert(req.user, 'User is not defined');

        if (restaurantId === 'undefined') {
            throw new MalouError(MalouErrorCode.ROUTING_ERROR, {
                message: 'This is a routing error that should not happen',
                metadata: { url: req.url },
            });
        }

        if (!restaurantId) return next();
        if (req.user.role === Role.ADMIN) return next();
        const userRestaurant = await userRestaurantsRepository.findOne({
            filter: { userId: req.user._id, restaurantId },
            projection: { restaurantId: 1, caslRole: 1 },
            options: { lean: true },
        });

        if (!userRestaurant) {
            throw new MalouError(MalouErrorCode.FORBIDDEN, {
                message: 'User does not have access to restaurant',
                metadata: {
                    restaurantId,
                    userId: req.user._id,
                },
            });
        }

        return next();
    } catch (err) {
        return next(err);
    }
};

export const casl = function () {
    return [
        userHasAccessToRestaurant,
        async (
            req: RequestWithUser & { session?: Session & { abilityRules?: { userRestaurants?: any; user: any } } },
            _res: Response,
            next: NextFunction
        ) => {
            try {
                assert(req.user, 'User is not defined');
                const { shouldExpireAbilitySession } = req.user;
                const session = req.session;
                assert(session, 'Session is not defined');
                if (!isAbilitySet(session) || shouldExpireAbilitySession) {
                    session.abilityRules = {
                        userRestaurants: null,
                        user: null,
                    };
                }
                assert(session.abilityRules, 'Ability rules are not defined');
                if (shouldExpireAbilitySession) {
                    session.destroy((err) => {
                        if (err) {
                            logger.error('[SESSION_DESTROY_FAILED]', err);
                        }
                    });
                    await usersRepository.findOneAndUpdate({
                        filter: { _id: req.user._id },
                        update: { shouldExpireAbilitySession: false },
                    });
                }
                if (!session.abilityRules.user) {
                    const user = await usersRepository.findOneOrFail({
                        filter: { _id: req.user._id },
                        options: {
                            populate: [
                                {
                                    path: 'organizations',
                                },
                            ],
                            lean: true,
                        },
                    });
                    const rules = await defineRulesForUser({
                        role: user.role,
                        caslRole: user.caslRole,
                        organizations: user.organizations.map((o) => ({ _id: o._id.toString() })),
                        organizationIds: user.organizations.map((o) => o._id.toString()),
                    });
                    session.abilityRules.user = rules;
                }
                if (!session.abilityRules.userRestaurants) {
                    const userWithHisRestaurants = await usersRepository.findOne({
                        filter: { _id: req.user._id },
                        options: {
                            populate: [
                                {
                                    path: 'restaurants',
                                    populate: [
                                        {
                                            path: 'restaurant',
                                            select: 'organizationId active',
                                        },
                                    ],
                                },
                            ],
                            lean: true,
                        },
                    });
                    const rules = (
                        await Promise.all(
                            (
                                userWithHisRestaurants?.restaurants
                                    ?.filter((r) => r.restaurant?.active)
                                    ?.map((ur) => ({ ...ur, user: { role: userWithHisRestaurants.role } })) ?? []
                            ) // we attach the user role to each restaurant object
                                .map((ur) =>
                                    defineRulesForUserRestaurant({
                                        user: { role: ur.user.role },
                                        caslRole: ur.caslRole,
                                        restaurantId: ur.restaurantId.toString(),
                                        restaurant: { organizationId: ur.restaurant?.organizationId.toString() },
                                    })
                                )
                        )
                    ).flat();
                    session.abilityRules.userRestaurants = rules;
                }
                Object.assign(req, { userAbility: new Ability(session.abilityRules.user) });
                Object.assign(req, { userRestaurantsAbility: new Ability(session.abilityRules.userRestaurants) });
                return next();
            } catch (err) {
                return next(err);
            }
        },
    ];
};
