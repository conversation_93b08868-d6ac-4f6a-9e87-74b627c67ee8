import { UUID } from 'crypto';

import {
    AppleBusinessConnectLocationDetails,
    AppleBusinessConnectLocationState,
    AppleBusinessConnectMediaState,
    AppleBusinessConnectValidationReportPayload,
} from ':modules/platforms/platforms/apple-business-connect/apple-business-connect.interfaces';

export interface AppleBusinessApiCommonHeaders {
    'Content-Type': 'application/json';
    Authorization: string;
    [key: string]: any;
}

export interface AppleBusinessApiCommonResponseBody<T> {
    companyId: string;
    id: string;
    createdDate: Date;
    updatedDate: Date;
    state: T;
}

// TODO be more specific about the details
export interface AppleBusinessApiMetaData {
    message: string;
    code: string;
    details: any[];
}

export interface AppleBusinessApiGetAccessTokenRequestBody {
    client_id: string;
    client_secret: string;
    grant_type: 'client_credentials';
    scope: 'business_connect';
}

export interface AppleBusinessApiGetAccessTokenResponseBody {
    expires_in: number;
    token_type: 'Bearer';
    access_token: string;
}

export interface AppleBusinessApiGetLocationByIdResponseBody {
    companyId: string;
    id: string;
    createdDate: Date;
    updatedDate: Date;
    state: AppleBusinessConnectLocationState;
    etag: UUID;
    placeCardUrl: string; // URL to the location's place card
    locationDetails: AppleBusinessConnectLocationDetails;
    metadata: AppleBusinessApiMetaData[];
}

export interface AppleBusinessConnectPagination {
    cursors?: {
        after: string;
    };
    next?: string;
}

export interface AppleBusinessApiGetLocationsResponseBody {
    data: AppleBusinessConnectLocationDetails[];
    pagination: AppleBusinessConnectPagination;
    total: number;
}

export interface AppleBusinessApiCreateLocationRequestBody {
    locationDetails: Partial<AppleBusinessConnectLocationDetails>;
}

export interface AppleBusinessApiCreateLocationResponseHeaders {
    'content-type': 'application/json';
    etag: UUID;
    location: string; // /api/v2/companies/9467895078742654934/locations/9467895078742654976
    'apple-request-id': UUID;
}

export interface AppleBusinessApiCreateLocationResponseBody extends AppleBusinessApiCommonResponseBody<AppleBusinessConnectLocationState> {
    etag: UUID;
    locationDetails: AppleBusinessConnectLocationDetails;
    // Warning or info messages
    validationReports?: AppleBusinessConnectValidationReportPayload[];
}

export interface AppleBusinessApiUpdateLocationRequestHeaders extends AppleBusinessApiCommonHeaders {
    'if-match': string;
}

export interface AppleBusinessApiUpdateLocationRequestBody {
    id: string;
    locationDetails: AppleBusinessConnectLocationDetails;
}

export interface AppleBusinessApiUpdateLocationResponseHeaders {
    'content-type': 'application/json';
    etag: UUID;
    location: string; // /api/v2/companies/9467895078742654934/locations/9467895078742654976
    'apple-request-id': UUID;
}

export interface AppleBusinessApiUpdateLocationResponseBody extends AppleBusinessApiCommonResponseBody<AppleBusinessConnectLocationState> {
    etag: UUID;
    locationDetails: AppleBusinessConnectLocationDetails;
    // Warning or info messages
    validationReports?: AppleBusinessConnectValidationReportPayload[];
}

export interface AppleBusinessApiMediaUploadResponseHeaders {
    'content-type': 'application/json';
    etag: UUID;
    location: string; // /api/v2/companies/9467895078742654934/images/9467895078742654949/metadata
    'apple-request-id': UUID;
}

export interface AppleBusinessApiMediaUploadResponseBody extends AppleBusinessApiCommonResponseBody<AppleBusinessConnectMediaState> {
    filename: string;
    width: number; // width in pixel
    height: number; // height in pixel
    fileSize: number; // calculated in megabytes
    contentType: string; // image/jpeg
}
