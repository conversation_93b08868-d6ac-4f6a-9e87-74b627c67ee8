import { Router } from 'express';
import { singleton } from 'tsyringe';

import { authorize } from ':plugins/passport';

import { ScansController } from './scans.controller';

@singleton()
export default class ScansRouter {
    constructor(private _scansController: ScansController) {}

    init(router: Router): void {
        router.get('/scans/', authorize(), (req: any, res, next) => this._scansController.handleSearchScan(req, res, next));

        router.post('/scans/insights-for-restaurants', authorize(), (req: any, res, next) =>
            this._scansController.handleGetInsightsForRestaurants(req, res, next)
        );

        router.post('/scans/:restaurant_id/insights-for-restaurant', authorize(), (req: any, res, next) =>
            this._scansController.handleGetInsightsForRestaurant(req, res, next)
        );

        // Reminder: this route is not protected, because it is used when a customer scans a nfc
        router.post('/scans/', (req, res, next) => this._scansController.handleCreateScan(req, res, next));
        router.patch('/scans/:scanId', (req, res, next) => this._scansController.handlePatchScan(req, res, next));
    }
}
