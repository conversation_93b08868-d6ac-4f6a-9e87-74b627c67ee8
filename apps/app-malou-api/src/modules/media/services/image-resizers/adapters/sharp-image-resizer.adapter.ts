import assert from 'node:assert/strict';
import { basename } from 'node:path';
import sharp from 'sharp';
import { autoInjectable } from 'tsyringe';

import { getTypeFromExtension, waitFor } from '@malou-io/package-utils';

import { getMimetypeFromExtension } from ':helpers/utils';

import { CropOptions, FileMetadata, ImageResizerPort, OutputFileInfo, ResizeOptions } from '../image-resizer.port.interface';

sharp.cache(false);
@autoInjectable()
export class SharpImageResizer implements ImageResizerPort {
    sharpInstance: sharp.Sharp = sharp();
    private _outputPath: string = '';

    initialize(filePath: string, outputPath: string): ImageResizerPort {
        this.sharpInstance = sharp(filePath);
        this._outputPath = outputPath;
        return this;
    }

    resize(options: ResizeOptions): ImageResizerPort {
        this.sharpInstance.resize(options);
        return this;
    }

    extract(options: CropOptions): ImageResizerPort {
        assert(options.width || options.width === 0, 'width is required');
        assert(options.height || options.height === 0, 'height is required');
        assert(options.left || options.left === 0, 'left is required');
        assert(options.top || options.top === 0, 'top is required');
        this.sharpInstance.extract({
            height: Math.round(options.height),
            width: Math.round(options.width),
            left: Math.round(options.left),
            top: Math.round(options.top),
        });
        return this;
    }

    rotate(degrees?: number): ImageResizerPort {
        this.sharpInstance.rotate(degrees);
        return this;
    }

    async metadata(): Promise<FileMetadata> {
        const metadata = await this.sharpInstance.metadata();
        await waitFor(500);
        const name = basename(this._outputPath);
        // we don’t use metadata.size because it is always undefined in this case,
        // because Sharp’s input is a file path.
        assert(metadata.format);
        assert(metadata.width);
        assert(metadata.height);
        return {
            type: getTypeFromExtension(metadata.format),
            mimetype: getMimetypeFromExtension(metadata.format ?? ''), // XXX here it is assumed that the name of a Sharp format is a valid file extension. It probably works in the most common scenarios but I’m not sure this is always correct.
            pathWhereFileIsStored: this._outputPath,
            name: name.split('.')[0],
            orientation: metadata.orientation,
            format: metadata.format,
            size: 0,
            width: metadata.width,
            height: metadata.height,
            normalWidth: this._getNormalSize(metadata).width,
            normalHeight: this._getNormalSize(metadata).height,
        };
    }

    toFile(outputPath: string): Promise<OutputFileInfo> {
        this._outputPath = outputPath;
        return this.sharpInstance.toFile(outputPath);
    }

    getOutputPath(): string {
        return this._outputPath;
    }

    _getNormalSize(metadata: sharp.Metadata) {
        const { width, height, orientation } = metadata;
        return (orientation ?? 0) >= 5 ? { width: height, height: width } : { width, height };
    }
}
