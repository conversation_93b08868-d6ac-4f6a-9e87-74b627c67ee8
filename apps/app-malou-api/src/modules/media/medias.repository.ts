import { singleton } from 'tsyringe';
import { DeepPartial } from 'utility-types';

import { EntityRepository, IMedia, IMediaTransformData, MediaModel, newDbId, toDbId } from '@malou-io/package-models';

import { MediaFilters } from ':helpers/filters/media-filters';
import { logger } from ':helpers/logger';
import { Pagination } from ':helpers/pagination';

import { Media, MediaProps } from './entities/media.entity';

interface MediasPaginated<T = IMedia> {
    totalCount: {
        total: number;
    }[];
    data: T[];
}

@singleton()
export class MediasRepository extends EntityRepository<IMedia> {
    constructor() {
        super(MediaModel);
    }

    async findById(id: string): Promise<Media | null> {
        const mediaDocument = await this.findOne({
            filter: { _id: toDbId(id) },
            options: { lean: true },
        });
        if (!mediaDocument) {
            return null;
        }
        return this.toEntity(mediaDocument);
    }

    async findMediasByIds(ids: string[]): Promise<Media[]> {
        const mediaDocuments = await this.find({ filter: { _id: { $in: ids.map(toDbId) } }, options: { lean: true } });
        return mediaDocuments.map(this.toEntity);
    }

    async findDeletedMedia(ids: string[]): Promise<Media | null> {
        const m = await this.findOne({
            filter: { _id: { $in: ids.map(toDbId) }, deletedAt: { $ne: null } },
            options: { lean: true },
        });
        return m ? this.toEntity(m) : null;
    }

    async createMedia(data: Media): Promise<Media> {
        const mediaDocument = await this.create({ data: this.toDocument(data), options: { lean: true } });
        logger.info('[MediasRepository] createMedia', { mediumId: mediaDocument._id });
        return this.toEntity(mediaDocument);
    }

    async createMedias(data: Media[]): Promise<Media[]> {
        const mediaDocuments = await this.createMany({
            data: data.map((media) => this.toDocument(media)),
            options: { lean: true },
        });
        return mediaDocuments.map((media: IMedia) => this.toEntity(media));
    }

    /** Returns null if no media has the given ID */
    async updateMedia(id: string, data: DeepPartial<MediaProps>): Promise<Media | null> {
        const mediaDocument = await this.findOneAndUpdate({
            filter: { _id: toDbId(id) },
            update: data,
            options: { lean: true },
        });
        if (!mediaDocument) {
            return null;
        }
        return this.toEntity(mediaDocument);
    }

    /**
     * @param folderId - If null, the media will be moved to the root folder.
     */
    async updateMediasFolder(mediaIds: string[], folderId: string | null): Promise<void> {
        await this.updateMany({
            filter: { _id: { $in: mediaIds.map(toDbId) } },
            update: { folderId: folderId === null ? null : toDbId(folderId) },
            options: { lean: true },
        });
    }

    async getFolderMedia(folderId: string): Promise<Media[]> {
        const mediaDocuments = await this.find({
            filter: { folderId: toDbId(folderId), deletedAt: null },
            options: { lean: true },
        });
        return mediaDocuments.map(this.toEntity);
    }

    async getRestaurantMediasPaginated(pagination: Pagination, filters: MediaFilters): Promise<MediasPaginated<Media>> {
        const sortOrder = parseInt(filters.sortOrder, 10);
        const query = filters.buildQuery();
        const restaurantIds = Array.isArray(filters.restaurantId) ? filters.restaurantId : [filters.restaurantId];
        const aggregationResult: MediasPaginated = (
            await this.aggregate([
                {
                    $match: {
                        restaurantId: { $in: restaurantIds.map((restaurantId) => toDbId(restaurantId)) },
                        deletedAt: null,
                    },
                },
                {
                    $facet: {
                        totalCount: [
                            {
                                $match: query,
                            },
                            { $count: 'total' },
                        ],
                        data: [
                            { $match: query },
                            { $sort: { updatedAt: sortOrder } },
                            { $skip: pagination.pageNumber * pagination.pageSize },
                            { $limit: pagination.pageSize },
                        ] as any,
                    },
                },
            ])
        ).pop();

        return {
            totalCount: aggregationResult.totalCount,
            data: aggregationResult.data?.map((mediaDocument) => this.toEntity(mediaDocument)),
        };
    }

    /** Does not return deleted medias */
    async getFolderMedias(folderId: string): Promise<Media[]> {
        const mediaDocument = await this.find({ filter: { folderId: toDbId(folderId), deletedAt: null }, options: { lean: true } });
        return mediaDocument.map(this.toEntity);
    }

    /** Hides a medium from the gallery. Returns false is the medium was already deleted. */
    async markMediumAsDeleted(id: string): Promise<boolean> {
        logger.info('[MEDIA_REPOSITORY] mark medium as deleted', { id });
        const result = await this.findOneAndUpdate({
            filter: { _id: toDbId(id) },
            update: { $set: { deletedAt: new Date() } },
        });
        return !!result;
    }

    async updateTransformData(mediaId: string, transformData: IMediaTransformData): Promise<Media> {
        const mediaDocument = await this.findOneAndUpdateOrFail({
            filter: { _id: toDbId(mediaId) },
            update: { transformData },
            options: { lean: true },
        });
        return this.toEntity(mediaDocument);
    }

    toEntity(document: IMedia): Media {
        return new Media({
            id: document._id.toString(),
            restaurantId: document.restaurantId?.toString(),
            userId: document.userId?.toString(),
            postIds: document.postIds?.map((postId) => postId.toString()),
            originalMediaId: document.originalMediaId?.toString(),
            folderId: document.folderId ? document.folderId.toString() : null,
            duplicatedFromRestaurantId: document.duplicatedFromRestaurantId?.toString(),
            urls: {
                original: document.urls.original,
                small: document.urls.small,
                igFit: document.urls.igFit,
                cover: document.urls.cover,
                smallCover: document.urls.smallCover,
                id: document.urls._id,
            },
            sizes: {
                cover: document.sizes?.cover,
                igFit: document.sizes?.igFit,
                original: document.sizes?.original,
                small: document.sizes?.small,
                smallCover: document.sizes?.smallCover,
                id: document.sizes?._id,
            },
            createdAt: document.createdAt,
            updatedAt: document.updatedAt,
            deletedAt: document.deletedAt,
            socialId: document.socialId,
            category: document.category,
            type: document.type,
            format: document.format,
            title: document.title,
            convertedStatus: document.convertedStatus,
            dimensions: document.dimensions,
            name: document.name,
            thumbnail: document.thumbnail,
            duration: document.duration,
            resizeMetadata: document.resizeMetadata,
            aiDescription: document.aiDescription,
            aiTags: document.aiTags,
            hasDisplayedText: document.hasDisplayedText,
            storedObjects: document.storedObjects,
            transformData: document.transformData,
            aspectRatio: document.aspectRatio,
            isV2: document.isV2,
            original: document.storedObjects?.original,
            thumbnail1024Outside: document.storedObjects?.thumbnail1024Outside,
            thumbnail256Outside: document.storedObjects?.thumbnail256Outside,
            timelinePreviewFrames256h: document.timelinePreviewFrames256h,
            isVideoNormalized: document.isVideoNormalized,
        });
    }

    toDocument(entity: Media): IMedia {
        return {
            ...entity,
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            // !!! dto from zod are all optionals because of strictNullChecks being false
            deleted: entity.deleted ?? undefined,
            aiTags: entity.aiTags,
            socialId: entity.socialId,
            _id: toDbId(entity.id),
            restaurantId: entity.restaurantId ? toDbId(entity.restaurantId) : undefined,
            userId: entity.userId ? toDbId(entity.userId) : null,
            postIds: entity.postIds ? entity.postIds?.map((pid) => toDbId(pid)) : [],
            originalMediaId: entity.originalMediaId ? toDbId(entity.originalMediaId) : null,
            folderId: entity.folderId ? toDbId(entity.folderId) : undefined,
            duplicatedFromRestaurantId: entity.duplicatedFromRestaurantId ? toDbId(entity.duplicatedFromRestaurantId) : undefined,
            urls: {
                ...entity.urls,
                _id: entity.urls?.id ?? newDbId().toString(),
            },
            sizes: {
                ...entity.sizes,
                _id: entity.sizes?.id ?? newDbId().toString(),
            },
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        };
    }
}
