import assert from 'node:assert';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { BusinessCategory, CountryCode, MalouErrorCode } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { getMockRestaurant } from ':helpers/mock-data-generator/restaurant';
import { getMockYextLocation } from ':helpers/mock-data-generator/yextLocation';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import { YextAccountRepository } from ':modules/publishers/yext/repositories/yext-account.repository';
import { YextLocationRepository } from ':modules/publishers/yext/repositories/yext-location.repository';
import { getDefaultYextAccount } from ':modules/publishers/yext/tests/yext-account.builder';
import CreateLocationUseCase from ':modules/publishers/yext/use-cases/create-location/create-location.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { YextProvider } from ':providers/yext/yext.provider';
import { YextProviderMock } from ':providers/yext/yext.provider.mock';

describe('Create location use case', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories([
            'OrganizationsRepository',
            'RestaurantsRepository',
            'CategoriesRepository',
            'MediasRepository',
            'YextAccountRepository',
            'YextLocationRepository',
        ]);
        container.register(YextProvider, { useValue: new YextProviderMock() });
        container.registerSingleton(AgendaSingleton);
    });

    afterEach(() => {
        container.reset();
    });

    it('should throw if restaurant is a brand', async () => {
        const restaurant = getMockRestaurant({ type: BusinessCategory.BRAND, isYextActivated: true });

        const createLocationUseCase = container.resolve(CreateLocationUseCase);
        const promiseResult = createLocationUseCase.execute(restaurant);
        await expect(promiseResult).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: MalouErrorCode.CANNOT_ADD_LOCATION_FOR_BRAND_RESTAURANT,
            })
        );
    });

    it('should throw if country is not supported by Yext', async () => {
        const restaurant = getMockRestaurant({
            address: { country: 'Italia', locality: 'Rome', postalCode: '00148', regionCode: CountryCode.ITALY },
            isYextActivated: true,
        });

        const createLocationUseCase = container.resolve(CreateLocationUseCase);
        const promiseResult = createLocationUseCase.execute(restaurant);
        await expect(promiseResult).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: MalouErrorCode.YEXT_NOT_SUPPORTED,
            })
        );
    });

    it('should throw if yextLocation already exists', async () => {
        const restaurant = getMockRestaurant({ organizationId: newDbId(), isYextActivated: true });
        const yextLocation = getMockYextLocation({ restaurantId: restaurant._id });

        const testCase = new TestCaseBuilderV2<'yextLocations'>({
            seeds: {
                yextLocations: { data: () => [yextLocation] },
            },
            expectedErrorCode: MalouErrorCode.YEXT_LOCATION_ALREADY_EXISTS,
        });
        await testCase.build();

        const createLocationUseCase = container.resolve(CreateLocationUseCase);
        const promiseResult = createLocationUseCase.execute(restaurant);
        await expect(promiseResult).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: testCase.getExpectedErrorCode(),
            })
        );
    });

    it('should throw if organization not found', async () => {
        const restaurant = getMockRestaurant({ organizationId: newDbId(), isYextActivated: true });

        const createLocationUseCase = container.resolve(CreateLocationUseCase);
        const promiseResult = createLocationUseCase.execute(restaurant);
        await expect(promiseResult).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: MalouErrorCode.ORGANIZATION_NOT_FOUND,
            })
        );
    });

    it('should create a yext location (case : yext account exists)', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'organizations' | 'yextAccounts'>({
            seeds: {
                organizations: { data: () => [getDefaultOrganization().build()] },
                restaurants: {
                    data: (dependencies) => [
                        getDefaultRestaurant()
                            .organizationId(dependencies.organizations()[0]._id)
                            .address({ country: 'France', locality: 'Toulon', postalCode: '83200', regionCode: CountryCode.FRANCE })
                            .isYextActivated(true)
                            .build(),
                    ],
                },
                yextAccounts: {
                    data: (dependencies) => [getDefaultYextAccount().organizationId(dependencies.organizations()[0]._id).build()],
                },
            },
            expectedErrorCode: MalouErrorCode.YEXT_LOCATION_ALREADY_EXISTS,
        });
        await testCase.build();

        const restaurant = testCase.getSeededObjects().restaurants[0];

        const createLocationUseCase = container.resolve(CreateLocationUseCase);
        await createLocationUseCase.execute(restaurant);

        const yextLocationRepository = container.resolve(YextLocationRepository);
        const yextLocationCreated = await yextLocationRepository.findOne({
            filter: { restaurantId: restaurant._id },
            options: { lean: true },
        });

        const expectedRestaurantId = restaurant._id;
        const expectedYextAccountId = testCase.getSeededObjects().yextAccounts[0]._id;

        expect(yextLocationCreated).toMatchObject({
            restaurantId: expectedRestaurantId,
            yextAccountId: expectedYextAccountId,
        });
        assert(yextLocationCreated);

        const agendaSingleton = container.resolve(AgendaSingleton);
        const jobs = await agendaSingleton.jobs({ name: AgendaJobName.UPDATE_YEXT_ADD_REQUEST_STATUS });
        expect(jobs).toHaveLength(1);
        const job = jobs[0];
        expect(job.attrs.data).toMatchObject({
            yextLocationId: yextLocationCreated._id.toString(),
        });
    });

    it('should create a yext location (case : yext account does not exists)', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'organizations'>({
            seeds: {
                organizations: { data: () => [getDefaultOrganization().build()] },
                restaurants: {
                    data: (dependencies) => [
                        getDefaultRestaurant()
                            .organizationId(dependencies.organizations()[0]._id)
                            .address({ country: 'France', locality: 'Toulon', postalCode: '83200', regionCode: CountryCode.FRANCE })
                            .isYextActivated(true)
                            .build(),
                    ],
                },
            },
            expectedErrorCode: MalouErrorCode.YEXT_LOCATION_ALREADY_EXISTS,
        });
        await testCase.build();

        const restaurant = testCase.getSeededObjects().restaurants[0];

        const createLocationUseCase = container.resolve(CreateLocationUseCase);
        await createLocationUseCase.execute(restaurant);

        const yextAccountRepository = container.resolve(YextAccountRepository);
        const yextAccountCreated = await yextAccountRepository.findOne({
            filter: { organizationId: restaurant.organizationId },
            options: { lean: true },
        });

        expect(yextAccountCreated).toBeDefined();
        assert(yextAccountCreated);

        expect(yextAccountCreated).toMatchObject({
            organizationId: restaurant.organizationId,
        });

        const yextLocationRepository = container.resolve(YextLocationRepository);
        const yextLocationCreated = await yextLocationRepository.findOne({
            filter: { restaurantId: restaurant._id },
            options: { lean: true },
        });

        expect(yextLocationCreated).toMatchObject({
            restaurantId: restaurant._id,
            yextAccountId: yextAccountCreated._id,
        });

        expect(yextLocationCreated).toBeDefined();
        assert(yextLocationCreated);

        const agendaSingleton = container.resolve(AgendaSingleton);
        const jobs = await agendaSingleton.jobs({ name: AgendaJobName.UPDATE_YEXT_ADD_REQUEST_STATUS });
        expect(jobs).toHaveLength(1);
        const job = jobs[0];
        expect(job.attrs.data).toMatchObject({
            yextLocationId: yextLocationCreated._id.toString(),
        });
    });
});
