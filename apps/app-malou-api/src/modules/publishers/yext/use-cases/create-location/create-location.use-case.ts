import { padEnd } from 'lodash';
import { randomUUID } from 'node:crypto';
import { singleton } from 'tsyringe';

import { IRestaurant, toDbId } from '@malou-io/package-models';
import { BusinessCategory, MalouErrorCode, YEXT_DISABLED_COUNTRY_CODES, YextAddRequestStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { YextAccountRepository } from ':modules/publishers/yext/repositories/yext-account.repository';
import { YextLocationRepository } from ':modules/publishers/yext/repositories/yext-location.repository';
import { ReservationsService } from ':modules/publishers/yext/services/reservations.service';
import { YextEntityMapper } from ':modules/publishers/yext/use-cases/update-location/yext-entity.mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { YextProvider } from ':providers/yext/yext.provider';
import { YextEntity } from ':providers/yext/yext.provider.interfaces';

@singleton()
export default class CreateLocationUseCase {
    constructor(
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _yextAccountRepository: YextAccountRepository,
        private readonly _yextLocationRepository: YextLocationRepository,
        private readonly _yextProvider: YextProvider,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _reservationsService: ReservationsService,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(restaurant: IRestaurant): Promise<void> {
        const restaurantId = restaurant._id.toString();
        if (restaurant.type === BusinessCategory.BRAND) {
            throw new MalouError(MalouErrorCode.CANNOT_ADD_LOCATION_FOR_BRAND_RESTAURANT, {
                message: 'Cannot create location : brand restaurant is not supported',
                metadata: { restaurantId },
            });
        }

        if (!this._isRestaurantCountrySupportedByYext(restaurant)) {
            throw new MalouError(MalouErrorCode.YEXT_NOT_SUPPORTED, {
                message: 'Cannot create location : Yext is not supported for this restaurant',
                metadata: { restaurantId, restaurantRegionCode: restaurant.address?.regionCode },
            });
        }

        const yextLocation = await this._yextLocationRepository.getYextLocationByRestaurantId(restaurantId);

        if (yextLocation) {
            if ([YextAddRequestStatus.CANCELED, YextAddRequestStatus.FAILED].includes(yextLocation.addRequestStatus)) {
                // When the add request is canceled or failed, no account/location are created in Yext
                // so we can delete the location on our side, and create a new one
                logger.info('[CREATE LOCATION]: Deleting yext location', { yextLocation });
                await this._yextLocationRepository.deleteById(yextLocation.id);
            } else {
                throw new MalouError(MalouErrorCode.YEXT_LOCATION_ALREADY_EXISTS, {
                    message: 'Cannot create location: yext location already exists',
                    metadata: { restaurantId, yextLocationId: yextLocation.id },
                });
            }
        }

        const organization = await this._organizationsRepository.getOrganizationById(restaurant.organizationId.toString());

        if (!organization) {
            throw new MalouError(MalouErrorCode.ORGANIZATION_NOT_FOUND, {
                message: 'Cannot create location : organization not found',
                metadata: { restaurantId, organizationId: restaurant.organizationId },
            });
        }

        try {
            const yextAccount = await this._yextAccountRepository.getByOrganizationId(restaurant.organizationId.toString());

            const partnerAccountId = yextAccount ? yextAccount.partnerAccountId : randomUUID();
            // Yext need accountName to be at least 3 characters
            const accountName = yextAccount ? undefined : padEnd(organization.name, 3, '-');

            const canMakeReservations = await this._reservationsService.canMakeReservations(restaurantId);
            const entity = await this._getYextEntity(restaurantId, canMakeReservations);
            const addRequestResponse = await this._yextProvider.createAddRequestForNewLocation(partnerAccountId, accountName, entity);

            let yextAccountId: string;
            if (!yextAccount) {
                const createdYextAccount = await this._yextAccountRepository.createYextAccount({
                    organizationId: organization.id,
                    partnerAccountId: addRequestResponse.responseBody.response.newLocationAccountId,
                });
                yextAccountId = createdYextAccount.id;
            } else {
                yextAccountId = yextAccount?.id;
            }

            const createYextLocation = await this._yextLocationRepository.createYextLocation({
                yextAccountId: yextAccountId,
                restaurantId,
                partnerLocationId: addRequestResponse.partnerLocationId,
                addRequestId: addRequestResponse.responseBody.response.id,
                addRequestStatus: addRequestResponse.responseBody.response.status,
            });

            await this._agendaSingleton.schedule(new Date(), AgendaJobName.UPDATE_YEXT_ADD_REQUEST_STATUS, {
                yextLocationId: createYextLocation.id,
            });
        } catch (error) {
            logger.error('[CREATE LOCATION]: Error creating location', error);
            throw error;
        }
    }

    private _isRestaurantCountrySupportedByYext(restaurant: IRestaurant): boolean {
        return !restaurant.address?.regionCode || !YEXT_DISABLED_COUNTRY_CODES.includes(restaurant.address.regionCode);
    }

    private async _getYextEntity(restaurantId: string, canMakeReservations: boolean): Promise<YextEntity> {
        const restaurantPopulated: RestaurantPopulatedToPublish | null = (await this._restaurantsRepository.findOne({
            filter: { _id: toDbId(restaurantId) },
            options: {
                lean: true,
                populate: [
                    { path: 'category' },
                    { path: 'logo' },
                    { path: 'cover' },
                    { path: 'categoryList' },
                    { path: 'attributeList', populate: [{ path: 'attribute' }] },
                ],
            },
        })) as RestaurantPopulatedToPublish | null;

        if (!restaurantPopulated) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                message: '[CREATE YEXT LOCATION] Cannot create location : restaurant not found',
                metadata: { restaurantId },
            });
        }

        // We should use categoryIds instead of categories when creating a new location
        const shouldUseCategoryIds = true;
        const yextMapper = new YextEntityMapper(restaurantPopulated);
        return yextMapper.mapRestaurantToYextEntity(canMakeReservations, shouldUseCategoryIds);
    }
}
