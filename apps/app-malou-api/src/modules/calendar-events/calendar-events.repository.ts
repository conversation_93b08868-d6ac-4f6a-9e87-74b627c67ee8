import { isUndefined, omitBy } from 'lodash';
import { DateTime } from 'luxon';
import { FilterQuery } from 'mongoose';
import { singleton } from 'tsyringe';

import { CalendarEventModel, EntityRepository, ICalendarEvent, toDbId, toDbIds } from '@malou-io/package-models';

import { CalendarEvent, CalendarEventProps } from ':modules/calendar-events/entities/calendar-event.entity';

@singleton()
export default class CalendarEventsRepository extends EntityRepository<ICalendarEvent> {
    constructor() {
        super(CalendarEventModel);
    }

    async createNewEvent(event: Omit<CalendarEventProps, 'id' | 'createdAt' | 'updatedAt'>): Promise<CalendarEvent> {
        const createdDocument = await this.create({ data: event });
        return this.toEntity(createdDocument);
    }

    async findEvent(partialEvent: Partial<CalendarEvent>): Promise<CalendarEvent | null> {
        const filter = this._toPartialDocument(partialEvent);
        const event = await this.findOne({ filter, options: { lean: true } });
        if (!event) {
            return null;
        }
        return this.toEntity(event);
    }

    async getCalendarEventsByIdsAndCountriesAfterDate(
        ids: string[],
        countries: string[],
        options?: { afterDate?: Date; beforeDate?: Date }
    ): Promise<CalendarEvent[]> {
        const dbIds = toDbIds(ids);
        const basicFilter = { _id: { $in: dbIds }, country: { $in: countries } };
        const $and: FilterQuery<ICalendarEvent>['$and'] = [];
        if (options?.afterDate) {
            $and.push({ startDate: { $gte: options.afterDate } });
        }
        if (options?.beforeDate) {
            $and.push({ startDate: { $lte: options.beforeDate } });
        }
        const startDateFilter = $and.length > 0 ? { $and } : {};
        const filter = { ...basicFilter, ...startDateFilter };
        const events = await this.find({
            filter,
            options: { lean: true },
        });
        return events.map((event) => this.toEntity(event));
    }

    toEntity(document: ICalendarEvent): CalendarEvent {
        return new CalendarEvent({
            id: document._id.toString(),
            startDate: document.startDate,
            emoji: document.emoji,
            key: document.key,
            country: document.country,
            name: {
                fr: document.name.fr ?? undefined,
                en: document.name.en ?? undefined,
                it: document.name.it ?? undefined,
                es: document.name.es ?? undefined,
            },
            byDefault: document.byDefault,
            example: document.example,
            ideas: document.ideas,
            isBankHoliday: document.isBankHoliday,
            shouldSuggestSpecialHourUpdate: document.shouldSuggestSpecialHourUpdate,
            shouldSuggestToPost: document.shouldSuggestToPost,
            createdAt: document.createdAt,
            updatedAt: document.updatedAt,
        });
    }

    private _toPartialDocument(event: Partial<CalendarEvent>): Partial<ICalendarEvent> {
        const partialDocument = {
            _id: event.id ? toDbId(event.id) : undefined,
            startDate: event.startDate,
            emoji: event.emoji,
            key: event.key,
            country: event.country,
            name: event.name,
            byDefault: event.byDefault,
            example: event.example,
            ideas: event.ideas,
            isBankHoliday: event.isBankHoliday,
            createdAt: event.createdAt,
            updatedAt: event.updatedAt,
        };

        return omitBy(partialDocument, isUndefined);
    }

    async findAllEventsByStartDate({
        startDate,
        onlyDefaultEvents,
    }: {
        startDate: DateTime;
        onlyDefaultEvents: boolean;
    }): Promise<CalendarEvent[]> {
        const filter: any = {
            startDate: { $gte: startDate.toJSDate(), $lte: startDate.endOf('day').toJSDate() },
        };

        if (onlyDefaultEvents) {
            filter.byDefault = true;
        }

        const events = await this.find({
            filter,
            options: { lean: true },
        });
        return events.map((event) => this.toEntity(event));
    }
}
