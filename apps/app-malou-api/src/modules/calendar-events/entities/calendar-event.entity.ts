import { CalendarEventDto } from '@malou-io/package-dto';
import { RemoveMethodsFromClass } from '@malou-io/package-utils';

export type CalendarEventProps = RemoveMethodsFromClass<CalendarEvent>;

interface CalendarEventName {
    en?: string;
    fr?: string;
    es?: string;
    it?: string;
}

interface CalendarEventExample {
    fr?: string;
    en?: string;
    es?: string;
    it?: string;
}

export class CalendarEvent {
    id: string;
    startDate: Date;
    emoji?: string;
    key: string;
    country?: string;
    name: CalendarEventName;
    byDefault: boolean;
    example?: CalendarEventExample;
    ideas?: CalendarEventExample;
    isBankHoliday?: boolean;
    shouldSuggestSpecialHourUpdate?: boolean;
    shouldSuggestToPost?: {
        active: boolean;
        concernedRestaurantCategories: string[];
    };
    createdAt: Date;
    updatedAt: Date;

    constructor(calendarEvent: CalendarEventProps) {
        this.id = calendarEvent.id;
        this.startDate = calendarEvent.startDate;
        this.emoji = calendarEvent.emoji;
        this.key = calendarEvent.key;
        this.country = calendarEvent.country;
        this.name = calendarEvent.name;
        this.byDefault = calendarEvent.byDefault;
        this.example = calendarEvent.example;
        this.ideas = calendarEvent.ideas;
        this.isBankHoliday = calendarEvent.isBankHoliday;
        this.shouldSuggestSpecialHourUpdate = calendarEvent.shouldSuggestSpecialHourUpdate ?? false;
        this.shouldSuggestToPost = calendarEvent.shouldSuggestToPost ?? {
            active: false,
            concernedRestaurantCategories: [],
        };
        this.createdAt = calendarEvent.createdAt;
        this.updatedAt = calendarEvent.updatedAt;
    }

    toDto(): CalendarEventDto {
        return {
            id: this.id,
            startDate: this.startDate,
            emoji: this.emoji,
            key: this.key,
            country: this.country,
            name: this.name,
            byDefault: this.byDefault,
            example: this.example,
            ideas: this.ideas,
            isBankHoliday: this.isBankHoliday,
            shouldSuggestSpecialHourUpdate: this.shouldSuggestSpecialHourUpdate,
            shouldSuggestToPost: this.shouldSuggestToPost,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
