import assert from 'assert';
import { singleton } from 'tsyringe';

import { CalendarEventDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { allCountries } from '@malou-io/package-utils';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class GetEventsBetweenDatesUseCase {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(restaurantId: string, startDate: string, endDate: string): Promise<CalendarEventDto[]> {
        const restaurant = await this._restaurantsRepository.findOne({
            filter: { _id: toDbId(restaurantId) },
            projection: { calendarEventsCountry: 1, address: 1, calendarEvents: 1 },
            options: { lean: true, populate: [{ path: 'calendarEvents' }] },
        });
        assert(restaurant);

        const country = restaurant.calendarEventsCountry ?? restaurant.address?.regionCode;

        return restaurant.calendarEvents
            .map((calendarEvent) => ({
                ...calendarEvent,
                id: calendarEvent._id.toString(),
                startDate: calendarEvent.startDate,
                name: {
                    en: calendarEvent.name.en ?? undefined,
                    fr: calendarEvent.name.fr ?? undefined,
                    es: calendarEvent.name.es ?? undefined,
                    it: calendarEvent.name.it ?? undefined,
                },
            }))
            .filter((evt) => {
                return (
                    (!evt.byDefault || [country, allCountries].includes(evt.country)) &&
                    evt.startDate.getTime() >= new Date(startDate).getTime() &&
                    evt.startDate.getTime() <= new Date(endDate).getTime()
                );
            });
    }
}
