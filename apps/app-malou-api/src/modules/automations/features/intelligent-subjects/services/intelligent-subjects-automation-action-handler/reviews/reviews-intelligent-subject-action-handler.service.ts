import { render } from '@react-email/render';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { ReviewIntelligentSubjectTemplate } from '@malou-io/package-emails';
import { IReview, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import {
    getPlatformDefinition,
    HeapEventName,
    IntelligentSubjectAutomationAction,
    IntelligentSubjectAutomationRelatedEntity,
    Locale,
    MalouErrorCode,
    processPromisesByChunks,
    TimeInMilliseconds,
    waitFor,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { ApiKeysRepository } from ':modules/api-keys/api-keys.repository';
import { IntelligentSubjectAutomation } from ':modules/automations/features/intelligent-subjects/entities/intelligent-subject-automation.entity';
import { EmailSenderService } from ':modules/mailing/email-sender.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { HeapAnalyticsService } from ':plugins/heap-analytics';
import { Translation } from ':services/translation.service';

/**
 * @internal
 * This service is intended for use only within the intelligent-subjects-automation-action-handler directory
 */
@singleton()
export class ReviewsIntelligentSubjectActionHandlerService {
    private readonly EMAILS_CHUNK_SIZE = 10;
    private readonly INTERVAL_BETWEEN_EMAILS = 5;
    constructor(
        private readonly _emailSenderService: EmailSenderService,
        private readonly _heapAnalyticsService: HeapAnalyticsService,
        private readonly _apiKeysRepository: ApiKeysRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _translator: Translation
    ) {}

    async handleReviewAction({ automations, reviewId }: { automations: IntelligentSubjectAutomation[]; reviewId: string }): Promise<void> {
        const review = await this._reviewsRepository.findOneOrFail({
            filter: { _id: toDbId(reviewId) },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
        });

        const { action } = automations[0];

        switch (action) {
            case IntelligentSubjectAutomationAction.SEND_EMAIL:
                /*
                    In this case, we can have multiple automations with different `subject`
                    if the `action` is to send an email, we need to send only one email to all the recipients of the automations
                */
                await this._sendEmailsToRecipients({ review, automations });
                break;
            default:
                throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                    message: `[ReviewsIntelligentSubjectActionHandlerService] Action ${action} is not implemented`,
                    metadata: {
                        action,
                    },
                });
        }
    }

    private async _sendEmailsToRecipients({
        automations,
        review,
    }: {
        automations: IntelligentSubjectAutomation[];
        review: IReview;
    }): Promise<void> {
        const recipients = automations.map((automation) => automation.recipientEmails).flat();
        const uniqueRecipients = [...new Set(recipients)];

        if (!uniqueRecipients.length) {
            logger.info('[ReviewsIntelligentSubjectActionHandlerService] No recipients found for the review', { reviewId: review._id });
            return;
        }

        const emailsPromises: (() => Promise<void>)[] = [];
        for (const recipientEmail of uniqueRecipients) {
            emailsPromises.push(async () => this._sendEmail({ recipientEmail, review }).catch((e) => e));
        }
        await processPromisesByChunks(emailsPromises, this.EMAILS_CHUNK_SIZE, async (_) => {
            await waitFor(this.INTERVAL_BETWEEN_EMAILS * TimeInMilliseconds.SECOND);
        });
    }

    private async _sendEmail({ recipientEmail, review }: { recipientEmail: string; review: IReview }): Promise<void> {
        const apiKey = await this._getApiKey();
        const userInfo = await this._getUserInfo(recipientEmail);
        const restaurantName = await this._getRestaurantName(review.restaurantId.toString());
        const emailSubject = this._getEmailSubject(userInfo.lang);
        const reviewIntelligentSubjectTemplate = ReviewIntelligentSubjectTemplate({
            locale: userInfo.lang,
            receiver: userInfo.receiverName,
            trackingUrl: this._buildTrackingUrl({ review, apiKey, recipientEmail }),
            reviewerProfilePictureUrl: review.reviewer?.profilePhotoUrl ?? '',
            socialAttachmentUrls: review.socialAttachments?.map((attachment) => attachment.urls.original),
            reviewerName: review.reviewer?.displayName ?? '',
            rating: review.rating ?? 0,
            text: review.text ?? '',
            socialCreatedAt: review.socialCreatedAt,
            platformName: getPlatformDefinition(review.key)?.fullName ?? '',
            platformKey: review.key,
            intelligentSubjects: review.intelligentSubjects ?? [],
            link: `${Config.baseAppUrl}/restaurants/${review.restaurantId.toString()}/reputation/reviews?reviewId=${review._id.toString()}`,
            restaurantName,
        });

        const html = render(reviewIntelligentSubjectTemplate);

        await this._emailSenderService.sendEmail({
            to: recipientEmail,
            subject: emailSubject,
            html,
            lang: userInfo.lang,
            fromEmail: Config.settings.adminUpdatesNotificationEmail,
        });

        await this._heapAnalyticsService.track({
            eventName: HeapEventName.REVIEW_INTELLIGENT_SUBJECT_EMAIL_SENT,
            identity: recipientEmail,
            properties: {
                receiverEmail: recipientEmail,
                restaurantId: review.restaurantId.toString(),
                reviewId: review._id.toString(),
            },
        });
    }

    private _getEmailSubject(lang: Locale): string {
        return this._translator.fromLang({ lang }).automations.intelligent_subjects.reviews.subject();
    }

    private _buildTrackingUrl({ review, apiKey, recipientEmail }: { review: IReview; apiKey: string; recipientEmail: string }): string {
        const subjects = review.intelligentSubjects?.map((intelligentSubject) => intelligentSubject.subject).join(',');
        const urlWithoutQuery = `${Config.baseApiUrl}/automations/intelligent-subjects/emails/opened`;
        const today = new Date().getTime();
        return `${urlWithoutQuery}?entityId=${review._id.toString()}&receiverEmail=${recipientEmail}&t=${today}&api_key=${apiKey}
            &relatedEntity=${IntelligentSubjectAutomationRelatedEntity.REVIEWS}&action=${IntelligentSubjectAutomationAction.SEND_EMAIL}
            &restaurantId=${review.restaurantId.toString()}&subjects=${subjects}`;
    }

    private async _getApiKey(): Promise<string> {
        const apiKey = (await this._apiKeysRepository.getApiKeyByName('email'))?.apiKey;
        assert(apiKey, 'Email API key not found');
        return apiKey;
    }

    private async _getUserInfo(recipientEmail: string): Promise<{ receiverName: string; lang: Locale }> {
        const user = await this._usersRepository.findOne({
            filter: { email: recipientEmail },
            projection: { defaultLanguage: 1, name: 1 },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });

        const receiverName = user?.name ?? this._getReceiverDisplayName(recipientEmail);
        const lang = (user?.defaultLanguage as unknown as Locale) ?? Locale.FR;
        return { receiverName, lang };
    }

    private _getReceiverDisplayName(recipientEmail: string): string {
        const emailParts = recipientEmail.split('@');
        const receiverName = emailParts[0];
        return receiverName;
    }

    private async _getRestaurantName(restaurantId: string): Promise<string> {
        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            projection: { name: 1 },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
        return restaurant.name;
    }
}
