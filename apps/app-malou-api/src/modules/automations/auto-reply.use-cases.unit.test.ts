import { container } from 'tsyringe';

import { IReview, newDbId } from '@malou-io/package-models';
import {
    IntelligentSubjectName,
    MalouErrorCode,
    ReviewAnalysisSentiment,
    ReviewReplyAutomationMethod,
    TemplateTag,
} from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { generateMockReview } from ':helpers/mock-data-generator/reviews';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { GenerateReviewReplyUseCase } from ':modules/ai/use-cases';
import { getDefaultIntelligentSubjectAutomation } from ':modules/automations/tests/intelligent-subject-automation.builder';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import TemplatesRepository from ':modules/templates/templates.repository';
import * as experimentationService from ':services/experimentations-service/experimentation.service';

import { AutoReplyUseCases } from './auto-reply.use-cases';
import ReviewReplyAutomationsRepository from './features/review-replies/review-replies.repository';

let autoReplyUseCase: AutoReplyUseCases;
let templatesRepository: TemplatesRepository;
let restaurantsRepository: RestaurantsRepository;
let agendaSingleton: AgendaSingleton;
let aiInteractionsRepository: AiInteractionsRepository;
let reviewReplyAutomationsRepository: ReviewReplyAutomationsRepository;
let generateReviewReplyUseCase: GenerateReviewReplyUseCase;

describe('auto-reply.use-cases.ts', () => {
    beforeEach(() => {
        container.reset();

        registerRepositories(['RestaurantsRepository', 'ReviewsRepository', 'IntelligentSubjectAutomationsRepository']);

        restaurantsRepository = {} as RestaurantsRepository;
        container.registerInstance(RestaurantsRepository, restaurantsRepository);
        templatesRepository = {} as TemplatesRepository;
        container.registerInstance(TemplatesRepository, templatesRepository);
        agendaSingleton = {} as AgendaSingleton;
        container.registerInstance(AgendaSingleton, agendaSingleton);
        aiInteractionsRepository = {} as AiInteractionsRepository;
        container.registerInstance(AiInteractionsRepository, aiInteractionsRepository);
        reviewReplyAutomationsRepository = {} as ReviewReplyAutomationsRepository;
        container.registerInstance(ReviewReplyAutomationsRepository, reviewReplyAutomationsRepository);
        generateReviewReplyUseCase = {} as GenerateReviewReplyUseCase;
        container.registerInstance(GenerateReviewReplyUseCase, generateReviewReplyUseCase);

        autoReplyUseCase = container.resolve(AutoReplyUseCases);

        jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
    });

    describe('handleReviewAutoReply', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should skip process if restaurant is not active', async () => {
            const review = generateMockReview({ socialUpdatedAt: new Date(), comments: [], archived: false });
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: false });

            const _isReviewTooOldToBeRepliedMock = jest.fn();
            jest.spyOn(AutoReplyUseCases.prototype as any, '_isReviewTooOldToBeReplied').mockImplementation(_isReviewTooOldToBeRepliedMock);

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(_isReviewTooOldToBeRepliedMock).not.toHaveBeenCalled();
        });

        it('should skip process if review already replied', async () => {
            const review = generateMockReview({ socialUpdatedAt: new Date(), archived: false });

            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            const getReviewReplyAutomationMock = jest.fn();
            reviewReplyAutomationsRepository.getReviewReplyAutomation = getReviewReplyAutomationMock;

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(getReviewReplyAutomationMock).not.toHaveBeenCalled();
        });

        it('should skip process if review is archived', async () => {
            const review = generateMockReview({ socialUpdatedAt: null, socialCreatedAt: new Date(), comments: [], archived: true });
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            const getReviewReplyAutomationMock = jest.fn();
            reviewReplyAutomationsRepository.getReviewReplyAutomation = getReviewReplyAutomationMock;

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(getReviewReplyAutomationMock).not.toHaveBeenCalled();
        });

        it('should skip process if review is too old', async () => {
            const review = generateMockReview({ socialUpdatedAt: null, socialCreatedAt: new Date('2020-01-01'), archived: false });
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            const getReviewReplyAutomationMock = jest.fn();
            reviewReplyAutomationsRepository.getReviewReplyAutomation = getReviewReplyAutomationMock;

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(getReviewReplyAutomationMock).not.toHaveBeenCalled();
        });

        it('should skip process if no automation found', async () => {
            const review = generateMockReview({ socialUpdatedAt: null, socialCreatedAt: new Date(), comments: [], archived: false });
            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest.fn().mockResolvedValue(null);
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            const _handleAutoReplyWithAiMock = jest.fn();
            const _handleAutoReplyWithTemplatesMock = jest.fn();
            jest.spyOn(AutoReplyUseCases.prototype as any, '_handleAutoReplyWithAi').mockImplementation(_handleAutoReplyWithAiMock);
            jest.spyOn(AutoReplyUseCases.prototype as any, '_handleAutoReplyWithTemplates').mockImplementation(
                _handleAutoReplyWithTemplatesMock
            );

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(_handleAutoReplyWithAiMock).not.toHaveBeenCalled();
            expect(_handleAutoReplyWithTemplatesMock).not.toHaveBeenCalled();
        });

        it('should skip process if an active intelligent subject automation match a detected intelligent subject in the review', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'intelligentSubjectAutomations'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects([
                                        {
                                            subject: IntelligentSubjectName.DISCRIMINATION,
                                            isDetected: true,
                                            sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                        },
                                    ])
                                    .comments([])
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const review = seededObjects.reviews[0] as IReview;

            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.AI });

            const _handleAutoReplyWithAiMock = jest.fn();
            const _handleAutoReplyWithTemplatesMock = jest.fn();
            jest.spyOn(AutoReplyUseCases.prototype as any, '_handleAutoReplyWithAi').mockImplementation(_handleAutoReplyWithAiMock);
            jest.spyOn(AutoReplyUseCases.prototype as any, '_handleAutoReplyWithTemplates').mockImplementation(
                _handleAutoReplyWithTemplatesMock
            );

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(_handleAutoReplyWithAiMock).not.toHaveBeenCalled();
            expect(_handleAutoReplyWithTemplatesMock).not.toHaveBeenCalled();
        });

        it('should throw if AI reply generation fails when AI reply is chosen', async () => {
            const review = generateMockReview({ socialUpdatedAt: new Date(), comments: [], archived: false });
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.AI });

            jest.spyOn(AutoReplyUseCases.prototype as any, '_getReviewGeneratedReply').mockImplementation(() => Promise.resolve(null));
            generateReviewReplyUseCase.execute = jest.fn().mockRejectedValue(
                new MalouError(MalouErrorCode.NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL, {
                    message: 'Restaurant has exceeded the maximum number of calls to the AI API',
                })
            );

            return autoReplyUseCase.handleReviewAutoReply(review).catch((err) => {
                expect(err.malouErrorCode).toEqual(MalouErrorCode.NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL);
            });
        });

        it('should skip process if shouldValidateAiBeforeSend is true when AI reply is chosen', async () => {
            const review = generateMockReview({ socialUpdatedAt: new Date(), comments: [], archived: false });
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });
            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()

                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.AI, shouldValidateAiBeforeSend: true });

            jest.spyOn(AutoReplyUseCases.prototype as any, '_getReviewGeneratedReply').mockImplementation(() => Promise.resolve(null));
            generateReviewReplyUseCase.execute = jest
                .fn()
                .mockResolvedValue({ completionText: 'generated reply text', aiInteractionId: newDbId() });

            const _createJobToPublishReplyMock = jest.fn();
            jest.spyOn(AutoReplyUseCases.prototype as any, '_createJobToPublishReply').mockImplementation(_createJobToPublishReplyMock);

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(_createJobToPublishReplyMock).not.toHaveBeenCalled();
        });

        it('should create a job to publish reply when AI reply is chosen', async () => {
            const review = generateMockReview({ _id: newDbId(), socialUpdatedAt: new Date(), comments: [], archived: false });
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.AI, shouldValidateAiBeforeSend: false });

            jest.spyOn(AutoReplyUseCases.prototype as any, '_getReviewGeneratedReply').mockImplementation(() => Promise.resolve(null));
            const interactionId = newDbId();
            generateReviewReplyUseCase.execute = jest
                .fn()
                .mockResolvedValue({ completionText: 'generated reply text', aiInteractionId: interactionId });

            const scheduleMock = jest.fn().mockResolvedValue({ name: AgendaJobName.AUTO_REPLY_TO_REVIEW });
            agendaSingleton.jobs = jest.fn().mockResolvedValue([]);
            agendaSingleton.schedule = scheduleMock;

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(scheduleMock).toBeCalledWith(expect.any(Date), AgendaJobName.AUTO_REPLY_TO_REVIEW, {
                reviewId: review._id.toString(),
                replyText: 'generated reply text',
                interactionId,
            });
        });

        it('should not create when a job to publish reply already exists when AI reply is chosen', async () => {
            const review = generateMockReview({ _id: newDbId(), socialUpdatedAt: new Date(), comments: [], archived: false });
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.AI, shouldValidateAiBeforeSend: false });

            jest.spyOn(AutoReplyUseCases.prototype as any, '_getReviewGeneratedReply').mockImplementation(() => Promise.resolve(null));
            const interactionId = newDbId();
            generateReviewReplyUseCase.execute = jest
                .fn()
                .mockResolvedValue({ completionText: 'generated reply text', aiInteractionId: interactionId });

            const scheduleMock = jest.fn().mockResolvedValue({ name: AgendaJobName.AUTO_REPLY_TO_REVIEW });
            agendaSingleton.jobs = jest.fn().mockResolvedValue([{ attrs: { data: { reviewId: review._id } } }]);
            agendaSingleton.schedule = scheduleMock;

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(scheduleMock).not.toHaveBeenCalled();
        });

        it('should skip process if no templateIds found when TEMPLATES reply is chosen', async () => {
            const review = generateMockReview({ socialUpdatedAt: new Date(), comments: [], archived: false });
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.TEMPLATES, templateIds: [] });

            const _createJobToPublishReplyMock = jest.fn();
            jest.spyOn(AutoReplyUseCases.prototype as any, '_createJobToPublishReply').mockImplementation(_createJobToPublishReplyMock);

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(_createJobToPublishReplyMock).not.toHaveBeenCalled();
        });

        it('should skip process if no populated templates when TEMPLATES reply is chosen', async () => {
            const review = generateMockReview({ socialUpdatedAt: new Date(), comments: [], archived: false });
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ active: true });

            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.TEMPLATES, templateIds: ['321859476'] });

            templatesRepository.find = jest.fn().mockResolvedValue([]);

            const _createJobToPublishReplyMock = jest.fn();
            jest.spyOn(AutoReplyUseCases.prototype as any, '_createJobToPublishReply').mockImplementation(_createJobToPublishReplyMock);

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(_createJobToPublishReplyMock).not.toHaveBeenCalled();
        });

        it('should create a job to publish reply when TEMPLATES reply is chosen', async () => {
            const review = generateMockReview({ _id: newDbId(), socialUpdatedAt: new Date(), comments: [], archived: false });
            const templateId = newDbId();
            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.TEMPLATES, templateIds: [templateId] });

            templatesRepository.find = jest.fn().mockResolvedValue([{ _id: templateId, text: 'template text' }]);
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ name: 'Les copains suisses', active: true });

            const scheduleMock = jest.fn().mockResolvedValue({ name: AgendaJobName.AUTO_REPLY_TO_REVIEW });
            agendaSingleton.jobs = jest.fn().mockResolvedValue([]);
            agendaSingleton.schedule = scheduleMock;

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(scheduleMock).toBeCalledWith(expect.any(Date), AgendaJobName.AUTO_REPLY_TO_REVIEW, {
                reviewId: review._id.toString(),
                replyText: 'template text',
                templateId,
            });
        });
    });

    describe('_replaceTemplateTags', () => {
        const restaurantName = 'Les copains suisses';
        const reviewId = newDbId();
        const templateId = newDbId();
        const review = generateMockReview({
            _id: reviewId,
            socialUpdatedAt: new Date(),
            comments: [],
            archived: false,
            reviewer: {
                displayName: 'Jacob',
                socialId: null,
                socialUrl: null,
                profilePhotoUrl: null,
            },
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should replace client name tag', async () => {
            const text = `Bonjour ${TemplateTag.CLIENT_NAME}`;
            const template = { _id: templateId, text, user: { name: 'Hugo' } };

            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.TEMPLATES, templateIds: [templateId] });

            templatesRepository.find = jest.fn().mockResolvedValue([template]);
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ name: 'Les copains suisses', active: true });

            const expectedTemplateText = `Bonjour ${review.reviewer?.displayName ?? ''}`;

            const _createJobToPublishReplyMock = jest.fn();
            jest.spyOn(AutoReplyUseCases.prototype as any, '_createJobToPublishReply').mockImplementation(_createJobToPublishReplyMock);

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(_createJobToPublishReplyMock).toBeCalledWith({
                reviewId: reviewId.toString(),
                replyText: expectedTemplateText,
                templateId,
            });
        });

        it('should replace restaurant name tag', async () => {
            const text = `Bienvenue chez ${TemplateTag.BUSINESS_NAME}`;
            const template = { _id: templateId, text, user: { name: 'Hugo' } };

            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.TEMPLATES, templateIds: [templateId] });

            templatesRepository.find = jest.fn().mockResolvedValue([template]);
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ name: restaurantName, active: true });

            const expectedTemplateText = `Bienvenue chez ${restaurantName}`;

            const _createJobToPublishReplyMock = jest.fn();
            jest.spyOn(AutoReplyUseCases.prototype as any, '_createJobToPublishReply').mockImplementation(_createJobToPublishReplyMock);

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(_createJobToPublishReplyMock).toBeCalledWith({
                reviewId: reviewId.toString(),
                replyText: expectedTemplateText,
                templateId,
            });
        });

        it('should replace myFirstname tag', async () => {
            const text = `Bonjour, je suis ${TemplateTag.MY_FIRSTNAME}. Avez-vous besoin d'aide ?`;
            const templateUserName = 'Hugo';
            const template = { _id: templateId, text, user: { name: templateUserName } };

            const expectedTemplateText = `Bonjour, je suis ${templateUserName}. Avez-vous besoin d'aide ?`;

            reviewReplyAutomationsRepository.getReviewReplyAutomation = jest
                .fn()
                .mockResolvedValue({ replyMethod: ReviewReplyAutomationMethod.TEMPLATES, templateIds: [templateId] });

            templatesRepository.find = jest.fn().mockResolvedValue([template]);
            restaurantsRepository.findOneOrFail = jest.fn().mockResolvedValue({ name: restaurantName, active: true });

            const _createJobToPublishReplyMock = jest.fn();
            jest.spyOn(AutoReplyUseCases.prototype as any, '_createJobToPublishReply').mockImplementation(_createJobToPublishReplyMock);

            await autoReplyUseCase.handleReviewAutoReply(review);

            expect(_createJobToPublishReplyMock).toBeCalledWith({
                reviewId: reviewId.toString(),
                replyText: expectedTemplateText,
                templateId,
            });
        });
    });
});
