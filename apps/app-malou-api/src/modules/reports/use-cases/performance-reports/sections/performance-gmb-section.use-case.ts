import { chunk } from 'lodash';
import { singleton } from 'tsyringe';

import { gmbPerformanceReportValidator, GmbPerformanceReportValidatorProps, SimpleRestaurant } from '@malou-io/package-dto';
import { ID } from '@malou-io/package-models';
import {
    AggregationTimeScale,
    BusinessCategory,
    computeGrowth,
    getGrowthVariation,
    isNotNil,
    MalouMetric,
    roundToDecimals,
} from '@malou-io/package-utils';

import { PlatformInsightFiltersGmb } from ':helpers/filters/platform-insight-filters-api-factory';
import { logger } from ':helpers/logger';
import { GmbPlatformInsights } from ':modules/platform-insights/platforms/gmb/use-cases';
import { Report } from ':modules/reports/report.entity';
import { IPerformancePeriods } from ':modules/reports/use-cases/performance-reports/performance-reports.interface';

type GmbRestaurant = {
    restaurantId: ID;
    restaurantName: string;
    address: string;
    logo: string;
    currentTotalImpressions: number;
    currentTotalActions: number;
    currentRatioActionsOverImpressions: number;
    previousTotalImpressions: number;
    previousTotalActions: number;
    previousRatioActionsOverImpressions: number;
};

type GmbInsightsEmailSection = {
    bestPerformingRestaurant?: GmbRestaurant;
    worstPerformingRestaurant?: GmbRestaurant;
    currentTotalImpressions: number;
    currentTotalActions: number;
    currentRatioActionsOverImpressions: number;
    previousTotalImpressions: number;
    previousTotalActions: number;
    previousRatioActionsOverImpressions: number;
    restaurantsInsightsDetails: GmbRestaurant[];
};

@singleton()
export class GetPerformanceGmbSectionUseCase {
    constructor(private readonly _gmbInsightsUseCases: GmbPlatformInsights) {}

    async execute({
        report,
        config,
        periods,
    }: {
        report: Report;
        config: Report['configurations'][0];
        periods: IPerformancePeriods;
    }): Promise<GmbPerformanceReportValidatorProps | undefined> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const gmbInsightsEmailSection = await this.getGmbInsightsEmailSection({ config, periods });
            return this._mapGmbEmailSection(gmbInsightsEmailSection);
        } catch (err) {
            logger.error(`${report.getLogGroup()} GMB insights section`, {
                err,
                ...metaData,
            });

            return undefined;
        }
    }

    async getGmbInsightsEmailSection({
        config,
        periods,
    }: {
        config: Report['configurations'][0];
        periods: IPerformancePeriods;
    }): Promise<GmbInsightsEmailSection> {
        const { restaurants } = config;

        const localBusinesses = this._getBusinessRestaurants(restaurants);
        const restaurantsInsightsDetails: GmbInsightsEmailSection['restaurantsInsightsDetails'] = [];
        for (const restaurantsChunk of chunk(localBusinesses, 10)) {
            const insights = (
                await Promise.all(
                    restaurantsChunk.map((restaurant) => this._getPreviousAndCurrentInsightsFromRestaurant({ restaurant, periods }))
                )
            ).filter(isNotNil);
            restaurantsInsightsDetails.push(...insights);
        }

        const { bestPerformingRestaurant, worstPerformingRestaurant } =
            this._getBestAndWorstGmbInsightsPerformingRestaurants(restaurantsInsightsDetails);
        const {
            currentTotalImpressions,
            currentTotalActions,
            currentRatioActionsOverImpressions,
            previousTotalImpressions,
            previousTotalActions,
            previousRatioActionsOverImpressions,
        } = this.computeTotalOfEachInsightMetrics(restaurantsInsightsDetails);

        return {
            currentTotalImpressions,
            previousTotalImpressions,
            currentTotalActions,
            previousTotalActions,
            bestPerformingRestaurant,
            worstPerformingRestaurant,
            currentRatioActionsOverImpressions,
            previousRatioActionsOverImpressions,
            restaurantsInsightsDetails,
        };
    }

    private _mapGmbEmailSection({
        bestPerformingRestaurant,
        currentRatioActionsOverImpressions,
        currentTotalActions,
        currentTotalImpressions,
        previousRatioActionsOverImpressions,
        previousTotalActions,
        previousTotalImpressions,
        worstPerformingRestaurant,
    }: GmbInsightsEmailSection): GmbPerformanceReportValidatorProps {
        const mappedGmbEmailSection: GmbPerformanceReportValidatorProps = {
            totalImpressions: {
                totalRated: currentTotalImpressions,
                growth: {
                    rate: roundToDecimals(computeGrowth(currentTotalImpressions, previousTotalImpressions), 1),
                    variation: getGrowthVariation(computeGrowth(currentTotalImpressions, previousTotalImpressions)),
                    isPercentage: true,
                },
            },
            totalActions: {
                totalRated: currentTotalActions,
                growth: {
                    rate: roundToDecimals(computeGrowth(currentTotalActions, previousTotalActions), 1),
                    variation: getGrowthVariation(computeGrowth(currentTotalActions, previousTotalActions)),
                    isPercentage: true,
                },
            },
            variation: getGrowthVariation(computeGrowth(currentTotalActions, previousTotalActions)),
            ratioActionsOverImpressions: {
                totalRated: roundToDecimals(currentRatioActionsOverImpressions, 1),
                growth: {
                    rate: roundToDecimals(currentRatioActionsOverImpressions - previousRatioActionsOverImpressions, 1),
                    variation: getGrowthVariation(currentRatioActionsOverImpressions - previousRatioActionsOverImpressions),
                    isPercentage: true,
                },
            },
            ...(bestPerformingRestaurant && {
                bestPerformingRestaurant: {
                    metaData: {
                        address: bestPerformingRestaurant.address,
                        image: bestPerformingRestaurant.logo,
                        name: bestPerformingRestaurant.restaurantName,
                    },
                    value: bestPerformingRestaurant.currentTotalImpressions,
                },
            }),
            ...(worstPerformingRestaurant && {
                worstPerformingRestaurant: {
                    metaData: {
                        address: worstPerformingRestaurant.address,
                        image: worstPerformingRestaurant.logo,
                        name: worstPerformingRestaurant.restaurantName,
                    },
                    value: worstPerformingRestaurant.currentTotalImpressions,
                },
            }),
        };

        return gmbPerformanceReportValidator.parse(mappedGmbEmailSection);
    }

    private computeTotalOfEachInsightMetrics(restaurantsInsightsDetails: GmbInsightsEmailSection['restaurantsInsightsDetails']) {
        const currentTotalImpressions = restaurantsInsightsDetails.reduce((acc, curr) => acc + curr.currentTotalImpressions, 0);
        const currentTotalActions = restaurantsInsightsDetails.reduce((acc, curr) => acc + curr.currentTotalActions, 0);
        const currentRatioActionsOverImpressions = currentTotalImpressions ? (currentTotalActions / currentTotalImpressions) * 100 : 0;

        const previousTotalImpressions = restaurantsInsightsDetails.reduce((acc, curr) => acc + curr.previousTotalImpressions, 0);
        const previousTotalActions = restaurantsInsightsDetails.reduce((acc, curr) => acc + curr.previousTotalActions, 0);
        const previousRatioActionsOverImpressions = previousTotalImpressions ? (previousTotalActions / previousTotalImpressions) * 100 : 0;

        return {
            currentTotalImpressions,
            currentTotalActions,
            currentRatioActionsOverImpressions,
            previousTotalImpressions,
            previousTotalActions,
            previousRatioActionsOverImpressions,
        };
    }

    private _getBusinessRestaurants(restaurants: SimpleRestaurant[]): SimpleRestaurant[] {
        return restaurants.filter((restaurant) => restaurant.type === BusinessCategory.LOCAL_BUSINESS);
    }

    private async _getPreviousAndCurrentInsightsFromRestaurant({
        restaurant,
        periods,
    }: {
        restaurant: SimpleRestaurant;
        periods: IPerformancePeriods;
    }) {
        const { startDate, endDate } = periods.current;
        const metrics = [
            MalouMetric.BUSINESS_IMPRESSIONS_DESKTOP_MAPS,
            MalouMetric.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH,
            MalouMetric.BUSINESS_IMPRESSIONS_MOBILE_MAPS,
            MalouMetric.BUSINESS_IMPRESSIONS_MOBILE_SEARCH,
            MalouMetric.ACTIONS_BOOKING_CLICK,
            MalouMetric.ACTIONS_DRIVING_DIRECTIONS,
            MalouMetric.ACTIONS_MENU_CLICK,
            MalouMetric.ACTIONS_PHONE,
            MalouMetric.ACTIONS_WEBSITE,
        ];

        const aggregators = [AggregationTimeScale.TOTAL];

        const currentAndPreviousRequestsParams = [
            {
                metrics,
                aggregators,
                startDate,
                endDate,
            },
            {
                metrics,
                aggregators,
                startDate: periods.previous.startDate,
                endDate: periods.previous.endDate,
            },
        ];

        const [currentInsights, previousInsights] = await Promise.all(
            currentAndPreviousRequestsParams.map(async (params) => {
                const insights = await this._gmbInsightsUseCases.getInsightsAggregated(
                    restaurant._id,
                    params.metrics,
                    params.aggregators,
                    new PlatformInsightFiltersGmb(params)
                );

                if (insights.error || !insights.total) {
                    return null;
                }

                const totalImpressions =
                    (insights.total.business_impressions_desktop_maps?.value ?? 0) +
                    (insights.total.business_impressions_desktop_search?.value ?? 0) +
                    (insights.total.business_impressions_mobile_maps?.value ?? 0) +
                    (insights.total.business_impressions_mobile_search?.value ?? 0);
                const totalActions =
                    (insights.total.actions_booking?.value ?? 0) +
                    (insights.total.actions_driving_directions?.value ?? 0) +
                    (insights.total.actions_menu?.value ?? 0) +
                    (insights.total.actions_phone?.value ?? 0) +
                    (insights.total.actions_website?.value ?? 0);
                const ratioActionsOverImpressions = totalImpressions ? totalActions / totalImpressions : 0;

                return {
                    restaurantId: restaurant._id,
                    restaurantName: restaurant.name,
                    address: restaurant.formattedAddress,
                    logo: restaurant.logo,
                    totalImpressions,
                    totalActions,
                    ratioActionsOverImpressions,
                };
            })
        );

        if (!currentInsights || !previousInsights) {
            return null;
        }

        return {
            restaurantId: restaurant._id,
            restaurantName: restaurant.name,
            address: restaurant.formattedAddress,
            logo: restaurant.logo,
            currentTotalImpressions: currentInsights.totalImpressions,
            currentTotalActions: currentInsights.totalActions,
            currentRatioActionsOverImpressions: currentInsights.ratioActionsOverImpressions,
            previousTotalImpressions: previousInsights.totalImpressions,
            previousTotalActions: previousInsights.totalActions,
            previousRatioActionsOverImpressions: previousInsights.ratioActionsOverImpressions,
        };
    }

    private _getBestAndWorstGmbInsightsPerformingRestaurants(
        restaurantsInsightsDetails: GmbInsightsEmailSection['restaurantsInsightsDetails']
    ) {
        if (restaurantsInsightsDetails.length < 2) {
            return {
                bestPerformingRestaurant: undefined,
                worstPerformingRestaurant: undefined,
            };
        }

        restaurantsInsightsDetails.sort((a, b) => b.currentTotalImpressions - a.currentTotalImpressions);

        return {
            bestPerformingRestaurant: restaurantsInsightsDetails[0],
            worstPerformingRestaurant: restaurantsInsightsDetails[restaurantsInsightsDetails.length - 1],
        };
    }
}
