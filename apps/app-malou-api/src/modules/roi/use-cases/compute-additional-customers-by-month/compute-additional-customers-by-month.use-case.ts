import { mean, round, sumBy } from 'lodash';
import { DateTime } from 'luxon';
import { autoInjectable } from 'tsyringe';

import {
    AdditionalCustomersAction,
    getLastXMonthsBeforeCurrentMonth,
    isNotNil,
    PlatformCategory,
    PlatformKey,
    RoiDetailedAdditionalCustomers,
} from '@malou-io/package-utils';

import { getDateIntervalForMonth } from ':helpers/date/date';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { RoiInsightsRepository } from ':modules/roi-insights/roi-insights.repository';
import { RoiSettingsRepository } from ':modules/roi-settings/roi-settings.repository';

import {
    ComputeAdditionalCustomersByPlatformTypeUseCase,
    RoiSeoDetailedAdditionalCustomers,
    RoiSocialDetailedAdditionalCustomers,
} from './compute-additional-customers-by-platform-type.use-case';

@autoInjectable()
export class ComputeAdditionalCustomersByMonthUseCase {
    constructor(
        private readonly _roiSettingsRepository: RoiSettingsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _roiInsightsRepository: RoiInsightsRepository,
        private readonly _computeAdditionalCustomersByPlatformTypeUseCase: ComputeAdditionalCustomersByPlatformTypeUseCase
    ) {}

    async execute({
        restaurantId,
        month,
        year,
        useDefaultRoiSettings = false,
        currentMonthPerformanceScore,
    }: {
        restaurantId: string;
        month: number;
        year: number;
        useDefaultRoiSettings?: boolean;
        currentMonthPerformanceScore: number;
    }): Promise<RoiDetailedAdditionalCustomers | null> {
        const roiSettings = await this._roiSettingsRepository.getRoiSettingByRestaurantId(restaurantId);
        let monthlyCustomers = 0;
        const hasCompleteRoiSettings = !!roiSettings && !!roiSettings.isComplete();
        if (!hasCompleteRoiSettings && !useDefaultRoiSettings) {
            return null;
        }

        if (!hasCompleteRoiSettings) {
            monthlyCustomers = 2500; // used for tasks only
        } else {
            const revenue = mean([roiSettings.minRevenue, roiSettings.maxRevenue]);
            monthlyCustomers = roiSettings.averageTicket ? revenue / roiSettings.averageTicket : 0;
        }

        const { startDate, endDate } = getDateIntervalForMonth({ month, year });

        const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);
        if (!restaurant) {
            return null;
        }
        const restaurantClosedDays = restaurant.specialHours?.filter(
            (specialHour) => specialHour.isClosed && specialHour.startDate.month === month - 1 && specialHour.startDate.year === year
        ).length;
        const openingDate = restaurant.openingDate;

        const seoPlatformsDetails = (await this._computeAdditionalCustomersByPlatformTypeUseCase.execute({
            restaurantId,
            startDate,
            endDate,
            platformType: PlatformCategory.SEO,
            monthlyCustomers,
            openingDate,
        })) as RoiSeoDetailedAdditionalCustomers | null;

        const socialPlatformsDetails = (await this._computeAdditionalCustomersByPlatformTypeUseCase.execute({
            restaurantId,
            startDate,
            endDate,
            platformType: PlatformCategory.SOCIAL,
            monthlyCustomers,
            openingDate,
        })) as RoiSocialDetailedAdditionalCustomers | null;

        if (!seoPlatformsDetails && !socialPlatformsDetails) {
            return null;
        }

        const connectedPlatforms = await this._platformsRepository.getPlatformsByRestaurantIdAndPlatformKeys(restaurantId, [
            PlatformKey.GMB,
            PlatformKey.FACEBOOK,
            PlatformKey.INSTAGRAM,
        ]);
        const isMissingSeoData = connectedPlatforms.find(({ key }) => key === PlatformKey.GMB) && !seoPlatformsDetails;
        const isMissingSocialData =
            connectedPlatforms.find(({ key }) => key === PlatformKey.FACEBOOK || key === PlatformKey.INSTAGRAM) && !socialPlatformsDetails;

        const performanceScore = await this._getPerformanceScoreFor6PreviousMonths({
            restaurantId,
            year,
            month,
            currentMonthPerformanceScore,
        });

        const totalDaysInMonth = DateTime.local(year, month).daysInMonth;
        const openedDaysRatio =
            totalDaysInMonth <= restaurantClosedDays ? 0 : round((totalDaysInMonth - restaurantClosedDays) / totalDaysInMonth, 1);

        const allKeysButTotal: Omit<
            RoiDetailedAdditionalCustomers,
            | 'totalCustomers'
            | 'defaultTotalCustomers'
            | 'socialUsedComputationMethod'
            | 'seoUsedComputationMethod'
            | 'hasCompleteRoiSettings'
            | 'isMissingData'
        > = {
            gmbDirectionRequests: this._applyReducedRatio({
                action: seoPlatformsDetails?.gmbDirectionRequests ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            gmbBooking: this._applyReducedRatio({
                action: seoPlatformsDetails?.gmbBooking ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            gmbPhoneCallClicks: this._applyReducedRatio({
                action: seoPlatformsDetails?.gmbPhoneCallClicks ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            gmbFoodMenuClicks: this._applyReducedRatio({
                action: seoPlatformsDetails?.gmbFoodMenuClicks ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            gmbWebsiteClicks: this._applyReducedRatio({
                action: seoPlatformsDetails?.gmbWebsiteClicks ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            gmbFoodOrders: this._applyReducedRatio({
                action: seoPlatformsDetails?.gmbFoodOrders ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            socialDirectionRequests: this._applyReducedRatio({
                action: socialPlatformsDetails?.socialDirectionRequests ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            socialPhoneCallClicks: this._applyReducedRatio({
                action: socialPlatformsDetails?.socialPhoneCallClicks ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            socialWebsiteClicks: this._applyReducedRatio({
                action: socialPlatformsDetails?.socialWebsiteClicks ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            socialEmailContacts: this._applyReducedRatio({
                action: socialPlatformsDetails?.socialEmailContacts ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            socialMessageSent: this._applyReducedRatio({
                action: socialPlatformsDetails?.socialMessageSent ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            socialImpressions: this._applyReducedRatio({
                action: socialPlatformsDetails?.socialImpressions ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            socialShares: this._applyReducedRatio({
                action: socialPlatformsDetails?.socialShares ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
            socialSaves: this._applyReducedRatio({
                action: socialPlatformsDetails?.socialSaves ?? undefined,
                openedDaysRatio,
                performanceScore,
            }),
        };
        const total = sumBy(Object.values(allKeysButTotal), 'customers');

        return {
            totalCustomers: total,
            defaultTotalCustomers: (seoPlatformsDetails?.defaultTotalCustomers ?? 0) + (socialPlatformsDetails?.defaultTotalCustomers ?? 0),
            socialUsedComputationMethod: socialPlatformsDetails?.usedComputationMethod ?? null,
            seoUsedComputationMethod: seoPlatformsDetails?.usedComputationMethod ?? null,
            hasCompleteRoiSettings, // TODO ROI: for script only, remove when we have RoiSettings for all active restaurants in prod
            isMissingData:
                isMissingSeoData || isMissingSocialData || !!seoPlatformsDetails?.isMissingData || !!socialPlatformsDetails?.isMissingData,
            ...allKeysButTotal,
        };
    }

    private async _getPerformanceScoreFor6PreviousMonths({
        restaurantId,
        year: currentYear,
        month: currentMonth,
        currentMonthPerformanceScore,
    }: {
        restaurantId: string;
        month: number;
        year: number;
        currentMonthPerformanceScore: number;
    }): Promise<number | null> {
        const last5MonthsBeforeCurrentComputeMonth = getLastXMonthsBeforeCurrentMonth(5, { currentYear, currentMonth });
        const roiInsights = await this._roiInsightsRepository.getManyByRestaurantIdAndDate({
            restaurantId,
            dateConditions: last5MonthsBeforeCurrentComputeMonth,
            projection: { performanceScore: 1 },
        });
        const allPerformanceScore = [
            ...roiInsights.map((insight) => insight.performanceScore?.performanceScore),
            currentMonthPerformanceScore,
        ].filter(isNotNil);
        if (!allPerformanceScore.length) {
            return null;
        }
        const meanPerformanceScore = mean(allPerformanceScore);
        return isFinite(meanPerformanceScore) ? meanPerformanceScore : null;
    }

    private _applyReducedRatio({
        action,
        openedDaysRatio,
        performanceScore,
    }: {
        action: AdditionalCustomersAction | undefined;
        openedDaysRatio: number;
        performanceScore: number | null;
    }): AdditionalCustomersAction | null {
        if (!action) {
            return null;
        }
        if (isNotNil(performanceScore)) {
            action.customers = round(action.customers * (performanceScore / 100), 0);
        }
        // reduce if restaurant was closed during month
        return this._applyOpenedDaysRatio({ action, openedDaysRatio });
    }

    private _applyOpenedDaysRatio({
        action,
        openedDaysRatio,
    }: {
        action: AdditionalCustomersAction | undefined;
        openedDaysRatio: number;
    }): AdditionalCustomersAction | null {
        if (!action) {
            return null;
        }
        return {
            ...action,
            customers: round(action.customers * openedDaysRatio, 0),
            ...(openedDaysRatio !== 1 ? { openedDaysRatio } : {}),
        };
    }
}
