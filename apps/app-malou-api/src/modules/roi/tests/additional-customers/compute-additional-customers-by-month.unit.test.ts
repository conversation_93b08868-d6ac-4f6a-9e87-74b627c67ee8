import { ceil } from 'lodash';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, IRestaurantSpecialHour, newDbId } from '@malou-io/package-models';
import { PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatformInsight } from ':modules/platform-insights/tests/platform-insight.builder';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultRoiInsights } from ':modules/roi-insights/tests/roi-insights.builder';
import { getDefaultRoiSettings } from ':modules/roi-settings/tests/roi-settings.builder';
import {
    COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH,
    COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH_50_PERCENT,
    COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH_PERF_SCORE_20,
    COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH_SEO_ONLY,
    COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH_SOCIAL_ONLY,
    LOW_PERFORMANCE_SCORE,
} from ':modules/roi/tests/roi.constants';
import { ComputeAdditionalCustomersByMonthUseCase } from ':modules/roi/use-cases/compute-additional-customers-by-month/compute-additional-customers-by-month.use-case';
import { ComputeAdditionalCustomersByPlatformTypeUseCase } from ':modules/roi/use-cases/compute-additional-customers-by-month/compute-additional-customers-by-platform-type.use-case';

describe('ComputeAdditionalCustomersByMonthUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'PlatformsRepository',
            'PlatformInsightsRepository',
            'RoiSettingsRepository',
            'RoiInsightsRepository',
        ]);
    });

    describe('execute', () => {
        const now = DateTime.now();
        const today = now.toJSDate();
        const currentDaysInMonth = DateTime.now().daysInMonth;
        const closedDays: IRestaurantSpecialHour[] = Array.from({ length: ceil(currentDaysInMonth / 2) }).map((_) => ({
            isClosed: true,
            endDate: { day: today.getDate(), month: today.getMonth(), year: today.getFullYear() },
            openTime: '24:00',
            closeTime: '24:00',
            startDate: { day: today.getDate(), month: today.getMonth(), year: today.getFullYear() },
        }));

        let shouldMockForNextTests = true;
        let computeAdditionalCustomersByMonthUseCase: ComputeAdditionalCustomersByMonthUseCase;
        const computeAdditionalCustomersByPlatformTypeUseCase = container.resolve(ComputeAdditionalCustomersByPlatformTypeUseCase);

        beforeEach(() => {
            class ComputeAdditionalCustomersByPlatformTypeUseCaseMock {
                async execute(props) {
                    return computeAdditionalCustomersByPlatformTypeUseCase.execute(props);
                }
                async getIsMissingData(props) {
                    return shouldMockForNextTests
                        ? { isMissingData: false, hasEnoughData: true }
                        : computeAdditionalCustomersByPlatformTypeUseCase.getIsMissingData(props);
                }
            }
            container.register(ComputeAdditionalCustomersByPlatformTypeUseCase, {
                useValue:
                    new ComputeAdditionalCustomersByPlatformTypeUseCaseMock() as unknown as ComputeAdditionalCustomersByPlatformTypeUseCase,
            });

            computeAdditionalCustomersByMonthUseCase = container.resolve(ComputeAdditionalCustomersByMonthUseCase);
        });

        it('should return nothing if no roi settings for restaurant', async () => {
            const testCase = new TestCaseBuilderV2({
                seeds: {},
                expectedResult() {
                    return null;
                },
            });

            await testCase.build();
            const restaurantId = newDbId().toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await computeAdditionalCustomersByMonthUseCase.execute({
                restaurantId,
                month: today.getMonth() + 1,
                year: today.getFullYear(),
                currentMonthPerformanceScore: 100,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should return nothing if not enough insights', async () => {
            const value = 10;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'platformInsights' | 'roiSettings'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    roiSettings: {
                        data(dependencies) {
                            return [
                                getDefaultRoiSettings()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .averageTicket(1)
                                    .minRevenue(1000)
                                    .maxRevenue(1000)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.FACEBOOK).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.INSTAGRAM).build(),
                            ];
                        },
                    },
                    platformInsights: {
                        data(dependencies) {
                            return Object.values(StoredInDBInsightsMetric)
                                .map((metric) => [
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[0].socialId!)
                                        .platformKey(PlatformKey.GMB)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[1].socialId!)
                                        .platformKey(PlatformKey.FACEBOOK)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[2].socialId!)
                                        .platformKey(PlatformKey.INSTAGRAM)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                ])
                                .flat();
                        },
                    },
                },
                expectedResult() {
                    return null;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await computeAdditionalCustomersByMonthUseCase.execute({
                restaurantId,
                month: today.getMonth() + 1,
                year: today.getFullYear(),
                currentMonthPerformanceScore: 100,
            });

            shouldMockForNextTests = true;

            expect(result).toEqual(expectedResult);
        });

        it('should return valid additional customers object when all platform connected', async () => {
            computeAdditionalCustomersByPlatformTypeUseCase.getIsMissingData = jest
                .fn()
                .mockResolvedValue({ isMissingData: false, hasEnoughData: true });
            const value = 10;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'platformInsights' | 'roiSettings'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    roiSettings: {
                        data(dependencies) {
                            return [
                                getDefaultRoiSettings()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .averageTicket(1)
                                    .minRevenue(1000)
                                    .maxRevenue(1000)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.FACEBOOK).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.INSTAGRAM).build(),
                            ];
                        },
                    },
                    platformInsights: {
                        data(dependencies) {
                            return Object.values(StoredInDBInsightsMetric)
                                .map((metric) => [
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[0].socialId!)
                                        .platformKey(PlatformKey.GMB)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[2].socialId!)
                                        .platformKey(PlatformKey.INSTAGRAM)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                ])
                                .flat();
                        },
                    },
                },
                expectedResult() {
                    return COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await computeAdditionalCustomersByMonthUseCase.execute({
                restaurantId,
                month: today.getMonth() + 1,
                year: today.getFullYear(),
                currentMonthPerformanceScore: 100,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should return valid additional customers object when gmb not connected', async () => {
            computeAdditionalCustomersByPlatformTypeUseCase.getIsMissingData = jest
                .fn()
                .mockResolvedValue({ isMissingData: false, hasEnoughData: true });
            const value = 10;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'platformInsights' | 'roiSettings'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .uniqueKey('facebook_101185912234405')
                                    .specialHours([
                                        {
                                            isClosed: true,
                                            startDate: { day: today.getDate(), month: today.getMonth() + 1, year: today.getFullYear() },
                                            endDate: { day: today.getDate(), month: today.getMonth() + 1, year: today.getFullYear() },
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                    roiSettings: {
                        data(dependencies) {
                            return [
                                getDefaultRoiSettings()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .averageTicket(1)
                                    .minRevenue(1000)
                                    .maxRevenue(1000)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.FACEBOOK).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.INSTAGRAM).build(),
                            ];
                        },
                    },
                    platformInsights: {
                        data(dependencies) {
                            return Object.values(StoredInDBInsightsMetric)
                                .map((metric) => [
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[1].socialId!)
                                        .platformKey(PlatformKey.INSTAGRAM)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                ])
                                .flat();
                        },
                    },
                },
                expectedResult() {
                    return COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH_SOCIAL_ONLY;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await computeAdditionalCustomersByMonthUseCase.execute({
                restaurantId,
                month: today.getMonth() + 1,
                year: today.getFullYear(),
                currentMonthPerformanceScore: 100,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should return valid additional customers object when social platforms are not connected', async () => {
            computeAdditionalCustomersByPlatformTypeUseCase.getIsMissingData = jest
                .fn()
                .mockResolvedValue({ isMissingData: false, hasEnoughData: true });
            const value = 10;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'platformInsights' | 'roiSettings'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    roiSettings: {
                        data(dependencies) {
                            return [
                                getDefaultRoiSettings()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .averageTicket(1)
                                    .minRevenue(1000)
                                    .maxRevenue(1000)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build()];
                        },
                    },
                    platformInsights: {
                        data(dependencies) {
                            return Object.values(StoredInDBInsightsMetric).map((metric) =>
                                getDefaultPlatformInsight()
                                    .socialId(dependencies.platforms()[0].socialId!)
                                    .platformKey(PlatformKey.GMB)
                                    .metric(metric)
                                    .value(value)
                                    .date(today)
                                    .month(today.getMonth() + 1)
                                    .year(today.getFullYear())
                                    .build()
                            );
                        },
                    },
                },
                expectedResult() {
                    return COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH_SEO_ONLY;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await computeAdditionalCustomersByMonthUseCase.execute({
                restaurantId,
                month: today.getMonth() + 1,
                year: today.getFullYear(),
                currentMonthPerformanceScore: 100,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should return ratio of additional customers object when restaurant has closed days during month', async () => {
            computeAdditionalCustomersByPlatformTypeUseCase.getIsMissingData = jest
                .fn()
                .mockResolvedValue({ isMissingData: false, hasEnoughData: true });
            const value = 10;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'platformInsights' | 'roiSettings'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').specialHours(closedDays).build()];
                        },
                    },
                    roiSettings: {
                        data(dependencies) {
                            return [
                                getDefaultRoiSettings()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .averageTicket(1)
                                    .minRevenue(1000)
                                    .maxRevenue(1000)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.FACEBOOK).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.INSTAGRAM).build(),
                            ];
                        },
                    },
                    platformInsights: {
                        data(dependencies) {
                            return Object.values(StoredInDBInsightsMetric)
                                .map((metric) => [
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[0].socialId!)
                                        .platformKey(PlatformKey.GMB)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[2].socialId!)
                                        .platformKey(PlatformKey.INSTAGRAM)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                ])
                                .flat();
                        },
                    },
                },
                expectedResult() {
                    return COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH_50_PERCENT;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await computeAdditionalCustomersByMonthUseCase.execute({
                restaurantId,
                month: today.getMonth() + 1,
                year: today.getFullYear(),
                currentMonthPerformanceScore: 100,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should return lowered result if performance score was under 40 on the last months', async () => {
            computeAdditionalCustomersByPlatformTypeUseCase.getIsMissingData = jest
                .fn()
                .mockResolvedValue({ isMissingData: false, hasEnoughData: true });
            const value = 10;

            const months = Array.from({ length: 3 }).map((_, index) =>
                now
                    .minus({ month: index + 1 })
                    .startOf('month')
                    .startOf('day')
            );

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'platformInsights' | 'roiSettings' | 'roiInsights'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    roiSettings: {
                        data(dependencies) {
                            return [
                                getDefaultRoiSettings()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .averageTicket(1)
                                    .minRevenue(1000)
                                    .maxRevenue(1000)
                                    .build(),
                            ];
                        },
                    },
                    roiInsights: {
                        data(dependencies) {
                            return months.map((month) =>
                                getDefaultRoiInsights()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .month(month.month)
                                    .year(month.year)
                                    .performanceScore(LOW_PERFORMANCE_SCORE)
                                    .build()
                            );
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.FACEBOOK).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.INSTAGRAM).build(),
                            ];
                        },
                    },
                    platformInsights: {
                        data(dependencies) {
                            return Object.values(StoredInDBInsightsMetric)
                                .map((metric) => [
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[0].socialId!)
                                        .platformKey(PlatformKey.GMB)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                    getDefaultPlatformInsight()
                                        .socialId(dependencies.platforms()[2].socialId!)
                                        .platformKey(PlatformKey.INSTAGRAM)
                                        .metric(metric)
                                        .value(value)
                                        .date(today)
                                        .month(today.getMonth() + 1)
                                        .year(today.getFullYear())
                                        .build(),
                                ])
                                .flat();
                        },
                    },
                },
                expectedResult() {
                    return COMPUTED_ADDITIONAL_CUSTOMERS_FOR_MONTH_PERF_SCORE_20;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await computeAdditionalCustomersByMonthUseCase.execute({
                restaurantId,
                month: today.getMonth() + 1,
                year: today.getFullYear(),
                currentMonthPerformanceScore: LOW_PERFORMANCE_SCORE.performanceScore,
            });

            expect(result).toEqual(expectedResult);
        });
    });
});
