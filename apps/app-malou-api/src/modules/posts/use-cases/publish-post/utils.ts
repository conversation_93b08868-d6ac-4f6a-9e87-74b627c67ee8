import { IPost } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { isFbTimeoutError } from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { ASKING_TOO_MUCH_DATA, IMAGE_SIZE_TOO_LARGE_SUBCODE } from ':modules/posts/platforms/facebook/facebook.errors';
import { INSTAGRAM_SERVER_ERROR_RETRY } from ':modules/posts/platforms/instagram/instagram.error';

import { ActionToBeTakenAfterFailure, FacebookError } from './interface';

export const getErrorHandler = (facebookError: FacebookError, post: IPost): ActionToBeTakenAfterFailure => {
    if (post.key === PlatformKey.GMB) {
        return ActionToBeTakenAfterFailure.NoFurtherAction;
    }
    if (facebookError.message?.match(/An unexpected error has occurred\. Please retry your request later\./)) {
        return ActionToBeTakenAfterFailure.ScheduleFetchPostAndCheckErrors;
    }
    const mediaCreationFailedCodes = { code: -1, subcode: 2207032 }; // "Échec de la création du média, veuillez essayer de le recréer"
    const isMediaCreationFailedError =
        facebookError.code === mediaCreationFailedCodes.code && facebookError.error_subcode === mediaCreationFailedCodes.subcode;
    if (
        facebookError.message?.match(new RegExp(INSTAGRAM_SERVER_ERROR_RETRY.toString())) ||
        facebookError.message === ASKING_TOO_MUCH_DATA ||
        facebookError.error_subcode === IMAGE_SIZE_TOO_LARGE_SUBCODE ||
        isMediaCreationFailedError
    ) {
        return ActionToBeTakenAfterFailure.SchedulePreparePost;
    }
    if (facebookError.message?.match(/An unknown error occurred/)) {
        return ActionToBeTakenAfterFailure.ScheduleWrongErrorCheck;
    }
    if (facebookError?.error_subcode === 2207019 && facebookError?.message?.match(/Invalid parameter/)) {
        return ActionToBeTakenAfterFailure.RemoveLocationAndSchedulePreparePost;
    }
    if (isFbTimeoutError(facebookError)) {
        return ActionToBeTakenAfterFailure.ScheduleWrongErrorCheck;
    }
    return ActionToBeTakenAfterFailure.NoFurtherAction;
};
