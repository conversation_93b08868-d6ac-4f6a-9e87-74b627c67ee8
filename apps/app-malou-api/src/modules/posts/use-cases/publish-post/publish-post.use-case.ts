import assert from 'assert';
import { Message, MessageBodyAttributeMap } from 'aws-sdk/clients/sqs';
import { DateTime } from 'luxon';
import { SQSMessage } from 'sqs-consumer';
import { singleton } from 'tsyringe';

import { IPost, toDbId } from '@malou-io/package-models';
import { EmailCategory, EmailType, MalouErrorCode, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import MailingUseCases from ':modules/mailing/use-cases';
import { MediasUseCases } from ':modules/media/medias.use-cases';
import { InstagramPostsUseCases } from ':modules/posts/platforms/instagram/use-cases';
import PostsRepository from ':modules/posts/posts.repository';
import PostsUseCases from ':modules/posts/posts.use-cases';

import { ActionToBeTakenAfterFailure, FacebookError } from './interface';
import { getErrorHandler } from './utils';

@singleton()
export class PublishPostUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _mediasUseCases: MediasUseCases,
        private readonly _mailingUseCases: MailingUseCases,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _igPostsUseCases: InstagramPostsUseCases
    ) {}

    public async handlePublishPost(postId: string, messageAttributes: MessageBodyAttributeMap): Promise<void> {
        try {
            logger.info('[PublishPostUseCase] publishing', { messageAttributes: messageAttributes, postId });
            await this._postsUseCases.publishPost({ postId });
            logger.info('[PublishPostUseCase] published', { messageAttributes: messageAttributes, postId });
            await this._mediasUseCases.updateMediaPostIds(postId, { $addToSet: { postIds: toDbId(postId) } });
        } catch (e: any) {
            logger.warn('Failed log first basic', { messageAttributes: messageAttributes, postId, error: e });
            const post = await this._postsRepository.findOne({
                filter: { _id: toDbId(postId) },
                options: { lean: true },
            });
            assert(post, 'Post not found');
            await this._handlePublishError(e, post, messageAttributes);
        }
    }

    public async handleCompletePublishPost(msg: SQSMessage): Promise<void> {
        logger.info('[PublishPostUseCase] handleCompletePublishPost', msg);
        const body = JSON.parse(msg.Body ?? '{}');
        const { creationId, postId, postSocialId } = body;
        try {
            const post = await this._postsRepository.findOne({ filter: { _id: postId }, options: { lean: true } });
            logger.info('First log complete', { messageAttributes: msg?.MessageAttributes, body: msg?.Body, post });
            assert(post, 'Post not found');
            switch (post.key) {
                case PlatformKey.FACEBOOK:
                    await this._postsUseCases.getPlatformPostUseCases(PlatformKey.FACEBOOK).completePublish({ postSocialId, postId });
                    logger.info('[FB] - Second log complete fb', {
                        messageAttributes: msg?.MessageAttributes,
                        body: msg?.Body,
                        post,
                    });
                    await this._postsRepository.updateOne({
                        filter: { _id: postId },
                        update: {
                            published: PostPublicationStatus.PUBLISHED,
                            errorData: null,
                            errorStage: null,
                            isPublishing: false,
                        },
                    });
                    return;
                case PlatformKey.INSTAGRAM:
                    await this._igPostsUseCases.completePublish({ creationId, postId });
                    logger.info('[IG] - Second log complete ig', {
                        messageAttributes: msg?.MessageAttributes,
                        body: msg?.Body,
                        post,
                    });
                    return;
                default:
                    return;
            }
        } catch (e) {
            const post = await this._postsRepository.findOne({ filter: { _id: postId }, options: { lean: true } });
            assert(post, 'Post not found');
            await this._handleCompletePublishError(e, post, msg);
        }
    }

    private async _handlePublishError(
        facebookError: FacebookError,
        post: IPost,
        messageAttributes: MessageBodyAttributeMap
    ): Promise<void> {
        try {
            const actionToBeTaken = getErrorHandler(facebookError, post);
            const agenda = await this._agendaSingleton.getInstance();
            switch (actionToBeTaken) {
                case ActionToBeTakenAfterFailure.ScheduleFetchPostAndCheckErrors:
                    const in30seconds = DateTime.now().plus({ seconds: 30 }).toJSDate();
                    const scheduleFetchPostAndCheckErrorsJobData = { postId: toDbId(post._id) };
                    logger.warn('[PUBLISH_POST_RESCHEDULE] - Failed log reschedule "fetch post and check errors"', {
                        jobData: scheduleFetchPostAndCheckErrorsJobData,
                        post,
                        errorMessage: facebookError.message,
                    });
                    await this._agendaSingleton.schedule(
                        in30seconds,
                        AgendaJobName.FETCH_POST_AND_CHECK_ERRORS,
                        scheduleFetchPostAndCheckErrorsJobData
                    );
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: {
                            published: PostPublicationStatus.PENDING,
                            errorData: facebookError?.message,
                            errorStage: AgendaJobName.PUBLISH_POST,
                        },
                    });
                    break;
                case ActionToBeTakenAfterFailure.SchedulePreparePost:
                    const in15minutes = DateTime.now().plus({ minutes: 15 }).toJSDate();
                    const schedulePreparePostJobData = {
                        userId: post.author?._id,
                        postId: post._id,
                        restaurantId: post.restaurantId,
                    };
                    logger.warn('[PUBLISH_POST_RESCHEDULE] - Failed log reschedule "prepare post"', {
                        jobData: schedulePreparePostJobData,
                        post,
                        errorMessage: facebookError.message,
                    });
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: { plannedPublicationDate: in15minutes },
                    });
                    await agenda.schedule(in15minutes, AgendaJobName.PREPARE_POST, schedulePreparePostJobData);
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: {
                            published: PostPublicationStatus.PENDING,
                            errorData: facebookError?.message,
                            errorStage: AgendaJobName.PUBLISH_POST,
                        },
                    });
                    break;
                case ActionToBeTakenAfterFailure.ScheduleWrongErrorCheck:
                    const in5minutes = DateTime.now().plus({ seconds: 5 }).toJSDate();
                    const scheduleWrongErrorCheckJobData = { postId: toDbId(post._id) };
                    logger.warn('[PUBLISH_POST_RESCHEDULE] - Failed log reschedule CHECK_IF_WRONG_ERROR_AND_UPDATE_ACCORDINGLY', {
                        jobData: scheduleWrongErrorCheckJobData,
                        post,
                        errorMessage: facebookError.message,
                    });
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: { plannedPublicationDate: in5minutes },
                    });
                    await this._agendaSingleton.schedule(
                        in5minutes,
                        AgendaJobName.CHECK_IF_WRONG_ERROR_AND_UPDATE_ACCORDINGLY,
                        scheduleWrongErrorCheckJobData
                    );
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: {
                            published: PostPublicationStatus.PENDING,
                            errorData: facebookError?.message,
                            errorStage: AgendaJobName.PUBLISH_POST,
                        },
                    });
                    break;
                case ActionToBeTakenAfterFailure.RemoveLocationAndSchedulePreparePost:
                    const userId = post.author?._id;
                    assert(userId);
                    const removeLocationAndSchedulePreparePostJobData = {
                        userId,
                        postId: post._id,
                        restaurantId: post.restaurantId,
                    };
                    await this._postsRepository.updateOne({ filter: { _id: post._id }, update: { location: null } });
                    logger.warn('[PUBLISH_POST_RESCHEDULE] - Failed log reschedule now "prepare post"', {
                        jobData: removeLocationAndSchedulePreparePostJobData,
                        post,
                        errorMessage: facebookError.message,
                    });
                    await this._agendaSingleton.now(AgendaJobName.PREPARE_POST, removeLocationAndSchedulePreparePostJobData);
                    await this._mailingUseCases.sendEmail(EmailCategory.POST_NOTIFICATION, EmailType.POST_LOCATION_EXPIRED, {
                        userId: userId.toString(),
                        post,
                        restaurantId: post.restaurantId.toString(),
                    });
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: {
                            published: PostPublicationStatus.PENDING,
                            errorData: facebookError?.message,
                            errorStage: AgendaJobName.PUBLISH_POST,
                        },
                    });
                    break;
                case ActionToBeTakenAfterFailure.NoFurtherAction:
                default:
                    await this._mediasUseCases.updateMediaPostIds(post._id, { $pull: { postIds: post._id } });
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: {
                            published: PostPublicationStatus.ERROR,
                            errorData: facebookError?.message,
                            errorStage: AgendaJobName.PUBLISH_POST,
                            isPublishing: false,
                        },
                    });
                    await this._postsUseCases.sendPublicationFailedEmail(post._id);
            }
            logger.warn('[PUBLISH_POST_BASIC] _handlePublishError end', {
                messageAttributes,
                postId: post._id,
                error: facebookError,
            });
            await this._mediasUseCases.updateMediaPostIds(post._id, { $pull: { postIds: post._id } });
        } catch (error) {
            logger.warn('[ERROR_HANDLE_PUBLISH]', { error });
        }
    }

    private async _handleCompletePublishError(error: any, post: IPost, msg: Message): Promise<void> {
        try {
            const errorData =
                error?.malouErrorCode === MalouErrorCode.MEDIA_NOT_READY_MAX_RETRIES_REACHED
                    ? 'Publishing the post took too long'
                    : `message::${error?.message}--fbCode::${error?.code}--fbSubcode::${error?.error_subcode}`;
            switch (post.key) {
                case PlatformKey.INSTAGRAM:
                    logger.warn('[COMPLETE_PUBLISH_POST][IG] - Failed log complete ig', {
                        messageAttributes: msg?.MessageAttributes,
                        body: msg?.Body,
                        error,
                    });
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: {
                            published: PostPublicationStatus.ERROR,
                            errorData,
                            errorStage: 'complete publish ig post',
                            isPublishing: false,
                        },
                    });
                    await this._postsUseCases.sendPublicationFailedEmail(post._id);
                    break;
                case PlatformKey.FACEBOOK:
                    logger.warn('[COMPLETE_PUBLISH_POST][FB] - Failed log complete fb', {
                        messageAttributes: msg?.MessageAttributes,
                        body: msg?.Body,
                        error,
                    });
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: {
                            published: PostPublicationStatus.PUBLISHED,
                            errorData,
                            errorStage: 'complete publish fb post',
                            isPublishing: false,
                        },
                    });
                    await this._postsUseCases.sendPublicationFailedEmail(post._id);
                    break;
                default:
                    logger.info('[COMPLETE_PUBLISH_ERROR_UNHANDLED_PLATFORM]', {
                        body: msg?.Body,
                    });
                    await this._postsUseCases.sendPublicationFailedEmail(post._id);
            }
        } catch (err) {
            // TODO: Check if we need to catch or throw the error
            logger.warn('[ERROR_HANDLE_COMPLETE_PUBLISH]', {
                messageAttributes: msg?.MessageAttributes,
                body: msg?.Body,
                error: err,
            });
        }
    }
}
