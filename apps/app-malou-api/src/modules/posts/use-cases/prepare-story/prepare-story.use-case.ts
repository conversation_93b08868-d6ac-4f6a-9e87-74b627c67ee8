import assert from 'node:assert';
import { singleton } from 'tsyringe';
import { v4 } from 'uuid';

import { newDbId, toDbId } from '@malou-io/package-models';
import { MediaCategory, MediaType } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { MediaUploaderService } from ':modules/media/services/media-uploader/media-uploader.service';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformPostGetter } from ':modules/posts/platforms/getter';
import { StoryToPublish } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import { FormatterType, MediaFormatter } from ':modules/posts/services/formatters/media-formatter.port';

@singleton()
export class PrepareStoryUseCase {
    readonly STORY_MEDIA_BITRATE_LIMIT = 20_000_000;

    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _platformPostGetter: PlatformPostGetter,
        private readonly _mediaUploaderService: MediaUploaderService,
        private readonly _mediasRepository: MediasRepository
    ) {}

    async execute(restaurantId: string, malouStoryId: string, platformKey: string): Promise<void> {
        const platform = await this._platformsRepository.findOne({
            filter: { restaurantId, key: platformKey },
            options: { lean: true },
        });
        assert(platform, 'Platform not found');

        const posts = await this._postsRepository.find({
            filter: { isStory: true, malouStoryId, key: platformKey },
            options: { lean: true, populate: [{ path: 'attachments' }], sort: { plannedPublicationDate: 1 } },
        });
        logger.info('[PREPARE_STORY]', { malouStoryId, posts });

        assert(
            posts?.length,
            `[PREPARE_STORY] No post to prepare found for story malouStoryId : ${malouStoryId} - platform : ${platformKey} - restaurantId - ${restaurantId}`
        );

        for (const post of posts) {
            await this._formatStoryBeforePublishing(post._id.toString());
        }

        const updatedPosts = await this._postsRepository.find({
            filter: { isStory: true, malouStoryId, key: platformKey },
            options: { lean: true, populate: [{ path: 'attachments' }], sort: { plannedPublicationDate: 1 } },
        });

        const creations: StoryToPublish[] = await this._platformPostGetter
            .getPlatformPostUseCases(platform.key)
            .createStoryList(platform, updatedPosts);

        void this._agendaSingleton.now(AgendaJobName.PUBLISH_STORY, { storyList: creations, platformId: platform._id });
    }

    private async _formatStoryBeforePublishing(postId: string): Promise<void> {
        const story = await this._postsRepository.findOne({
            filter: { _id: postId },
            options: { lean: true, populate: [{ path: 'attachments' }] },
        });
        assert(story, 'Story not found');
        assert(story.key, "Story's key not found");

        const storyMedia = story.attachments[0];
        if (storyMedia.type !== MediaType.VIDEO) {
            return;
        }

        const mediaUrl = storyMedia.urls.original;

        const media = new Media({
            id: storyMedia._id.toString(),
            restaurantId: story.restaurantId.toString(),
            duplicatedFromRestaurantId: story.duplicatedFromRestaurantId?.toString(),
            postIds: storyMedia.postIds?.map((a) => a._id.toString()),
            ...storyMedia,
        } as any);

        const { pathName: inputFilePath } = await this._mediaUploaderService.downloadMediaFromUrl(mediaUrl);
        const outputFilePath = `./downloadedMedias/${postId}_${v4()}_resized.${media.getOriginalImageExtension()}`;

        const mediaFormatter = new MediaFormatter(media, FormatterType.STORY);

        logger.info('[PREPARE_STORY] resizing video', { malouStoryId: story.malouStoryId, postId });

        const resizedMediaMetadata = await mediaFormatter.formatMedia(
            inputFilePath,
            outputFilePath,
            story.key,
            storyMedia.type as MediaType
        );

        const mediaToCreate = {
            ...media,
            id: newDbId().toString(),
            restaurantId: media.restaurantId?.toString(),
            userId: media.userId?.toString(),
            postIds: media.postIds?.map((pid) => pid.toString()),
            originalMediaId: media.id?.toString(),
            dimensions: {
                original: {
                    width: resizedMediaMetadata.width,
                    height: resizedMediaMetadata.height,
                },
            },
            resizeMetadata: {
                width: resizedMediaMetadata.width,
                height: resizedMediaMetadata.height,
                aspectRatio: resizedMediaMetadata.width / resizedMediaMetadata.height,
                cropPosition: { left: 0, top: 0 },
            },
            category: media.category ?? MediaCategory.ADDITIONAL,
            createdAt: new Date(),
            updatedAt: new Date(),
            folderId: null,
            socialId: '',
        };

        const uploadedMedia = await this._mediaUploaderService.uploadFromFile(resizedMediaMetadata, mediaToCreate, {
            keepOriginalAsIgFit: true,
        });

        const createdMedia = await this._mediasRepository.createMedia(new Media({ ...mediaToCreate, ...uploadedMedia }));

        await this._postsRepository.updateOne({
            filter: { _id: postId },
            update: { attachments: [toDbId(createdMedia.id)] },
        });
    }
}
