import { singleton } from 'tsyringe';

import { AiSeoPostDuplicationCaptionResponseDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { HeapEventName, MalouErrorCode, PostPublicationStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { DuplicateSeoTextService } from ':modules/ai/services/duplicate-seo-text.service';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import PostsRepository from ':modules/posts/posts.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { HeapAnalyticsService } from ':plugins/heap-analytics';

@singleton()
export class DuplicateSeoPostWithAiUseCase {
    readonly MAX_ALLOWED_RESTAURANTS = 150;
    constructor(
        private readonly _duplicateSeoTextService: DuplicateSeoTextService,
        private readonly _postsRepository: PostsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _eventTrackingService: HeapAnalyticsService,
        private readonly _usersRepository: UsersRepository
    ) {}

    async execute({
        restaurantIds,
        postIdToDuplicate,
        userId,
    }: {
        restaurantIds: string[];
        postIdToDuplicate: string;
        userId: string;
    }): Promise<AiSeoPostDuplicationCaptionResponseDto> {
        const post = await this._postsRepository.findOne({
            filter: { _id: toDbId(postIdToDuplicate) },
            options: { lean: true, select: { text: 1, language: 1, restaurantId: 1, platformId: 1 } },
        });
        if (!post) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                message: 'No post found for seo duplication',
                metadata: { postIdToDuplicate, restaurantIds, userId },
            });
        }
        const restaurantId = post.restaurantId;

        const postRestaurant = await this._restaurantsRepository.findOne({
            filter: { _id: toDbId(restaurantId) },
            options: { lean: true, select: { _id: 1, name: 1, address: 1, ai: 1 } },
        });
        if (!postRestaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                message: 'No restaurant found for seo duplication',
                metadata: { postIdToDuplicate, restaurantIds, userId, platformId: post.platformId, restaurantId },
            });
        }
        if (post.published !== PostPublicationStatus.DRAFT) {
            const user = await this._usersRepository.findOne({
                filter: { _id: toDbId(userId) },
                projection: { email: 1 },
                options: { lean: true },
            });
            this._eventTrackingService.track({
                eventName: HeapEventName.DUPLICATE_POSTS,
                identity: userId,
                properties: {
                    duplicatedFromRestaurantId: post.restaurantId,
                    toRestaurantIds: restaurantIds,
                    platforms: post.keys,
                    userEmail: user?.email,
                },
            });
        }
        return Promise.all(
            restaurantIds.slice(0, this.MAX_ALLOWED_RESTAURANTS).map((restId) =>
                this._duplicateSeoTextService.execute({
                    post,
                    postRestaurant,
                    restaurantId: restId,
                    userId,
                })
            )
        );
    }
}
