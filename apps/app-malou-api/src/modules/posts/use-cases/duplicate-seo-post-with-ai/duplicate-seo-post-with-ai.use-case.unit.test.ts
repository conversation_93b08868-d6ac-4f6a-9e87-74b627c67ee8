import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { MalouErrorCode } from '@malou-io/package-utils';

import ':helpers/tests/testing-utils';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { AiPostDuplicationResponse, AiTextDuplicationService } from ':microservices/ai-text-duplication.service';
import { DuplicateSeoPostTextPayload } from ':modules/ai/interfaces/ai.interfaces';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { DuplicateSeoPostWithAiUseCase } from ':modules/posts/use-cases/duplicate-seo-post-with-ai/duplicate-seo-post-with-ai.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('DuplicateSeoPostWithAiUseCase', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository', 'RestaurantsRepository', 'PlatformsRepository']);
    });

    const duplicatedPostCaption = 'A duplicated post caption';

    beforeEach(() => {
        class AiTextDuplicationServiceMock {
            generateDuplication(payload: DuplicateSeoPostTextPayload): Promise<GenericAiServiceResponseType<AiPostDuplicationResponse>> {
                return Promise.resolve({
                    aiResponse: {
                        restaurantName: payload.restaurantData.duplicator.restaurantName,
                        postDescription: `${duplicatedPostCaption} ${payload.restaurantData.duplicator.restaurantName}`,
                    },
                    aiInteractionDetails: [],
                });
            }
        }
        container.register(AiTextDuplicationService, { useValue: new AiTextDuplicationServiceMock() as any });
    });

    it('should throw POST_NOT_FOUND error if post not found', async () => {
        const duplicateSeoPostWithAiUseCase = container.resolve(DuplicateSeoPostWithAiUseCase);

        const testCase = new TestCaseBuilderV2({
            seeds: {},
            expectedErrorCode: MalouErrorCode.POST_NOT_FOUND,
        });
        await testCase.build();
        const expectedErrorCode = testCase.getExpectedErrorCode();

        await expect(
            duplicateSeoPostWithAiUseCase.execute({
                restaurantIds: [],
                postIdToDuplicate: newDbId().toString(),
                userId: newDbId().toString(),
            })
        ).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });

    it('should throw RESTAURANT_NOT_FOUND error if post restaurant not found', async () => {
        const duplicateSeoPostWithAiUseCase = container.resolve(DuplicateSeoPostWithAiUseCase);

        const testCase = new TestCaseBuilderV2<'platforms' | 'posts'>({
            seeds: {
                platforms: {
                    data() {
                        return [getDefaultPlatform().build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().platformId(dependencies.platforms()[0]._id).build()];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.RESTAURANT_NOT_FOUND,
        });
        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const expectedErrorCode = testCase.getExpectedErrorCode();
        const postId = seededObjects.posts[0]?._id as DbId;

        await expect(
            duplicateSeoPostWithAiUseCase.execute({
                restaurantIds: [],
                postIdToDuplicate: postId.toString(),
                userId: newDbId().toString(),
            })
        ).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });

    it('should return list of post captions with restaurants informations', async () => {
        const duplicateSeoPostWithAiUseCase = container.resolve(DuplicateSeoPostWithAiUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().name('Restaurant 1').build(),
                            getDefaultRestaurant().name('Restaurant 2').build(),
                            getDefaultRestaurant().name('Restaurant 3').build(),
                            getDefaultRestaurant().name('Restaurant 4').build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).text('A nice post caption').build()];
                    },
                },
            },
            expectedResult: (dependencies) => {
                return [
                    {
                        restaurantId: dependencies.restaurants[1]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[1].name}`,
                        restaurantKeywords: [],
                    },
                    {
                        restaurantId: dependencies.restaurants[2]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[2].name}`,
                        restaurantKeywords: [],
                    },
                    {
                        restaurantId: dependencies.restaurants[3]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[3].name}`,
                        restaurantKeywords: [],
                    },
                ];
            },
        });
        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();
        const postId = seededObjects.posts[0]?._id as DbId;
        const restaurantIds = seededObjects.restaurants.map((restaurant) => restaurant._id.toString()).filter((_, index) => index !== 0);

        const result = await duplicateSeoPostWithAiUseCase.execute({
            restaurantIds,
            postIdToDuplicate: postId.toString(),
            userId: newDbId().toString(),
        });

        expect(result).toEqual(expectedResult);
    });

    it('should return list limited to 150 restaurants', async () => {
        const duplicateSeoPostWithAiUseCase = container.resolve(DuplicateSeoPostWithAiUseCase);

        const maxAcceptedRestaurants = duplicateSeoPostWithAiUseCase.MAX_ALLOWED_RESTAURANTS;
        const arrayOf160 = Array.from({ length: duplicateSeoPostWithAiUseCase.MAX_ALLOWED_RESTAURANTS + 10 });

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().name('Restaurant 1').build(),
                            ...arrayOf160.map((_, index) => getDefaultRestaurant().name(`Restaurant ${index}`).build()),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).text('A nice post caption').build()];
                    },
                },
            },
            expectedResult: (dependencies) => {
                return arrayOf160
                    .map((_, index) => ({
                        restaurantId: dependencies.restaurants[index + 1]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[index + 1].name}`,
                        restaurantKeywords: [],
                    }))
                    .filter((_, index) => index < maxAcceptedRestaurants);
            },
        });
        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();
        const postId = seededObjects.posts[0]?._id as DbId;
        const restaurantIds = seededObjects.restaurants.map((restaurant) => restaurant._id.toString()).filter((_, index) => index !== 0);

        const result = await duplicateSeoPostWithAiUseCase.execute({
            restaurantIds,
            postIdToDuplicate: postId.toString(),
            userId: newDbId().toString(),
        });

        expect(result.length).toEqual(maxAcceptedRestaurants);
        expect(result).toEqual(expectedResult);
    });
});
