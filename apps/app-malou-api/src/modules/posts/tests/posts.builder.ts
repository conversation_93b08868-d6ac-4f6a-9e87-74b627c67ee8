import { Builder } from 'builder-pattern';

import { IPost, newDbId } from '@malou-io/package-models';
import { CallToActionType, PostPublicationStatus, PostSource, PostType, SeoPostTopic } from '@malou-io/package-utils';

type PostPayload = IPost;

const _buildPost = (post: PostPayload) => Builder<PostPayload>(post);

export const getDefaultPost = () =>
    _buildPost({
        _id: newDbId(),
        platformId: newDbId(),
        keys: [],
        author: undefined,
        postTopic: SeoPostTopic.STANDARD,
        postType: PostType.IMAGE,
        isStory: false,
        shouldDuplicateInOtherPlatforms: false,
        socialId: 'Random string id',
        socialCreatedAt: new Date(),
        socialLink: 'https://www.google.com',
        socialUpdatedAt: new Date(),
        published: PostPublicationStatus.PENDING,
        restaurantId: newDbId(),
        source: PostSource.SEO,
        createdAt: new Date(),
        updatedAt: new Date(),
        callToAction: {
            actionType: CallToActionType.SHOP,
            url: 'https://www.google.com',
        },
        attachments: [],
        sortDate: new Date(),
        instagramCollaboratorsUsernames: [],
    });
