import { ok } from 'neverthrow';
import { container } from 'tsyringe';

import { SocialPostDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { CaslRole, PlatformKey, PostPublicationStatus, PostType, TiktokPrivacyStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import { CreateSocialPostUseCase } from ':modules/posts/v2/use-cases/create-social-post/create-social-post.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';

describe('CreateSocialPostUseCase', () => {
    const fakePageId = 'fakePageId';
    const defaultLocation = {
        id: fakePageId,
        name: 'fakePageName',
        link: 'http://fake-page.link',
        location: {
            city: 'fakeCity',
            country: 'fakeCountry',
            latitude: 10.12,
            longitude: -12.45,
            street: 'fakeStreet',
            zip: '12345',
        },
    };

    beforeAll(() => {
        registerRepositories(['PlatformsRepository', 'RestaurantsRepository', 'UsersRepository', 'UserRestaurantsRepository']);
        const metaGraphApiHelperMock: Partial<MetaGraphApiHelper> = {
            fetchPageLocation: jest.fn().mockResolvedValue(ok(defaultLocation)),
        };
        container.registerInstance(MetaGraphApiHelper, metaGraphApiHelperMock as MetaGraphApiHelper);
    });

    it('should create a post without platforms and init all default values', async () => {
        const createSocialPostUseCase = container.resolve(CreateSocialPostUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): SocialPostDto => {
                return {
                    id: expect.any(String),
                    title: undefined,
                    text: '',
                    platformKeys: [],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    postType: PostType.IMAGE,
                    plannedPublicationDate: expect.any(String),
                    attachments: [],
                    hashtags: {
                        selected: [],
                        suggested: [],
                    },
                    location: null,
                    feedbacks: null,
                    callToAction: undefined,
                    error: undefined,
                    socialCreatedAt: undefined,
                    socialLink: undefined,
                    author: {
                        id: dependencies.users[0]._id.toString(),
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname ?? undefined,
                        picture: undefined,
                    },
                    userTagsList: [],
                    bindingId: expect.any(String),
                    tiktokOptions: {
                        privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                        interactionAbility: {
                            comment: false,
                            duet: false,
                            stitch: false,
                        },
                        contentDisclosureSettings: {
                            isActivated: false,
                            yourBrand: false,
                            brandedContent: false,
                        },
                    },
                    instagramCollaboratorsUsernames: [],
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const expectedResult = testCase.getExpectedResult();

        const restaurantId = restaurant?._id.toString();
        const createdPost = await createSocialPostUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            isReel: false,
        });

        expect(createdPost).toEqual(expectedResult);
    });

    it('should create a post without non social platforms and init all default values', async () => {
        const createSocialPostUseCase = container.resolve(CreateSocialPostUseCase);

        const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform().key(PlatformKey.GMB).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.TRIPADVISOR).restaurantId(dependencies.restaurants()[0]._id).build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): SocialPostDto => {
                return {
                    id: expect.any(String),
                    title: undefined,
                    text: '',
                    platformKeys: [],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    postType: PostType.IMAGE,
                    plannedPublicationDate: expect.any(String),
                    attachments: [],
                    hashtags: {
                        selected: [],
                        suggested: [],
                    },
                    location: null,
                    feedbacks: null,
                    callToAction: undefined,
                    error: undefined,
                    socialCreatedAt: undefined,
                    socialLink: undefined,
                    author: {
                        id: dependencies.users[0]._id.toString(),
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname ?? undefined,
                        picture: undefined,
                    },
                    userTagsList: [],
                    bindingId: expect.any(String),
                    tiktokOptions: {
                        privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                        interactionAbility: {
                            comment: false,
                            duet: false,
                            stitch: false,
                        },
                        contentDisclosureSettings: {
                            isActivated: false,
                            yourBrand: false,
                            brandedContent: false,
                        },
                    },
                    instagramCollaboratorsUsernames: [],
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const expectedResult = testCase.getExpectedResult();

        const restaurantId = restaurant._id.toString();
        const createdPost = await createSocialPostUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            isReel: false,
        });

        expect(createdPost).toEqual(expectedResult);
    });

    it('should create a post with social platforms and init all default values', async () => {
        const createSocialPostUseCase = container.resolve(CreateSocialPostUseCase);

        const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform().key(PlatformKey.GMB).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.TRIPADVISOR).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.FACEBOOK).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.INSTAGRAM).restaurantId(dependencies.restaurants()[0]._id).build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): SocialPostDto => {
                return {
                    id: expect.any(String),
                    title: undefined,
                    text: '',
                    platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    postType: PostType.IMAGE,
                    plannedPublicationDate: expect.any(String),
                    attachments: [],
                    hashtags: {
                        selected: [],
                        suggested: [],
                    },
                    location: null,
                    feedbacks: null,
                    callToAction: undefined,
                    error: undefined,
                    socialCreatedAt: undefined,
                    socialLink: undefined,
                    author: {
                        id: dependencies.users[0]._id.toString(),
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname ?? undefined,
                        picture: undefined,
                    },
                    userTagsList: [],
                    bindingId: expect.any(String),
                    tiktokOptions: {
                        privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                        interactionAbility: {
                            comment: false,
                            duet: false,
                            stitch: false,
                        },
                        contentDisclosureSettings: {
                            isActivated: false,
                            yourBrand: false,
                            brandedContent: false,
                        },
                    },
                    instagramCollaboratorsUsernames: [],
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const expectedResult = testCase.getExpectedResult();

        const restaurantId = restaurant._id.toString();
        const createdPost = await createSocialPostUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            isReel: false,
        });

        expect(createdPost).toEqual(expectedResult);
    });

    it('should create a post with Facebook and a default location', async () => {
        const createSocialPostUseCase = container.resolve(CreateSocialPostUseCase);

        const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform()
                                .key(PlatformKey.FACEBOOK)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .socialId(fakePageId)
                                .credentials([newDbId()])
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): SocialPostDto => {
                return {
                    id: expect.any(String),
                    title: undefined,
                    text: '',
                    platformKeys: [PlatformKey.FACEBOOK],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    postType: PostType.IMAGE,
                    plannedPublicationDate: expect.any(String),
                    attachments: [],
                    hashtags: {
                        selected: [],
                        suggested: [],
                    },
                    location: defaultLocation,
                    feedbacks: null,
                    callToAction: undefined,
                    error: undefined,
                    socialCreatedAt: undefined,
                    socialLink: undefined,
                    author: {
                        id: dependencies.users[0]._id.toString(),
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname ?? undefined,
                        picture: undefined,
                    },
                    userTagsList: [],
                    bindingId: expect.any(String),
                    tiktokOptions: {
                        privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                        interactionAbility: {
                            comment: false,
                            duet: false,
                            stitch: false,
                        },
                        contentDisclosureSettings: {
                            isActivated: false,
                            yourBrand: false,
                            brandedContent: false,
                        },
                    },
                    instagramCollaboratorsUsernames: [],
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const expectedResult = testCase.getExpectedResult();

        const restaurantId = restaurant._id.toString();
        const createdPost = await createSocialPostUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            isReel: false,
        });

        expect(createdPost).toEqual(expectedResult);
    });

    it('should create a reel with social platforms and init all default values', async () => {
        const createSocialPostUseCase = container.resolve(CreateSocialPostUseCase);

        const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform().key(PlatformKey.GMB).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.TRIPADVISOR).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.FACEBOOK).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.INSTAGRAM).restaurantId(dependencies.restaurants()[0]._id).build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): SocialPostDto => {
                return {
                    id: expect.any(String),
                    title: undefined,
                    text: '',
                    platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    postType: PostType.REEL,
                    plannedPublicationDate: expect.any(String),
                    attachments: [],
                    hashtags: {
                        selected: [],
                        suggested: [],
                    },
                    location: null,
                    feedbacks: null,
                    callToAction: undefined,
                    error: undefined,
                    socialCreatedAt: undefined,
                    socialLink: undefined,
                    author: {
                        id: dependencies.users[0]._id.toString(),
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname ?? undefined,
                        picture: undefined,
                    },
                    userTagsList: [],
                    bindingId: expect.any(String),
                    tiktokOptions: {
                        privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                        interactionAbility: {
                            comment: false,
                            duet: false,
                            stitch: false,
                        },
                        contentDisclosureSettings: {
                            isActivated: false,
                            yourBrand: false,
                            brandedContent: false,
                        },
                    },
                    instagramCollaboratorsUsernames: [],
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const expectedResult = testCase.getExpectedResult();

        const restaurantId = restaurant._id.toString();
        const createdPost = await createSocialPostUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            isReel: true,
        });

        expect(createdPost).toEqual(expectedResult);
    });
});
