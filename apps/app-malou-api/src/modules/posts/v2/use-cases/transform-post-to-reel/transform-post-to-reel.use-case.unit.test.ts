import { container } from 'tsyringe';

import { SocialPostDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { MediaType, PostPublicationStatus, PostSource, PostType, TiktokPrivacyStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultMedia } from ':modules/media/tests/media.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { TransformPostToReelUseCase } from ':modules/posts/v2/use-cases/transform-post-to-reel/transform-post-to-reel.use-case';

describe('TransformPostToReelUseCase', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository', 'MediasRepository']);
    });

    it('should transform a post to a reel', async () => {
        const testCase = new TestCaseBuilderV2({
            seeds: {
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .source(PostSource.SOCIAL)
                                .postType(PostType.CAROUSEL)
                                .published(PostPublicationStatus.DRAFT)
                                .socialLink(undefined)
                                .socialCreatedAt(undefined)
                                .plannedPublicationDate(new Date())
                                .attachments([dependencies.medias()[0]._id])
                                .author({
                                    _id: newDbId(),
                                    name: 'John',
                                    lastname: 'Doe',
                                })
                                .build(),
                        ];
                    },
                },
                medias: {
                    data() {
                        return [
                            getDefaultMedia()
                                .type(MediaType.VIDEO)
                                .thumbnail('http://my-thumbnail.com/picture.jpg')
                                .dimensions({
                                    original: { width: 1920, height: 1080 },
                                    thumbnail: { width: 250, height: 120 },
                                })
                                .aspectRatio(16 / 9)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): SocialPostDto {
                const post = dependencies.posts[0];

                return {
                    id: post._id.toString(),
                    title: null,
                    text: post.text ?? '',
                    published: post.published,
                    isPublishing: post.isPublishing ?? false,
                    platformKeys: post.keys ?? [],
                    postType: PostType.REEL,
                    plannedPublicationDate: post.plannedPublicationDate!.toISOString(),
                    attachments: [
                        expect.objectContaining({
                            id: dependencies.medias[0]._id.toString(),
                            type: MediaType.VIDEO,
                        }),
                    ],
                    callToAction: undefined,
                    location: post.location ?? null,
                    socialLink: null,
                    socialCreatedAt: undefined,
                    error: undefined,
                    feedbacks: post.feedbackId ? expect.objectContaining({ id: post.feedbackId.toString() }) : null,
                    hashtags: { selected: [], suggested: [] },
                    author: post.author
                        ? {
                              id: post.author._id.toString(),
                              name: post.author.name,
                              lastname: post.author.lastname,
                              picture: post.author.picture,
                          }
                        : null,
                    userTagsList: post.userTagsList ?? [],
                    bindingId: post.bindingId,
                    tiktokOptions: post.tiktokOptions ?? {
                        privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                        interactionAbility: {
                            comment: false,
                            duet: false,
                            stitch: false,
                        },
                        contentDisclosureSettings: {
                            isActivated: false,
                            yourBrand: false,
                            brandedContent: false,
                        },
                    },
                    instagramCollaboratorsUsernames: [],
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const postId = seededObjects.posts[0]._id.toString();
        const author = {
            id: newDbId().toString(),
            name: 'my name is',
            lastname: 'author',
        };

        const transformPostToReelUseCase = container.resolve(TransformPostToReelUseCase);
        const result = await transformPostToReelUseCase.execute({ postId, author });

        const expectedResult = testCase.getExpectedResult();
        expect(result).toEqual(expectedResult);
    });
});
