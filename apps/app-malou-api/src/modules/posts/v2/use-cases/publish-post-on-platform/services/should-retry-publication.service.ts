import { singleton } from 'tsyringe';

import {
    MetaGraphApiCredentialsHandlerErrorCodes as ApiErrorCodes,
    MetaGraphApiCredentialsHandlerErrorCode,
} from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';

@singleton()
export class ShouldRetryPublicationService {
    execute(error: MetaGraphApiCredentialsHandlerErrorCode): boolean {
        switch (error) {
            case ApiErrorCodes.USER_NEEDS_TO_LOG_IN:
            case ApiErrorCodes.USER_NEEDS_TO_LOG_IN_TO_IG_APP:
            case ApiErrorCodes.USER_MISSING_APPROPRIATE_ROLE:
            case ApiErrorCodes.CREDENTIAL_NOT_FOUND:
            case ApiErrorCodes.CREDENTIAL_PAGE_ACCESS_TOKEN_NOT_FOUND:
            case ApiErrorCodes.INVALID_TOKEN:
                return false;
            case ApiErrorCodes.MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION:
            case ApiErrorCodes.CANNOT_VALIDATE_RESPONSE:
            case ApiErrorCodes.CANNOT_VALIDATE_ERROR_RESPONSE:
            case ApiErrorCodes.UNKNOWN_ERROR:
            case ApiErrorCodes.COULD_NOT_REFRESH_ACCESS_TOKEN:
            case ApiErrorCodes.VIDEO_REEL_PUBLISH_WAIT_MAX_ATTEMPTS_EXCEEDED:
            case ApiErrorCodes.VIDEO_REEL_UPLOAD_WAIT_MAX_ATTEMPTS_EXCEEDED:
            case ApiErrorCodes.MEDIA_CREATION_FAILED:
                return true;
        }
    }
}
