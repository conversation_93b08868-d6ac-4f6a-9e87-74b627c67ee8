import { singleton } from 'tsyringe';

import { PublicationErrorCode } from '@malou-io/package-utils';

import { MapstrCreatePostErrorMessage, MapstrCreatePostResponseBody } from ':modules/providers/platforms/mapstr/mapstr.interface';

@singleton()
export class MapMapstrCreatePostErrorToPublicationError {
    execute(error: MapstrCreatePostResponseBody['data']): PublicationErrorCode {
        switch (error) {
            case MapstrCreatePostErrorMessage.BEARER_TOKEN_INVALID:
            case MapstrCreatePostErrorMessage.MALOU_TOKEN_INVALID:
                return PublicationErrorCode.CONNECTION_EXPIRED;
            case MapstrCreatePostErrorMessage.MISSING_POST_CREATION_INFORMATION:
            default:
                return PublicationErrorCode.UNKNOWN_ERROR;
        }
    }
}
