import { singleton } from 'tsyringe';

import { PublicationErrorCode } from '@malou-io/package-utils';

import { MetaGraphApiCredentialsHandlerErrorCode } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';

@singleton()
export class MapMetaErrorToPublicationError {
    execute(code: MetaGraphApiCredentialsHandlerErrorCode): PublicationErrorCode {
        const map: Record<MetaGraphApiCredentialsHandlerErrorCode, PublicationErrorCode> = {
            CANNOT_VALIDATE_ERROR_RESPONSE: PublicationErrorCode.UNKNOWN_ERROR,
            CANNOT_VALIDATE_RESPONSE: PublicationErrorCode.UNKNOWN_ERROR,
            INVALID_TOKEN: PublicationErrorCode.CONNECTION_EXPIRED,
            MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION: PublicationErrorCode.UNKNOWN_ERROR,
            UNKNOWN_ERROR: PublicationErrorCode.UNKNOWN_ERROR,
            USER_MISSING_APPROPRIATE_ROLE: PublicationErrorCode.USER_MISSING_APPROPRIATE_ROLE,
            USER_NEEDS_TO_LOG_IN: PublicationErrorCode.USER_NEEDS_TO_LOG_IN,
            USER_NEEDS_TO_LOG_IN_TO_IG_APP: PublicationErrorCode.USER_NEEDS_TO_LOG_IN_TO_IG_APP,
            CREDENTIAL_NOT_FOUND: PublicationErrorCode.CONNECTION_EXPIRED,
            CREDENTIAL_PAGE_ACCESS_TOKEN_NOT_FOUND: PublicationErrorCode.CONNECTION_EXPIRED,
            COULD_NOT_REFRESH_ACCESS_TOKEN: PublicationErrorCode.UNKNOWN_ERROR,
            VIDEO_REEL_UPLOAD_WAIT_MAX_ATTEMPTS_EXCEEDED: PublicationErrorCode.UNKNOWN_ERROR,
            VIDEO_REEL_PUBLISH_WAIT_MAX_ATTEMPTS_EXCEEDED: PublicationErrorCode.UNKNOWN_ERROR,
            MEDIA_CREATION_FAILED: PublicationErrorCode.UNKNOWN_ERROR,
        };

        return map[code];
    }
}
