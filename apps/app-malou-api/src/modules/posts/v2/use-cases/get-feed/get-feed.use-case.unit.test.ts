import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { FeedItemDto } from '@malou-io/package-dto';
import { PlatformKey, PostSource, PostType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { GetFeedUseCase } from ':modules/posts/v2/use-cases/get-feed/get-feed.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('GetFeedUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'PostsRepository']);
    });

    describe('Empty result cases', () => {
        it('should return an empty array and null nextCursor if there is no posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: (): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    return { feed: [], nextCursor: null };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, null, null);

            expect(feed).toEqual(expectedResult);
        });

        it('should return an empty array and null nextCursor if the restaurant has no posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('123').build(), getDefaultRestaurant().uniqueKey('456').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [getDefaultPost().source(PostSource.SOCIAL).restaurantId(dependencies.restaurants()[1]._id).build()];
                        },
                    },
                },
                expectedResult: (): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    return { feed: [], nextCursor: null };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, null, null);

            expect(feed).toEqual(expectedResult);
        });

        it('should return an empty array and null nextCursor if the restaurant has no social posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).source(PostSource.SEO).build()];
                        },
                    },
                },
                expectedResult: (): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    return { feed: [], nextCursor: null };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, null, null);

            expect(feed).toEqual(expectedResult);
        });

        it('should return an empty array and null nextCursor if the restaurant has no Instagram social posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .keys([])
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.MAPSTR)
                                    .keys([])
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.TIKTOK)
                                    .keys([])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    return { feed: [], nextCursor: null };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, null, null);

            expect(feed).toEqual(expectedResult);
        });

        it('should return an empty array and null nextCursor if the restaurant only has reels with isReelDisplayedInFeed false', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .keys([])
                                    .postType(PostType.REEL)
                                    .isReelDisplayedInFeed(false)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(undefined)
                                    .keys([PlatformKey.INSTAGRAM])
                                    .postType(PostType.REEL)
                                    .isReelDisplayedInFeed(false)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    return { feed: [], nextCursor: null };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, null, null);

            expect(feed).toEqual(expectedResult);
        });
    });

    describe('Pagination', () => {
        it('should return the social posts of the restaurant sorted by sort date desc', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();

            const limit = 3;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .postType(PostType.REEL)
                                    .isReelDisplayedInFeed(true)
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Awesome social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    const amazingSocialPost = dependencies.posts[0];
                    const awesomeSocialPost = dependencies.posts[1];
                    const incredibleSocialPost = dependencies.posts[2];
                    return {
                        feed: [
                            {
                                postId: amazingSocialPost._id.toString(),
                                published: amazingSocialPost.published,
                                plannedPublicationDate: amazingSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: amazingSocialPost.socialCreatedAt,
                                postType: amazingSocialPost.postType,
                                sortDate: amazingSocialPost.sortDate!,
                                updatedAt: amazingSocialPost.updatedAt,
                            },
                            {
                                postId: incredibleSocialPost._id.toString(),
                                published: incredibleSocialPost.published,
                                plannedPublicationDate: incredibleSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: incredibleSocialPost.socialCreatedAt,
                                postType: incredibleSocialPost.postType,
                                sortDate: incredibleSocialPost.sortDate!,
                                updatedAt: incredibleSocialPost.updatedAt,
                            },
                            {
                                postId: awesomeSocialPost._id.toString(),
                                published: awesomeSocialPost.published,
                                plannedPublicationDate: awesomeSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: awesomeSocialPost.socialCreatedAt,
                                postType: awesomeSocialPost.postType,
                                sortDate: awesomeSocialPost.sortDate!,
                                updatedAt: awesomeSocialPost.updatedAt,
                            },
                        ],
                        nextCursor: awesomeSocialPost.sortDate ?? null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, null, limit);

            expect(feed).toEqual(expectedResult);
        });

        it('should return maximum [limit] social posts of the restaurant sorted by sort date desc', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();

            const limit = 3;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Awesome social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Formidable social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(threeDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    const amazingSocialPost = dependencies.posts[0];
                    const awesomeSocialPost = dependencies.posts[1];
                    const incredibleSocialPost = dependencies.posts[2];
                    return {
                        feed: [
                            {
                                postId: amazingSocialPost._id.toString(),
                                published: amazingSocialPost.published,
                                plannedPublicationDate: amazingSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: amazingSocialPost.socialCreatedAt,
                                postType: amazingSocialPost.postType,
                                sortDate: amazingSocialPost.sortDate!,
                                updatedAt: amazingSocialPost.updatedAt,
                            },
                            {
                                postId: incredibleSocialPost._id.toString(),
                                published: incredibleSocialPost.published,
                                plannedPublicationDate: incredibleSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: incredibleSocialPost.socialCreatedAt,
                                postType: incredibleSocialPost.postType,
                                sortDate: incredibleSocialPost.sortDate!,
                                updatedAt: incredibleSocialPost.updatedAt,
                            },
                            {
                                postId: awesomeSocialPost._id.toString(),
                                published: awesomeSocialPost.published,
                                plannedPublicationDate: awesomeSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: awesomeSocialPost.socialCreatedAt,
                                postType: awesomeSocialPost.postType,
                                sortDate: awesomeSocialPost.sortDate!,
                                updatedAt: awesomeSocialPost.updatedAt,
                            },
                        ],
                        nextCursor: awesomeSocialPost.sortDate ?? null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, null, limit);

            expect(feed).toEqual(expectedResult);
        });

        it('should return all the social posts of the restaurant, and nextCursor null, if the limit exceeds their count', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();

            const limit = 10;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Awesome social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Formidable social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(threeDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    const amazingSocialPost = dependencies.posts[0];
                    const awesomeSocialPost = dependencies.posts[1];
                    const incredibleSocialPost = dependencies.posts[2];
                    const formidableSocialPost = dependencies.posts[3];
                    return {
                        feed: [
                            {
                                postId: amazingSocialPost._id.toString(),
                                published: amazingSocialPost.published,
                                plannedPublicationDate: amazingSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: amazingSocialPost.socialCreatedAt,
                                postType: amazingSocialPost.postType,
                                sortDate: amazingSocialPost.sortDate!,
                                updatedAt: amazingSocialPost.updatedAt,
                            },
                            {
                                postId: incredibleSocialPost._id.toString(),
                                published: incredibleSocialPost.published,
                                plannedPublicationDate: incredibleSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: incredibleSocialPost.socialCreatedAt,
                                postType: incredibleSocialPost.postType,
                                sortDate: incredibleSocialPost.sortDate!,
                                updatedAt: incredibleSocialPost.updatedAt,
                            },
                            {
                                postId: awesomeSocialPost._id.toString(),
                                published: awesomeSocialPost.published,
                                plannedPublicationDate: awesomeSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: awesomeSocialPost.socialCreatedAt,
                                postType: awesomeSocialPost.postType,
                                sortDate: awesomeSocialPost.sortDate!,
                                updatedAt: awesomeSocialPost.updatedAt,
                            },
                            {
                                postId: formidableSocialPost._id.toString(),
                                published: formidableSocialPost.published,
                                plannedPublicationDate: formidableSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: formidableSocialPost.socialCreatedAt,
                                postType: formidableSocialPost.postType,
                                sortDate: formidableSocialPost.sortDate!,
                                updatedAt: formidableSocialPost.updatedAt,
                            },
                        ],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, null, limit);

            expect(feed).toEqual(expectedResult);
        });

        it('should return the feed items after the given next cursor', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();

            const limit = 3;
            const nextCursor = twoDaysAgo;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Awesome social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Formidable social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(threeDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    const formidableSocialPost = dependencies.posts[3];
                    return {
                        feed: [
                            {
                                postId: formidableSocialPost._id.toString(),
                                published: formidableSocialPost.published,
                                plannedPublicationDate: formidableSocialPost.plannedPublicationDate ?? null,
                                media: null,
                                socialCreatedAt: formidableSocialPost.socialCreatedAt,
                                postType: formidableSocialPost.postType,
                                sortDate: formidableSocialPost.sortDate!,
                                updatedAt: formidableSocialPost.updatedAt,
                            },
                        ],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, nextCursor, limit);

            expect(feed).toEqual(expectedResult);
        });

        it('should return and empty array if the restaurant has no social post after the given next cursor', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();

            const limit = 3;
            const nextCursor = threeDaysAgo;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Awesome social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Formidable social post')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(threeDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): { feed: FeedItemDto[]; nextCursor: null | Date } => {
                    return {
                        feed: [],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getFeedUseCase = container.resolve(GetFeedUseCase);
            const feed = await getFeedUseCase.execute(restaurantId, nextCursor, limit);

            expect(feed).toEqual(expectedResult);
        });
    });
});
