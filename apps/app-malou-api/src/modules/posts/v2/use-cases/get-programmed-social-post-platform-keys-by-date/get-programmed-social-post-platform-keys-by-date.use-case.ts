import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { ProgrammedSocialPostPlatformKeysByDateDto } from '@malou-io/package-dto';
import { isSameDay } from '@malou-io/package-utils';

import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';

@singleton()
export class GetProgrammedSocialPostPlatformKeysByDateUseCase {
    constructor(private readonly _postsRepository: PostsRepository) {}

    /**
     * Because client's machine and server might have different timezones, we need to take the timezone into account when grouping the posts by date.
     * The purpose is to display the posts at the right date in the calendar from the client's point of view.
     *
     * Example:
     * A post that is programmed the 2nd of January at 01:00:00 in the client's timezone (e.g. Europe/Paris) will be programmed the 1st of January at 23:00:00 in the server's timezone (e.g. UTC).
     * If we don't take the timezone into account, the post will be displayed in the calendar on the 1st of January instead of the 2nd of January.
     */
    async execute(restaurantId: string, timezone?: string): Promise<ProgrammedSocialPostPlatformKeysByDateDto> {
        const programmedSocialPosts = await this._postsRepository.getProgrammedSocialPostPlatformKeys(restaurantId);

        const groupedByDate = programmedSocialPosts.reduce((acc, programmedSocialPost) => {
            const index = acc.findIndex((item) => isSameDay(new Date(item.date), programmedSocialPost.plannedPublicationDate, timezone));
            if (index !== -1) {
                acc[index].platformKeys.push(...programmedSocialPost.platformKeys);
            } else {
                const date = timezone
                    ? DateTime.fromJSDate(programmedSocialPost.plannedPublicationDate).setZone(timezone).toISODate()
                    : DateTime.fromJSDate(programmedSocialPost.plannedPublicationDate).toISODate();
                acc.push({ date, platformKeys: programmedSocialPost.platformKeys });
            }
            return acc;
        }, [] as ProgrammedSocialPostPlatformKeysByDateDto);

        groupedByDate.forEach((item) => {
            // the sort helps to test the function
            item.platformKeys = [...new Set(item.platformKeys)].sort();
        });

        return groupedByDate;
    }
}
