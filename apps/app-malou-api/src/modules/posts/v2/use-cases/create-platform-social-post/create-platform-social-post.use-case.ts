import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { IPost, IPostWithAttachments, newDbId } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { SocialPostToMapstrSocialPostMapper } from ':modules/posts/v2/platforms/mapstr/social-post-to-mapstr-social-post.mapper';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';

// TODO posts-v2 write a test for this
@singleton()
export class CreateAndSendPostOnPlatformUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _socialPostToMapstrSocialPostMapper: SocialPostToMapstrSocialPostMapper,
        private readonly _agendaSingleton: AgendaSingleton
    ) {}

    async execute({ post, platformKey }: { post: IPostWithAttachments; platformKey: PlatformKey }): Promise<void> {
        logger.info('[POST PUBLICATION] About to create platform post', {
            platformKey,
        });
        assert(post.published, PostPublicationStatus.PENDING);

        const { restaurantId } = post;
        const originalPostId = post._id.toString();
        const platform = await this._platformsRepository.findOne({
            filter: { restaurantId, key: platformKey },
            options: { lean: true },
        });
        assert(platform, 'Platform not found');

        delete post.socialId;
        const sortDate = post.socialCreatedAt ?? post.plannedPublicationDate ?? new Date();
        const platformPost = await this._postsRepository.create({
            data: {
                ...this._mapToPlatformPost(post, platformKey),
                _id: newDbId(),
                tries: 0,
                key: platformKey,
                keys: [platformKey],
                platformId: platform?._id,
                sortDate,
                isPublishing: true,
            },
        });
        const platformPostId = platformPost._id.toString();

        await this._agendaSingleton.now(AgendaJobName.PUBLISH_POST_ON_PLATFORM, { postId: platformPostId });

        logger.info('[POST PUBLICATION] Platform social post created and sent for publication', {
            originalPostId,
            platformPostId,
            platformKey,
        });
    }

    private _mapToPlatformPost(post: IPostWithAttachments, platformKey: PlatformKey): IPost {
        switch (platformKey) {
            case PlatformKey.MAPSTR:
                return this._socialPostToMapstrSocialPostMapper.execute(post);
            default:
                return this._mapToDefaultPlatformPost(post);
        }
    }

    private _mapToDefaultPlatformPost(post: IPostWithAttachments): IPost {
        return {
            ...post,
            attachments: post.attachments?.map((a) => a._id),
        };
    }
}
