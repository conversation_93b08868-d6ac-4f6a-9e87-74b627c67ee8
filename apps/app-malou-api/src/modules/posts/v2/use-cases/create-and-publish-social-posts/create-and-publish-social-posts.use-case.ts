import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import FeedbacksRepository from ':modules/feedbacks/feedback.repository';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { CreateAndSendPostOnPlatformUseCase } from ':modules/posts/v2/use-cases/create-platform-social-post/create-platform-social-post.use-case';

// TODO posts-v2 write a test for this
@singleton()
export class CreateAndSendPostOnPlatformsUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _feedbacksRepository: FeedbacksRepository,
        private readonly _createAndSendPostOnPlatformUseCase: CreateAndSendPostOnPlatformUseCase
    ) {}

    async execute({ postId }: { postId: string }): Promise<void> {
        const post = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            options: { populate: [{ path: 'attachments' }], lean: true },
        });
        assert(post?.keys, 'Post not found');
        assert(post.published, PostPublicationStatus.PENDING);

        const postCreationStatusOnPlatforms: { platformKey: PlatformKey; success: boolean }[] = await Promise.all(
            post.keys.map(async (platformKey) => {
                try {
                    await this._createAndSendPostOnPlatformUseCase.execute({ post, platformKey });

                    return { success: true, platformKey };
                } catch (err) {
                    logger.error('[POST PUBLICATION] Error when creating post for platform', {
                        platformKey,
                        err,
                    });

                    return { success: false, platformKey };
                }
            })
        );
        const failedPlatformKeys = postCreationStatusOnPlatforms.filter(({ success }) => !success).map(({ platformKey }) => platformKey);

        if (failedPlatformKeys.length > 0) {
            logger.error('[POST PUBLICATION] Missing platforms for post creation', { failedPlatformKeys });
            await this._postsRepository.updateOne({
                filter: { _id: postId },
                update: { keys: failedPlatformKeys },
            });

            throw new MalouError(MalouErrorCode.POST_CREATION_FOR_PLATFORM_FAILED, {
                metadata: { failedPlatformKeys },
            });
        }

        // Close feedbacks if post is published on all platforms
        if (post.feedbackId) {
            await this._feedbacksRepository.updateIsOpen(post.feedbackId.toString(), false);
        }

        await this._postsRepository.deleteOne({ filter: { _id: postId } });
    }
}
