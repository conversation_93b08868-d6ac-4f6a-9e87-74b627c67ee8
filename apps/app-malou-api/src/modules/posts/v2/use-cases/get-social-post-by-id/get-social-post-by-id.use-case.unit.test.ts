import { container } from 'tsyringe';

import { SocialPostDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { MalouErrorCode, PostSource, TiktokPrivacyStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { GetSocialPostByIdUseCase } from ':modules/posts/v2/use-cases/get-social-post-by-id/get-social-post-by-id.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('GetSocialPostByIdUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'UsersRepository', 'PostsRepository']);
    });

    it('should get the right social post', async () => {
        const getSocialPostByIdUseCase = container.resolve(GetSocialPostByIdUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .attachments([])
                                .socialAttachments([])
                                .hashtags(undefined)
                                .plannedPublicationDate(new Date())
                                .feedbackId(null)
                                .location(null)
                                .author({
                                    _id: newDbId(),
                                    name: 'my name is',
                                    lastname: 'author',
                                    picture: 'picture',
                                })
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): SocialPostDto => {
                const post = dependencies.posts[0];

                return {
                    id: post._id.toString(),
                    title: post.title,
                    text: post.text ?? '',
                    published: post.published,
                    isPublishing: post.isPublishing ?? false,
                    platformKeys: post.keys ?? [],
                    postType: post.postType,
                    plannedPublicationDate: post.plannedPublicationDate!.toISOString(),
                    attachments: [],
                    hashtags: { selected: [], suggested: [] },
                    socialCreatedAt: post.socialCreatedAt?.toISOString(),
                    socialLink: post.socialLink,
                    location: null,
                    feedbacks: null,
                    callToAction: undefined,
                    error: undefined,
                    author: {
                        id: post.author!._id.toString(),
                        name: post.author!.name,
                        lastname: post.author!.lastname,
                        picture: post.author!.picture,
                    },
                    userTagsList: [],
                    bindingId: post.bindingId,
                    tiktokOptions: post.tiktokOptions ?? {
                        privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                        interactionAbility: {
                            comment: false,
                            duet: false,
                            stitch: false,
                        },
                        contentDisclosureSettings: {
                            isActivated: false,
                            yourBrand: false,
                            brandedContent: false,
                        },
                    },
                    instagramCollaboratorsUsernames: [],
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const post = await getSocialPostByIdUseCase.execute({
            id: seededObjects?.posts[0]._id.toString(),
        });

        expect(post).toEqual(expectedResult);
    });

    it('should throw an error if the post is not found', async () => {
        const getSocialPostByIdUseCase = container.resolve(GetSocialPostByIdUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .attachments([])
                                .socialAttachments([])
                                .hashtags(undefined)
                                .feedbackId(null)
                                .location(null)
                                .build(),
                        ];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.POST_NOT_FOUND,
        });

        await testCase.build();

        const postId = newDbId().toString();
        const expectedErrorCode = testCase.getExpectedErrorCode();

        await expect(
            getSocialPostByIdUseCase.execute({
                id: postId,
            })
        ).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });

    it('should throw an error if the post is found but it is not a social post', async () => {
        const getSocialPostByIdUseCase = container.resolve(GetSocialPostByIdUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .attachments([])
                                .socialAttachments([])
                                .hashtags(undefined)
                                .feedbackId(null)
                                .location(null)
                                .build(),
                        ];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.POST_IS_NOT_SOCIAL_POST,
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const postId = seededObjects.posts[0]._id.toString();
        const expectedErrorCode = testCase.getExpectedErrorCode();

        await expect(
            getSocialPostByIdUseCase.execute({
                id: postId,
            })
        ).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });
});
