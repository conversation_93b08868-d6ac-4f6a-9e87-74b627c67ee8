import {
    DbId,
    ID,
    IPost,
    IPostWithAttachments,
    IPostWithAttachmentsAndThumbnail,
    IRestaurant,
    ISocialAttachment,
} from '@malou-io/package-models';
import { MediaType, PlatformKey, PostError, PostPublicationStatus, PostSource, PostType, SeoPostTopic } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';

export interface MalouPostData {
    socialId: string;
    socialCreatedAt: Date;
    socialUpdatedAt: Date;
    socialLink: string;
    title?: string;
    language?: string;
    text: string;
    published: PostPublicationStatus;
    key: PlatformKey;
    socialAttachments?: ISocialAttachment[];
    platformId?: DbId;
    restaurantId: DbId;
    source: PostSource;
    postType?: PostType;
    postTopic?: SeoPostTopic;
    isReelDisplayedInFeed?: boolean;
    errorData?: PostError;
}

export type StoryToPublish = { plannedPublicationDate: Date; postId: ID; creationId: string; type: MediaType };

export type PlatformPostUseCases = {
    synchronizeStories: (platform: Platform) => Promise<Partial<IPost>[]>;
    publish: ({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }) => Promise<MalouPostData | void>;
    fetchPost;
    completePublish;
    getCompetitorsPosts;
    updatePost;
    synchronize({ platform, recentPostsOnly }: { platform: Platform; recentPostsOnly: boolean }): Promise<MalouPostData[]>;
    deletePost;
    fetchPostsWithInsights;
    fetchPostsForRestaurantV2?;
    publishStory: (platformId: string, creationId: string, type: MediaType, postId: string) => Promise<IPost>;
    createStoryList;
    upsertStoryAndSaveAttachments;
    searchAccounts;
    oembed;
    getMatchingPosts?;
    getMissingSocialPost?;
    buildDataToUpdateMatchedPost?;
};

export interface PlatformWithRestaurantDetails {
    key: string;
    socialId?: string;
    id: string;
    restaurantId: string;
    restaurant?: {
        name?: string;
        internalName?: string;
        address?: IRestaurant['address'];
    };
}

export interface PlatformFollowersInsightForTop3Posts {
    socialId: string;
    value?: number | null;
    platformKey: PlatformKey;
    date: Date;
}
