import { Job } from 'agenda';
import { intersection } from 'lodash';
import assert from 'node:assert';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { errorReplacer, <PERSON><PERSON>ey, PostError, PostPublicationStatus, PostType } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { preparePostValidator } from ':helpers/validators/jobs/posts-jobs.validators';
import PostsRepository from ':modules/posts/posts.repository';
import PostsUseCases from ':modules/posts/posts.use-cases';
import { CreateAndSendPostOnPlatformsUseCase } from ':modules/posts/v2/use-cases/create-and-publish-social-posts/create-and-publish-social-posts.use-case';
import { isFeatureAvailableForUser } from ':services/experimentations-service/experimentation.service';
import { SlackService } from ':services/slack.service';

import { waitUntilReelThumbnailIsExtracted } from '../v2/use-cases/update-social-post/update-social-post.use-case';

type DataAttributes = z.infer<typeof preparePostValidator>;

@singleton()
export class PreparePostJob extends GenericJobDefinition {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _createAndSendPostOnPlatformsUseCase: CreateAndSendPostOnPlatformsUseCase,
        private readonly _slackService: SlackService
    ) {
        super({
            agendaJobName: AgendaJobName.PREPARE_POST,
            shouldDeleteJobOnSuccess: true,
            getLogMetadata: async (job: Job<DataAttributes>) => {
                const data = preparePostValidator.parse(job.attrs.data);
                const { postId } = data;
                const { restaurantId } = await this._postsRepository.findOneOrFail({
                    filter: { _id: postId },
                    projection: { restaurantId: 1 },
                    options: { lean: true },
                });

                assert(restaurantId, 'Missing restaurantId on post');

                return {
                    restaurant: { id: restaurantId.toString() },
                };
            },
        });
    }

    async executeJob(job: Job<DataAttributes>): Promise<void> {
        const data = preparePostValidator.parse(job.attrs.data);
        const { userId, postId } = data;
        logger.info('[PreparePostJob] starting', job);

        try {
            let post = await this._postsRepository.findOneAndUpdate({
                filter: { _id: postId, published: PostPublicationStatus.PENDING },
                update: { $set: { isPublishing: true } },
                options: { lean: true, new: true, upsert: false },
            });

            if (!post) {
                post = await this._postsRepository.findOne({ filter: { _id: postId } });
                assert(post, 'Post not found');
                logger.info('[PreparePostJob] the post is not published', { postId, published: post.published });
                return;
            }

            if (post.postType === PostType.REEL) {
                await waitUntilReelThumbnailIsExtracted(postId.toString());
            }

            const postFound = await this._postsRepository.findOne({
                filter: {
                    text: post.text,
                    'author._id': userId,
                    published: PostPublicationStatus.PUBLISHED,
                    key: post.key,
                    restaurantId: post.restaurantId,
                },
                options: { lean: true },
            });

            if (postFound?.socialId) {
                logger.warn('[POST PUBLICATION] Stop publish, found post with same socialId', { postFound });

                await this._postsRepository.updateOne({
                    filter: { _id: postId },
                    update: {
                        published: PostPublicationStatus.ERROR,
                        errorData: PostError.POST_ALREADY_PUBLISHED,
                        errorStage: AgendaJobName.PREPARE_POST,
                        isPublishing: false,
                    },
                });

                return;
            }

            const isNewSocialPostsFeatureAvailable = await isFeatureAvailableForUser({
                userId: userId.toString(),
                featureName: 'release-new-post-publication',
            });

            logger.info('[POST PUBLICATION] Start publication on platforms');
            if (
                (isNewSocialPostsFeatureAvailable &&
                    intersection([...(post.keys ?? []), post.key], [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.MAPSTR])
                        .length > 0) ||
                [...(post.keys ?? []), post.key].includes(PlatformKey.TIKTOK)
            ) {
                await this._createAndSendPostOnPlatformsUseCase.execute({ postId: postId.toString() });
            } else {
                await this._postsUseCases.preparePost({ userId, postId });
            }
        } catch (err: any) {
            logger.error('[POST PUBLICATION] Error while initiating post publication', { err });

            await this._postsRepository.updateOne({
                filter: { _id: postId },
                update: {
                    published: PostPublicationStatus.ERROR,
                    errorData: err?.message ?? JSON.stringify(err, errorReplacer),
                    errorStage: AgendaJobName.PREPARE_POST,
                    isPublishing: false,
                },
            });

            await this._slackService.sendAlert({ data: { err } });

            throw err;
        }
    }
}
