import { singleton } from 'tsyringe';

import { IPopulatedPost } from '@malou-io/package-models';
import { TiktokPostPublishFailedReason, TiktokPrivacyStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import PostsRepository from ':modules/posts/posts.repository';
import { HandleTiktokPublicationErrorService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/handle-tiktok-publication-error.service';
import { TiktokProvider } from ':providers/tiktok/tiktok.provider';
import { FileUploadMethod, PublishVideoInitRequestBody } from ':providers/tiktok/validators';

/**
 * After initializing the publication of a Tiktok post, we receive updated information about the post via Webhook.
 * See HandleTiktokIncomingEventsUseCase for more information.
 */
@singleton()
export class TiktokPostInitPublishUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _callTiktokApiService: CallTiktokApiService,
        private readonly _tiktokProvider: TiktokProvider,
        private readonly _handleTiktokPublicationErrorService: HandleTiktokPublicationErrorService
    ) {}

    async execute({ post }: { post: IPopulatedPost }): Promise<void> {
        try {
            const tiktokPublishVideoInitRequestBody = this._mapMalouPostToTiktokPost({ post });
            const {
                data: { publish_id: publishId },
            } = await this._callTiktokApiService.execute({
                restaurantId: post.restaurantId.toString(),
                method: this._tiktokProvider.initVideoPublication,
                args: {
                    body: tiktokPublishVideoInitRequestBody,
                },
            });
            await this._postsRepository.updateOne({
                filter: { _id: post._id },
                update: { tiktokPublishId: publishId },
            });
        } catch (err: any) {
            logger.error('[TIKTOK API] Error while initializing video publication', {
                err,
            });
            const tiktokError = err?.response?.data?.error?.code as TiktokPostPublishFailedReason | undefined;
            if (!tiktokError) {
                throw err;
            }
            await this._handleTiktokPublicationErrorService.execute(tiktokError, post._id.toString());
        }
    }

    private _mapMalouPostToTiktokPost({ post }: { post: IPopulatedPost }): PublishVideoInitRequestBody {
        const selectedHashtagsText = post.hashtags?.selected?.map((hashtag) => hashtag.text).join(' ') ?? '';
        const textWithHashtags = selectedHashtagsText ? `${post.text}\n\n${selectedHashtagsText}` : post.text;

        return {
            post_info: {
                video_cover_timestamp_ms: Math.round(post.thumbnailOffsetTimeInMs ?? 0),
                brand_content_toggle: post.tiktokOptions?.contentDisclosureSettings?.brandedContent,
                brand_organic_toggle: post.tiktokOptions?.contentDisclosureSettings?.yourBrand,
                disable_comment: post.tiktokOptions?.interactionAbility?.comment === false,
                disable_duet: post.tiktokOptions?.interactionAbility?.duet === false,
                disable_stitch: post.tiktokOptions?.interactionAbility?.stitch === false,
                is_aigc: false,
                privacy_level: post.tiktokOptions?.privacyStatus ?? TiktokPrivacyStatus.SELF_ONLY,
                title: textWithHashtags,
            },
            source_info: {
                source: FileUploadMethod.PULL_FROM_URL,
                video_url: post.attachments[0].urls.original,
            },
        };
    }
}
