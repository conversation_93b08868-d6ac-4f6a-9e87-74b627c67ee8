import assert from 'assert';
import { singleton } from 'tsyringe';

import { IPost, toDbId } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { TiktokQueryCreatorInfoUseCase } from ':modules/posts/platforms/tiktok/use-cases/tiktok-query-creator-info.use-case';
import PostsRepository from ':modules/posts/posts.repository';
import {
    postPublishPubliclyAvailableEventContentValidator,
    TiktokEventRequestBody,
} from ':modules/webhooks/platforms/tiktok/validators/webhook-events.validators';
import { TiktokProvider } from ':providers/tiktok/tiktok.provider';
import { VideoObject } from ':providers/tiktok/validators/display-api.validators';

@singleton()
export class TiktokHandlePostPublishPubliclyAvailableUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _callTiktokApiService: CallTiktokApiService,
        private readonly _tiktokProvider: TiktokProvider,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _tiktokQueryCreatorInfoUseCase: TiktokQueryCreatorInfoUseCase
    ) {}

    // Used when post is publicly available. Triggered on post.publish.publicly_available webhook event
    async execute({ body }: { body: TiktokEventRequestBody }): Promise<void> {
        const { content } = body;
        const { publish_id: publishId, post_id: socialId } = await postPublishPubliclyAvailableEventContentValidator.parseAsync(
            JSON.parse(content)
        );

        const post = await this._postsRepository.findOneAndUpdate({
            filter: { tiktokPublishId: publishId },
            update: {
                socialId,
                published: PostPublicationStatus.PUBLISHED,
                isPublishing: false,
            },
            options: { lean: true },
        });
        if (!post) {
            return;
        }

        // Get corresponding video info
        const { data } = await this._callTiktokApiService.execute({
            restaurantId: post.restaurantId.toString(),
            method: this._tiktokProvider.queryVideos,
            args: {
                ids: [socialId],
            },
        });
        const publicationInfo = data.videos?.[0];

        if (!publicationInfo) {
            return;
        }

        assert(post.restaurantId);

        const socialLink = await this._buildAndSaveSocialLink({
            socialId,
            restaurantId: post.restaurantId.toString(),
        });

        await this._postsRepository.updateOne({
            filter: { _id: post._id },
            update: { ...this._mapPublicationToPostInfo({ publicationInfo }), socialLink },
        });
    }

    private _mapPublicationToPostInfo({ publicationInfo }: { publicationInfo: VideoObject }): Partial<IPost> {
        return {
            socialCreatedAt: new Date(publicationInfo.create_time * 1000),
            socialLink: publicationInfo.share_url,
            text: publicationInfo.video_description,
            title: publicationInfo.title,
        };
    }

    private async _buildAndSaveSocialLink({ socialId, restaurantId }: { socialId: string; restaurantId: string }): Promise<string | null> {
        const tiktokPlatform = await this._platformsRepository.findOne({
            filter: { restaurantId: toDbId(restaurantId), key: PlatformKey.TIKTOK },
            options: { lean: true },
        });
        let name = tiktokPlatform?.name;

        if (!name && tiktokPlatform) {
            const userInfo = await this._tiktokQueryCreatorInfoUseCase.execute({ restaurantId });
            name = userInfo.creatorUserName;

            await this._platformsRepository.updateOne({
                filter: { _id: tiktokPlatform._id },
                update: { name, socialLink: `https://www.tiktok.com/@${name}` },
            });
        }

        return name ? `https://www.tiktok.com/@${name}/video/${socialId}` : null;
    }
}
