import assert from 'assert';
import { singleton } from 'tsyringe';

import { IPostWithAttachments, IPostWithAttachmentsAndThumbnail } from '@malou-io/package-models';
import { MalouErrorCode, TimeInMilliseconds, waitFor } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { Platform } from ':modules/platforms/platforms.entity';
import { sendCompletePublishPost } from ':modules/posts/queues/publish-post/publish-post.producer';
import { metricsService } from ':services/metrics.service';

const checkUploadStatusRetriesCounter = metricsService.getMeter().createCounter('posts.facebookReels.publish.checkUploadStatusRetries', {
    description: 'Number of _checkUploadStatus retries in FacebookPublishReelUseCase',
});

const globalRetriesCounter = metricsService.getMeter().createCounter('posts.facebookReels.publish.globalRetries', {
    description: 'Number of “global” retries in FacebookPublishReelUseCase',
});

const requestsCounter = metricsService.getMeter().createCounter('posts.facebookReels.publish.requests', {
    description: 'Number of calls to FacebookPublishReelUseCase#execute',
});

const CHECK_STATUS_MAX_TRIES = 10;
const CHECK_STATUS_WAIT_IN_MILLISECONDS = 5 * TimeInMilliseconds.SECOND;

@singleton()
export class FacebookPublishReelUseCase {
    constructor(private readonly _facebookCredentialsRepository: FacebookCredentialsRepository) {}
    async execute(post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments, platform: Platform, credentialId: string): Promise<void> {
        logger.info('[FacebookPublishReelUseCase] Start', { postId: post._id });

        requestsCounter.add(1);

        assert(platform.socialId, 'Missing socialId on platform');

        let attempts: number = 0;
        for (;;) {
            let postSocialId: string;
            try {
                const credential = await this._facebookCredentialsRepository.getCredentialById(credentialId);

                const pageId = platform.socialId;
                assert(pageId);
                const pageAccessToken = await facebookCredentialsUseCases.getPageAccessTokenBySocialId(credential, pageId);
                const videoUrl = post.attachments.find((media) => !!media.urls.original)?.urls.original;
                assert(videoUrl);

                const { upload_url: uploadUrl, video_id: videoId } = await this._initUploadSession(pageAccessToken, pageId);
                await this._uploadVideo(pageAccessToken, uploadUrl, videoUrl);
                await this._checkUploadStatus(pageAccessToken, videoId, FacebookApiTypes.Videos.VideoStatus.UPLOAD_COMPLETE);
                await this._publishReel(pageAccessToken, videoId, pageId, post);
                await this._checkUploadStatus(pageAccessToken, videoId, FacebookApiTypes.Videos.VideoStatus.READY);
                postSocialId = await this._getPostSocialId(pageAccessToken, videoId, platform.socialId);
            } catch (error: unknown) {
                const errorString = error instanceof Error ? error.stack : JSON.stringify(error);
                logger.info(`[FacebookPublishReelUseCase] (attempt ${attempts}) error`, { errorString, postId: post._id });
                if (attempts === 2) {
                    throw error;
                }
                attempts++;
                globalRetriesCounter.add(1);
                continue;
            }

            await sendCompletePublishPost({
                postId: post._id,
                postSocialId,
            });
            return;
        }
    }

    private async _initUploadSession(pageAccessToken: string, pageId: string): Promise<FacebookApiTypes.Videos.InitUploadSessionResponse> {
        const res = await facebookCredentialsUseCases.FbReels.initUploadSession(pageAccessToken, pageId);
        return res;
    }

    private async _uploadVideo(pageAccessToken: string, uploadUrl: string, videoUrl: string): Promise<void> {
        const uploadVideoRes = await facebookCredentialsUseCases.FbVideos.uploadVideo(pageAccessToken, uploadUrl, videoUrl);
        if (!uploadVideoRes.success) {
            throw new MalouError(MalouErrorCode.FACEBOOK_REEL_UPLOAD_VIDEO_FAILED, { metadata: { res: uploadVideoRes } });
        }
    }

    private async _checkUploadStatus(
        pageAccessToken: string,
        videoId: string,
        videoStatusToWaitFor: FacebookApiTypes.Videos.VideoStatus
    ): Promise<void> {
        let getUploadStatusRes: FacebookApiTypes.Videos.GetVideoUploadStatusResponse | undefined;
        for (let i = 0; i < CHECK_STATUS_MAX_TRIES; i++) {
            getUploadStatusRes = await facebookCredentialsUseCases.FbVideos.getUploadStatus(pageAccessToken, videoId);
            if (
                [
                    FacebookApiTypes.Videos.VideoStatus.ERROR,
                    FacebookApiTypes.Videos.VideoStatus.UPLOAD_FAILED,
                    FacebookApiTypes.Videos.VideoStatus.EXPIRED,
                ].includes(getUploadStatusRes.status.video_status)
            ) {
                throw new MalouError(MalouErrorCode.FACEBOOK_REEL_BAD_UPLOAD_STATUS, {
                    metadata: { status: getUploadStatusRes.status, position: 'in while loop' },
                });
            }

            if (getUploadStatusRes.status.video_status === videoStatusToWaitFor) {
                return;
            }

            if (i < CHECK_STATUS_MAX_TRIES) {
                await waitFor(CHECK_STATUS_WAIT_IN_MILLISECONDS);
            }

            checkUploadStatusRetriesCounter.add(1, { videoStatusToWaitFor });
        }

        if (getUploadStatusRes?.status.video_status !== videoStatusToWaitFor) {
            logger.info('[FacebookPublishReelUseCase] _checkUploadStatus timeout', { videoId, videoStatusToWaitFor });
            throw new MalouError(MalouErrorCode.FACEBOOK_REEL_BAD_UPLOAD_STATUS, {
                metadata: { status: getUploadStatusRes?.status, position: 'after while loop' },
            });
        }
    }

    private async _publishReel(
        pageAccessToken: string,
        videoId: string,
        pageId: string,
        post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments
    ): Promise<void> {
        const res = await facebookCredentialsUseCases.FbReels.publishReel(pageAccessToken, pageId, videoId, {
            title: post.title ?? '',
            description: post.text ?? '',
            locationId: post.location?.id,
        });
        if (!res.success) {
            throw new MalouError(MalouErrorCode.FACEBOOK_REEL_PUBLISH_REEL_FAILED, { metadata: { res } });
        }
    }

    private async _getPostSocialId(pageAccessToken: string, videoId: string, platformSocialId: string): Promise<string> {
        const fbReel = await facebookCredentialsUseCases.FbReels.getReel(pageAccessToken, videoId);
        const postId = fbReel.post_id;
        if (!postId) {
            throw new MalouError(MalouErrorCode.FACEBOOK_REEL_GET_POST_ID_FAILED, { metadata: fbReel });
        }
        return `${platformSocialId}_${postId}`;
    }
}
