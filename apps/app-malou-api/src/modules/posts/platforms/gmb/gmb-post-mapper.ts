/* eslint-disable complexity */
import { DateTime } from 'luxon';

import { IPostWithAttachments, IPostWithAttachmentsAndThumbnail, toDbId } from '@malou-io/package-models';
import { CallToActionType, PlatformKey, PostError, PostPublicationStatus, PostSource, SeoPostTopic } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';
import { GmbMediaFormat, GmbMediaItemInput, GmbMediaItemOutput } from ':modules/platforms/platforms/gmb/gmb.types';
import { MalouPostData } from ':modules/posts/posts.interface';
import { PostMapper } from ':modules/posts/posts.mapper';

import { GmbActionType, GmbLocalPost, GmbLocalPostState } from './gmb-post.interface';
import { GMB_PUBLISH_POST_MAX_RETRIES, GMB_PUBLISH_POST_RETRY_INTERVAL } from './gmb-posts.constants';

export class GmbPostMapper extends PostMapper {
    constructor() {
        super();
    }

    mapToMalouPost({ post, platform }: { post: GmbLocalPost<GmbMediaItemOutput>; platform: Platform }): MalouPostData {
        const res: MalouPostData = {
            socialId: post.name,
            socialLink: post.searchUrl ?? '',
            socialCreatedAt: new Date(post.createTime),
            socialUpdatedAt: new Date(post.updateTime),
            language: post.languageCode,
            text: post.summary ?? '',
            key: PlatformKey.GMB,
            published: PostPublicationStatus.PUBLISHED,
            postTopic: post.topicType ? SeoPostTopic[post.topicType] : undefined,
            source: PostSource.SEO,
            errorData: undefined,
            restaurantId: toDbId(platform.restaurantId),
        };
        const { callToAction } = post;
        if (callToAction) {
            Object.assign(res, { callToAction });
        }
        if (post?.event) {
            Object.assign(res, {
                event: {
                    title: post?.event.title,
                    startDate: toMalouDate(post?.event?.schedule?.startDate),
                    endDate: toMalouDate(post?.event?.schedule?.endDate),
                },
            });
        }
        if (post?.offer) {
            Object.assign(res, {
                offer: {
                    couponCode: post?.offer.couponCode,
                    onlineUrl: post?.offer?.redeemOnlineUrl,
                    termsConditions: post?.offer?.termsConditions,
                },
            });
        }
        if (platform?.restaurantId) {
            Object.assign(res, { restaurantId: platform.restaurantId });
        }
        if (platform) {
            Object.assign(res, { platformId: platform._id });
        }
        if (post.media) {
            Object.assign(res, {
                socialAttachments: post.media.map((m) => {
                    const type = { PHOTO: 'image' }[m.mediaFormat];
                    return { socialId: m.name, urls: { original: m.googleUrl }, type };
                }),
            });
        }
        switch (post.state) {
            case GmbLocalPostState.LIVE:
                res.published = PostPublicationStatus.PUBLISHED;
                break;
            case GmbLocalPostState.PROCESSING:
                if (this._isStuckInProcessingState(res.socialCreatedAt)) {
                    res.published = PostPublicationStatus.REJECTED;
                    res.errorData = PostError.POST_STUCK_PROCESSING;
                } else {
                    res.published = PostPublicationStatus.PENDING;
                }
                break;
            default:
                res.published = PostPublicationStatus.REJECTED;
        }
        return res;
    }

    mapToPlatformPost({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }): GmbLocalPost<GmbMediaItemInput> {
        const { socialId, language, text, attachments, attachmentsName, callToAction, postTopic, event, offer } = post;
        const mappedPost: Partial<GmbLocalPost<GmbMediaItemInput>> = {
            name: socialId,
            languageCode: language,
            summary: text,
            topicType: SeoPostTopic[postTopic] ?? SeoPostTopic.STANDARD,
        };

        if (callToAction?.actionType && callToAction?.actionType !== CallToActionType.NONE) {
            const actionType = GmbActionType[callToAction?.actionType];
            mappedPost.callToAction = {
                actionType: actionType ?? null,
                url: actionType === GmbActionType.CALL ? '' : (callToAction?.url ?? ''),
            };
        }

        if ([SeoPostTopic.EVENT, SeoPostTopic.OFFER].includes(postTopic)) {
            mappedPost.event =
                event && event.startDate && event.endDate
                    ? {
                          title: event.title ?? '',
                          schedule: {
                              startDate: toGmbDate(new Date(event.startDate)),
                              startTime: {
                                  hours: 0,
                                  minutes: 0,
                                  seconds: 0,
                              },
                              endDate: toGmbDate(new Date(event.endDate)),
                              endTime: {
                                  hours: 23,
                                  minutes: 59,
                                  seconds: 59,
                              },
                          },
                      }
                    : undefined;
        }

        if (postTopic === SeoPostTopic.OFFER) {
            delete mappedPost.callToAction;

            mappedPost.offer = {
                couponCode: offer?.couponCode ?? '',
                redeemOnlineUrl: offer?.onlineUrl ?? '',
                termsConditions: offer?.termsConditions ?? '',
            };
        }

        if (attachments) {
            mappedPost.media = attachments.map((attachment) => ({
                mediaFormat: GmbMediaFormat.PHOTO,
                description: attachmentsName ?? attachment.title ?? '',
                sourceUrl: attachment?.urls?.igFit ?? attachment?.urls?.original,
            }));
        }

        return mappedPost as GmbLocalPost<GmbMediaItemInput>;
    }

    private _isStuckInProcessingState(socialCreatedAt: Date): boolean {
        const now = new Date();
        const isStuckInProcessingState =
            DateTime.fromJSDate(socialCreatedAt)
                .plus({ milliseconds: GMB_PUBLISH_POST_MAX_RETRIES * GMB_PUBLISH_POST_RETRY_INTERVAL })
                .toJSDate() <= now;
        return isStuckInProcessingState;
    }
}

function toGmbDate(date: Date): { day: number; month: number; year: number } {
    return { day: date.getDate(), month: date.getMonth() + 1, year: date.getFullYear() };
}

function toMalouDate(date) {
    return new Date(date?.year, date?.month - 1, date.day);
}
