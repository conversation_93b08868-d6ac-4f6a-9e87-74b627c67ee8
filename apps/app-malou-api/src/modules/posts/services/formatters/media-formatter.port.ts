import { MediaType, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { Media } from ':modules/media/entities/media.entity';
import { CropOptions, FileMetadata } from ':modules/media/services/image-resizers/image-resizer.port.interface';

import { MediaFormatterPort } from './media-formatter.port.interface';
import { FbMediaFormatter } from './platforms/fb-media-formatter.adapter';
import { ReelsMediaFormatter } from './platforms/reels-media-formatter.adapter';
import { StoryMediaFormatter } from './platforms/story-media-formatter.adapter';

export enum FormatterType {
    FB = 'fb',
    REELS = 'reels',
    STORY = 'story',
}

export class MediaFormatter implements MediaFormatterPort {
    private _formatter: MediaFormatterPort | undefined;
    media: Media;
    formatterType: FormatterType;

    constructor(media: Media, formatterType: FormatterType) {
        this.media = media;
        this.formatterType = formatterType;
    }

    async formatMedia(
        inputPath: string,
        outputPath: string,
        platform: PlatformKey,
        mediaType: MediaType,
        resizeParams?: CropOptions,
        options?: {
            shouldForceResizeToRecommendedSize?: boolean;
        }
    ): Promise<FileMetadata> {
        logger.info('[MediaFormatter] formatMedia', { inputPath, outputPath, platform, mediaType, resizeParams, options });
        this._initializeFormatter();
        if (!this._formatter) {
            throw new Error('Formatter not initialized');
        }
        return this._formatter.formatMedia(inputPath, outputPath, platform, mediaType, resizeParams, options);
    }

    private _initializeFormatter(): void {
        switch (this.formatterType) {
            case FormatterType.REELS:
                this._formatter = new ReelsMediaFormatter(this.media);
                break;
            case FormatterType.FB:
                this._formatter = new FbMediaFormatter(this.media);
                break;
            case FormatterType.STORY:
                this._formatter = new StoryMediaFormatter(this.media);
                break;
            default:
                throw new Error('Invalid formatter type');
        }
    }
}
