import { isNil } from 'lodash';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, SemanticAnalysisFetchStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GenerateReviewSemanticAnalysisService } from ':modules/ai/services/generate-review-semantic-analysis.service';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { FetchReviewSemanticAnalysisProducerPayload } from ':modules/segment-analyses/queues/review-semantic-analysis/review-semantic-analysis.producer';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { SaveSegmentAnalysesService } from ':modules/segment-analyses/services/save-segment-analyses.service';

@singleton()
export class AnalyzeReviewSemanticallyUseCase {
    constructor(
        private readonly _generateReviewSemanticAnalysisService: GenerateReviewSemanticAnalysisService,
        private readonly _saveSegmentAnalysesService: SaveSegmentAnalysesService,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _segmentAnalysesRepository: SegmentAnalysesRepository
    ) {}

    async execute({ reviewId, restaurantId, isPrivateReview }: FetchReviewSemanticAnalysisProducerPayload): Promise<void> {
        try {
            const review: { socialId: string; key: PlatformKey } | null = isPrivateReview
                ? { socialId: reviewId, key: PlatformKey.PRIVATE }
                : await this._reviewsRepository.findOne({
                      filter: { _id: toDbId(reviewId) },
                      projection: { socialId: 1, key: 1 },
                      options: { lean: true },
                  });

            if (!review) {
                logger.error('[AnalyzeReviewSemanticallyUseCase] Review not found', { reviewId, restaurantId, isPrivateReview });
                throw new MalouError(MalouErrorCode.REVIEW_NOT_FOUND, { metadata: { reviewId, restaurantId, isPrivateReview } });
            }

            const existingSegmentAnalysis = await this._segmentAnalysesRepository.findOne({
                filter: { reviewSocialId: review.socialId, platformKey: review.key },
                projection: { _id: 1 },
                options: { lean: true },
            });

            if (existingSegmentAnalysis) {
                logger.info('[AnalyzeReviewSemanticallyUseCase] Review already has semantic analysis', {
                    reviewId,
                    restaurantId,
                    isPrivateReview,
                });
                return;
            }

            const segmentAnalyses = await this._generateReviewSemanticAnalysisService.execute({
                relatedEntityId: reviewId,
                restaurantId,
                isPrivateReview,
            });

            if (isNil(segmentAnalyses)) {
                logger.error('[AnalyzeReviewSemanticallyUseCase] Failed to generate semantic analysis', {
                    reviewId,
                    restaurantId,
                    isPrivateReview,
                });
                throw new MalouError(MalouErrorCode.FAILED_SEMANTIC_ANALYSIS, { metadata: { reviewId, restaurantId, isPrivateReview } });
            }

            if (segmentAnalyses.length === 0) {
                logger.info('[AnalyzeReviewSemanticallyUseCase] No semantic analysis results', {
                    reviewId,
                    restaurantId,
                    isPrivateReview,
                });
                await this._updateReviewSemanticAnalysisFetchStatus(reviewId, SemanticAnalysisFetchStatus.NO_RESULTS, isPrivateReview);
                return;
            }

            await this._saveSegmentAnalysesService.execute({
                segmentAnalyses,
                restaurantId,
            });

            await this._updateReviewSemanticAnalysisFetchStatus(reviewId, SemanticAnalysisFetchStatus.DONE, isPrivateReview);
            logger.info('[AnalyzeReviewSemanticallyUseCase] Semantic analysis generated successfully', {
                reviewId,
                restaurantId,
                isPrivateReview,
            });
        } catch (error: any) {
            logger.error('[AnalyzeReviewSemanticallyUseCase] Failed to fetch semantic analysis', { reviewId, error });
            if (error.malouErrorCode === MalouErrorCode.REVIEW_NOT_FOUND) {
                throw error;
            }
            await this._updateReviewSemanticAnalysisFetchStatus(reviewId, SemanticAnalysisFetchStatus.FAILED, isPrivateReview);
        }
    }

    private async _updateReviewSemanticAnalysisFetchStatus(
        reviewId: string,
        status: SemanticAnalysisFetchStatus,
        isPrivateReview: boolean
    ): Promise<void> {
        if (isPrivateReview) {
            await this._privateReviewsRepository.updateOne({
                filter: { _id: toDbId(reviewId) },
                update: { semanticAnalysisFetchStatus: status },
            });
            return;
        }
        await this._reviewsRepository.updateOne({
            filter: { _id: toDbId(reviewId) },
            update: { semanticAnalysisFetchStatus: status },
        });
    }
}
