import { singleton } from 'tsyringe';

import { GetReviewAnalysesChartDataByRestaurantIdResponseDto } from '@malou-io/package-dto';
import { toDbIds } from '@malou-io/package-models';
import { isNotNil } from '@malou-io/package-utils';

import PlatformsRepository from ':modules/platforms/platforms.repository';
import { SegmentAnalysesChartDataDtoMapper } from ':modules/segment-analyses/mappers/segment-analyses-chart-data.mapper.dto';
import { SegmentAnalysesChartFilter } from ':modules/segment-analyses/segment-analyses.interface';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';

@singleton()
export class GetSegmentAnalysesChartDataUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _segmentAnalysesRepository: SegmentAnalysesRepository,
        private readonly _segmentAnalysesChartDataDtoMapper: SegmentAnalysesChartDataDtoMapper,
        private readonly _segmentAnalysesParentTopicsRepository: SegmentAnalysisParentTopicsRepository
    ) {}

    async execute(segmentAnalysesFilter: SegmentAnalysesChartFilter): Promise<GetReviewAnalysesChartDataByRestaurantIdResponseDto> {
        const platforms = await this._platformsRepository.find({
            filter: { restaurantId: { $in: toDbIds(segmentAnalysesFilter.restaurantIds) } },
            projection: { socialId: 1, restaurantId: 1 },
            options: { lean: true },
        });

        const platformsWithRestaurantIdAndSocialId: { restaurantId: string; socialId: string }[] = platforms
            .map(({ restaurantId, socialId }) =>
                socialId
                    ? {
                          restaurantId: restaurantId.toString(),
                          socialId,
                      }
                    : null
            )
            .filter(isNotNil);
        const privatePlatformsWithRestaurantIdAndSocialId = segmentAnalysesFilter.restaurantIds.map((restaurantId) => ({
            restaurantId: restaurantId.toString(),
            socialId: restaurantId.toString(),
        }));
        const allPlatformsWithRestaurantIdAndSocialId = [
            ...platformsWithRestaurantIdAndSocialId,
            ...privatePlatformsWithRestaurantIdAndSocialId,
        ];

        const parentTopics = await this._segmentAnalysesParentTopicsRepository.getByRestaurantIds(segmentAnalysesFilter.restaurantIds);
        const parentTopicIds = parentTopics.map((parentTopic) => parentTopic.id);
        const segmentAnalysesReviewCount = await this._segmentAnalysesRepository.getSegmentAnalysesReviewCountByCategoryAndSentiment({
            reviewSocialCreatedAt: {
                $gte: segmentAnalysesFilter.startDate,
                $lte: segmentAnalysesFilter.endDate,
            },
            platformKey: { $in: segmentAnalysesFilter.keys },
            segmentAnalysisParentTopicIds: { $in: toDbIds(parentTopicIds) },
        });

        return this._segmentAnalysesChartDataDtoMapper.fromSegmentAnalysesReviewCountToReviewAnalysesChartDataDto(
            segmentAnalysesReviewCount,
            allPlatformsWithRestaurantIdAndSocialId
        );
    }
}
