import { groupBy, slice, uniqBy } from 'lodash';
import { singleton } from 'tsyringe';

import { GetSegmentAnalysesTopTopicsResponseDto, SegmentAnalysesTopicDto } from '@malou-io/package-dto';
import { ISegmentAnalysis } from '@malou-io/package-models';
import { ApplicationLanguage, EMPTY_KEY, isNotNil, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { SegmentAnalysesTopTopicsFilter } from ':modules/segment-analyses/segment-analyses.interface';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { SegmentAnalysisParentTopic } from ':modules/segment-analysis-parent-topics/entities/segment-analysis-parent-topic.entity';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';

@singleton()
export class GetSegmentAnalysesTopTopicsUseCase {
    readonly TOP_TOPICS_COUNT = 6;
    readonly DISPLAY_EXCLUDED_TOPICS_THRESHOLD_COUNT = 20;
    readonly DISPLAY_EXCLUDED_TOPICS_THRESHOLD_RATE = 3;

    constructor(
        private readonly _segmentAnalysesRepository: SegmentAnalysesRepository,
        private readonly _segmentAnalysesParentTopicsRepository: SegmentAnalysisParentTopicsRepository
    ) {}

    async execute(segmentAnalysesFilter: SegmentAnalysesTopTopicsFilter): Promise<GetSegmentAnalysesTopTopicsResponseDto> {
        const topics = await this._segmentAnalysesParentTopicsRepository.getByRestaurantIds([segmentAnalysesFilter.restaurantId]);

        const segmentAnalysesForTopics = await this._segmentAnalysesRepository.find({
            filter: {
                segmentAnalysisParentTopicIds: { $in: topics.map((t) => t.id) },
                reviewSocialCreatedAt: {
                    $gte: segmentAnalysesFilter.startDate,
                    $lte: segmentAnalysesFilter.endDate,
                },
                platformKey: { $in: segmentAnalysesFilter.keys },
                subcategory: null,
            },
            options: { lean: true },
        });

        const mostNegativeTopics = this._getTopTopics(segmentAnalysesForTopics, topics, ReviewAnalysisSentiment.NEGATIVE);
        const mostPositiveTopics = this._getTopTopics(
            segmentAnalysesForTopics,
            topics,
            ReviewAnalysisSentiment.POSITIVE,
            mostNegativeTopics
        );

        return {
            positiveTopics: mostPositiveTopics,
            negativeTopics: mostNegativeTopics,
        };
    }

    private readonly _getTopTopics = (
        segmentAnalyses: ISegmentAnalysis[],
        topics: SegmentAnalysisParentTopic[],
        sentiment: ReviewAnalysisSentiment,
        excludedTopics?: SegmentAnalysesTopicDto[]
    ): SegmentAnalysesTopicDto[] => {
        const filteredSegmentAnalyses = segmentAnalyses
            .filter((s) => s.sentiment === sentiment)
            .filter((s) => s.category !== ReviewAnalysisTag.OVERALL_EXPERIENCE);
        const duplicatedFilteredSegmentAnalyses = filteredSegmentAnalyses
            .map((segmentAnalysis) => {
                return segmentAnalysis.segmentAnalysisParentTopicIds?.length
                    ? segmentAnalysis.segmentAnalysisParentTopicIds.map((id) => ({
                          ...segmentAnalysis,
                          segmentAnalysisParentTopicIds: [id],
                      }))
                    : [segmentAnalysis];
            })
            .flat();
        const groupedFilteredTopics = Object.fromEntries(
            Object.entries(
                groupBy(duplicatedFilteredSegmentAnalyses, (segment) => {
                    const parentTopicId = segment.segmentAnalysisParentTopicIds?.find((id) =>
                        topics.some((topic) => topic.id === id.toString())
                    );
                    return parentTopicId ? parentTopicId.toString() : EMPTY_KEY;
                })
            ).filter(([key]) => key !== EMPTY_KEY)
        );
        const orderedGroupedTopics = Object.entries(groupedFilteredTopics)
            .filter(([_topic, segments]) => segments.length > 0)
            .map(([topic, segments]: [string, ISegmentAnalysis[]]): [string, ISegmentAnalysis[]] => {
                const uniqSegmentAnalysesByReviewAndCategory = uniqBy(
                    segments,
                    (s) => `${s.reviewSocialId}-${s.platformKey}-${s.category}`
                );
                return [topic, uniqSegmentAnalysesByReviewAndCategory];
            })
            .sort(([topicA, segmentsA], [topicB, segmentsB]) => {
                if (segmentsA.length > segmentsB.length) {
                    return -1;
                }
                if (segmentsA.length < segmentsB.length) {
                    return 1;
                }
                return topicA.localeCompare(topicB);
            });
        const orderedGroupedFilteredTopics = orderedGroupedTopics.filter(([topic, segments]) => {
            const topicName = topics.find((t) => t.id === topic)?.name;
            const topicInExcludedList = excludedTopics?.find((excludedTopic) => excludedTopic.name === topicName);
            if (!topicInExcludedList) {
                return true;
            }
            const isPositiveAndTooImportant =
                sentiment === ReviewAnalysisSentiment.POSITIVE &&
                this._isPositiveTopicTooImportantToBeExcluded(segments.length, topicInExcludedList.negativeCount ?? 0);
            return isPositiveAndTooImportant;
        });

        const topTopics = slice(orderedGroupedFilteredTopics, 0, this.TOP_TOPICS_COUNT).map(([parentTopicId, segments]) => {
            const associatedTopic = topics.find((t) => t.id === parentTopicId);
            return associatedTopic
                ? {
                      name: associatedTopic?.name,
                      translations: {
                          [ApplicationLanguage.EN]: associatedTopic?.translations?.[ApplicationLanguage.EN] ?? null,
                          [ApplicationLanguage.FR]: associatedTopic?.translations?.[ApplicationLanguage.FR] ?? null,
                          [ApplicationLanguage.ES]: associatedTopic?.translations?.[ApplicationLanguage.ES] ?? null,
                          [ApplicationLanguage.IT]: associatedTopic?.translations?.[ApplicationLanguage.IT] ?? null,
                      },
                      positiveCount: sentiment === ReviewAnalysisSentiment.POSITIVE ? segments.length : null,
                      negativeCount: sentiment === ReviewAnalysisSentiment.NEGATIVE ? segments.length : null,
                  }
                : null;
        });
        return topTopics.filter(isNotNil);
    };

    private _isPositiveTopicTooImportantToBeExcluded(topicCount: number, negativeSentimentCount: number): boolean {
        if (!topicCount) {
            return false;
        }
        const isAboveCountLimit = topicCount > this.DISPLAY_EXCLUDED_TOPICS_THRESHOLD_COUNT;
        const isWidelyAboveNegativeCount =
            !!negativeSentimentCount && topicCount > this.DISPLAY_EXCLUDED_TOPICS_THRESHOLD_RATE * negativeSentimentCount;
        return isAboveCountLimit || isWidelyAboveNegativeCount;
    }
}
