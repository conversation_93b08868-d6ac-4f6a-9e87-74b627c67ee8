import { DateTime } from 'luxon';
import { ReadPreference } from 'mongodb';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { SimpleRestaurant } from '@malou-io/package-dto';
import { DbId, EntityRepository, ID, IRestaurant, PopulateBuilderHelper, RestaurantModel, toDbId, toDbIds } from '@malou-io/package-models';
import {
    AiInteractionType,
    BusinessCategory,
    getPlatformDefinition,
    MalouErrorCode,
    NfcType,
    PlatformAccessStatus,
    PlatformAccessType,
    PlatformDataFetchedStatus,
    PlatformKey,
    PostPublicationStatus,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { RankedRestaurant } from ':modules/keywords/entities/ranked-restaurant.entity';
import { Restaurant } from ':modules/restaurants/entities/restaurant.entity';

export type DeeplyPopulatedRestaurant = PopulateBuilderHelper<
    IRestaurant,
    [
        { path: 'category'; populate: [{ path: 'platformCategories' }] },
        { path: 'categoryList'; populate: [{ path: 'platformCategories' }] },
        { path: 'attributeList'; populate: [{ path: 'attribute'; populate: [{ path: 'platformAttributes' }] }] },
        {
            path: 'managers';
            populate: [
                {
                    path: 'user';
                    select: {
                        _id: 1;
                        name: 1;
                        lastname: 1;
                        email: 1;
                        profilePicture: 1;
                        role: 1;
                    };
                },
            ];
        },
        { path: 'logo' },
        { path: 'cover' },
        { path: 'bricks' },
        { path: 'organization' },
        { path: 'availableHoursTypes' },
    ]
>;

@singleton()
export default class RestaurantsRepository extends EntityRepository<IRestaurant> {
    constructor() {
        super(RestaurantModel);
    }

    async getAllIds(): Promise<string[]> {
        const all = await this.find({
            filter: { active: true },
            options: { lean: true },
            projection: { _id: 1 },
        });
        return all.map((restaurant) => restaurant._id.toString());
    }

    getRestaurantById(restaurantId: string): Promise<IRestaurant | null> {
        return this.findOne({
            filter: { _id: toDbId(restaurantId) },
            options: { lean: true },
        });
    }

    getRestaurantsById(restaurantIds: string[]): Promise<IRestaurant[]> {
        return this.find({
            filter: { _id: { $in: toDbIds(restaurantIds) } },
            options: { lean: true },
        });
    }

    getRestaurantByIdPopulated = (id: string): Promise<DeeplyPopulatedRestaurant | null> =>
        this.findOne({
            filter: { _id: toDbId(id) },
            projection: {
                // $slice only take the first 100 elements but it doesnt work like a normal projection
                // it will return the whole array and all other fields
                specialHours: { $slice: 100 },
            },
            options: {
                populate: [
                    { path: 'category', populate: [{ path: 'platformCategories' }] },
                    { path: 'categoryList', populate: [{ path: 'platformCategories' }] },
                    { path: 'attributeList', populate: [{ path: 'attribute', populate: [{ path: 'platformAttributes' }] }] },
                    {
                        path: 'managers',
                        populate: [
                            {
                                path: 'user',
                                select: {
                                    _id: 1,
                                    name: 1,
                                    lastname: 1,
                                    email: 1,
                                    profilePicture: 1,
                                    role: 1,
                                },
                            },
                        ],
                    },
                    { path: 'logo' },
                    { path: 'cover' },
                    { path: 'bricks' },
                    { path: 'organization' },
                    { path: 'availableHoursTypes' },
                ],
                lean: true,
            },
        }) as any as Promise<DeeplyPopulatedRestaurant | null>;

    getRestaurantWithAttributesById(_id: DbId) {
        return this.findOne({
            filter: { _id },
            options: {
                lean: true,
                populate: [
                    {
                        path: 'attributeList',
                        populate: [
                            {
                                path: 'attribute',
                                populate: [{ path: 'platformAttributes' }],
                            },
                        ],
                    },
                ],
            },
        });
    }

    async getRestaurantsWithoutSticker(): Promise<Restaurant[]> {
        const pipeline = [
            {
                $lookup: {
                    from: 'nfcs',
                    localField: '_id',
                    foreignField: 'restaurantId',
                    as: 'sticker',
                    pipeline: [
                        {
                            $match: {
                                type: NfcType.STICKER,
                            },
                        },
                    ],
                },
            },
            {
                $match: {
                    sticker: [],
                },
            },
        ];
        const results = await (this.aggregate(pipeline) as any as Promise<IRestaurant[]>);

        return results.map((restaurant) => this._toEntity(restaurant));
    }

    incrementRestaurantAiCallCount({
        restaurantId,
        feature,
        value = 1,
    }: {
        restaurantId: DbId;
        feature?: AiInteractionType;
        value?: number;
    }) {
        logger.info(`Incrementing restaurant AI call count for restaurant ${restaurantId}`, { restaurantId, value, feature });
        return this.findOneAndUpdate({
            filter: { _id: restaurantId },
            update: {
                $inc: {
                    'ai.monthlyCallCount': value,
                    'ai.callCount': value,
                },
            },
        });
    }

    async startUpdateReviews(restaurantId: string, platforms: PlatformKey[]) {
        const defaultPlatformState = {
            status: PlatformDataFetchedStatus.PENDING,
            lastTried: null,
            error: null,
        };
        const asyncPlatformState = {
            status: PlatformDataFetchedStatus.ASYNC,
            lastTried: null,
            error: null,
        };

        const restaurant = await this.getRestaurantById(restaurantId);

        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                metadata: {
                    restaurantId,
                },
            });
        }

        // TODO: to check again, probably a better / more lisible way to do this
        // + let's try to avoid the whole currentState (here only reviews are updated)
        const currentState = { ...(restaurant?.currentState ?? {}) };
        const newState = {
            ...currentState,
            reviews: {
                ...(currentState.reviews ?? {}),
            },
        };

        newState.reviews.fetched = Object.fromEntries(
            new Map(
                platforms.map((platformName) => {
                    const platform = getPlatformDefinition(platformName);
                    return platform?.isAsynchronouslyScrapped ? [platformName, asyncPlatformState] : [platformName, defaultPlatformState];
                })
            )
        );

        await this.findOneAndUpdate({
            filter: { _id: restaurant._id },
            update: {
                currentState: newState,
                reviewsLastUpdate: new Date(),
            },
        });
    }

    async startUpdateComments(restaurantId: ID, platforms: PlatformKey[]) {
        const defaultPlatformState = {
            status: PlatformDataFetchedStatus.PENDING,
            lastTried: null,
            error: null,
        };
        const restaurant = await this.findOne({ filter: { _id: toDbId(restaurantId) } });
        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                metadata: {
                    restaurantId,
                },
            });
        }
        const currentState = { ...(restaurant?.toJSON()?.currentState ?? {}) };
        const newState = {
            ...currentState,
            comments: {
                ...(currentState.comments ?? {}),
            },
        };
        newState.comments.fetched = Object.fromEntries(
            new Map(
                platforms.map((platformName) => {
                    return [platformName, defaultPlatformState];
                })
            )
        );

        await this.findOneAndUpdate({
            filter: { _id: toDbId(restaurantId) },
            update: {
                currentState: newState,
                commentsLastUpdate: new Date(),
            },
        });
    }

    filterActiveRestaurants = (restaurantIds: DbId[]) =>
        this.find({
            filter: { _id: { $in: restaurantIds }, active: true },
            options: { lean: true },
        });

    countRestaurantsInOrganizations = (organizationIds: DbId[]) =>
        this.aggregate([
            {
                $match: { organizationId: { $in: organizationIds }, active: true },
            },
            { $count: 'count' },
        ]);

    countRestaurantsInOrganization(organizationId: string): Promise<number> {
        const dbId = toDbId(organizationId);
        return this.countDocuments({
            filter: {
                organizationId: dbId,
            },
        });
    }

    deletePlatformAccess(restaurantId: string, platformKey: PlatformKey) {
        return this.findOneAndUpdate({
            filter: { _id: toDbId(restaurantId) },
            update: {
                $pull: {
                    access: {
                        platformKey,
                    },
                },
            },
        });
    }

    getRestaurantsWithoutScheduledPosts = async (): Promise<DbId[]> => {
        const today = new Date();

        const restaurants = await this.aggregate([
            {
                $match: {
                    active: true,
                },
            },
            {
                $lookup: {
                    from: 'posts',
                    localField: '_id',
                    foreignField: 'restaurantId',
                    as: 'posts',
                },
            },
            {
                $addFields: {
                    postsPlanned: {
                        $size: {
                            $filter: {
                                input: '$posts',
                                as: 'post',
                                cond: {
                                    $and: [
                                        { $eq: ['$$post.published', PostPublicationStatus.PENDING] },
                                        { $gte: ['$$post.plannedPublicationDate', today] },
                                        { $gte: ['$$post.createdAt', DateTime.now().minus({ month: 1 }).toJSDate()] },
                                    ],
                                },
                            },
                        },
                    },
                },
            },
            {
                $match: {
                    postsPlanned: { $eq: 0 },
                },
            },
            {
                $project: {
                    _id: 1,
                },
            },
        ]);

        return restaurants.map((restaurant) => restaurant._id);
    };

    startUpdateMessages = async (restaurantId: DbId, platforms: PlatformKey[]) => {
        const restaurant = await this.findOne({ filter: { _id: restaurantId } });
        const asyncPlatformState = {
            status: PlatformDataFetchedStatus.ASYNC,
            lastTried: null,
            error: null,
        };
        const currentState = { ...(restaurant?.toJSON()?.currentState ?? {}) };
        const newState = {
            ...currentState,
            messages: {
                ...(currentState.messages ?? {}),
            },
        };
        // (?) setting property fetched while creating newState does not work
        newState.messages.fetched = Object.fromEntries(new Map(platforms.map((platformName) => [platformName, asyncPlatformState])));
        newState.messages.lastSync = new Date();

        await this.findOneAndUpdate({
            filter: { _id: restaurantId },
            update: {
                currentState: newState,
            },
        });
    };

    async resetRestaurantAiCredits(): Promise<void> {
        await this.updateMany({
            filter: { ai: { $ne: null } },
            update: { 'ai.monthlyCallCount': 0 },
            options: { lean: true },
        });
    }

    async addBricksToRestaurant(restaurantId: string, brickIds: string[]): Promise<void> {
        await this.findOneAndUpdate({
            filter: { _id: toDbId(restaurantId) },
            update: { $addToSet: { bricks: toDbIds(brickIds) } },
        });
    }

    async updateRestaurantBricks(restaurantId: string, brickIds: string[]): Promise<void> {
        await this.findOneAndUpdate({
            filter: { _id: toDbId(restaurantId) },
            update: { bricks: toDbIds(brickIds) },
        });
    }

    async findRestaurantForRankingById(restaurantId: string): Promise<RankedRestaurant> {
        const restaurant = await this.findOne({
            filter: { _id: toDbId(restaurantId) },
            projection: { name: 1, placeId: 1, internalName: 1, address: 1 },
            options: { lean: true },
        });
        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                message: 'Restaurant not found',
                metadata: { restaurantId },
            });
        }
        return new RankedRestaurant(restaurant);
    }

    async getRestaurantsForReviewsReportByIds(restaurantIds: string[]): Promise<SimpleRestaurant[]> {
        const restaurants = await this.find({
            filter: { _id: { $in: restaurantIds } },
            options: { lean: true, populate: [{ path: 'logo' }] },
            projection: { _id: 1, name: 1, logo: 1, address: 1, formattedAddress: 1, type: 1 },
        });

        return restaurants.map((restaurant) => {
            return {
                _id: restaurant._id.toString(),
                id: restaurant._id.toString(),
                name: restaurant.internalName ?? restaurant.name,
                address: restaurant.address?.formattedAddress,
                // eslint-disable-next-line max-len
                formattedAddress: `${restaurant.address?.formattedAddress}, ${restaurant.address?.postalCode} ${restaurant.address?.locality}`,
                type: restaurant.type,
                logo: restaurant.logo?.urls.small ?? restaurant.logo?.urls.original ?? '',
            };
        });
    }

    async isOrganizationIdLinkedToAtLeastOneRestaurant(organizationId: string): Promise<boolean> {
        const dbId = toDbId(organizationId);
        const res = await this.findOne({
            filter: { organizationId: dbId },
            options: { lean: true },
        });
        return !!res;
    }

    async updateRestaurantAccess(restaurantId: string, platformAccess: IRestaurant['access']): Promise<IRestaurant | null> {
        return this.findOneAndUpdate({
            filter: {
                _id: restaurantId,
            },
            update: { access: platformAccess },
        });
    }

    async getAllRestaurantsIdsForRoiInsights(): Promise<string[]> {
        const activeRestaurants = await this.find({
            filter: { active: true, type: BusinessCategory.LOCAL_BUSINESS },
            options: { lean: true },
            projection: { _id: 1 },
        });
        return activeRestaurants.map((restaurant) => restaurant._id.toString());
    }

    async getAllActiveLocationForUpdateSimilarRestaurants(): Promise<Pick<IRestaurant, '_id' | 'createdAt'>[]> {
        return this.find({
            filter: { active: true, type: BusinessCategory.LOCAL_BUSINESS },
            options: { lean: true },
            projection: { _id: 1, createdAt: 1 },
        });
    }

    getOpeningAndCreationDatesForRestaurant(restaurantId: string): Promise<Pick<IRestaurant, '_id' | 'openingDate' | 'createdAt'> | null> {
        return this.findOne({
            filter: { _id: toDbId(restaurantId) },
            options: { lean: true },
            projection: { _id: 1, openingDate: 1, createdAt: 1 },
        });
    }

    async addCalendarEventToRestaurant(restaurantId: string, calendarEventId: string): Promise<void> {
        const restaurant = await this.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            projection: { calendarEvents: 1 },
            options: { lean: true },
        });
        restaurant.calendarEvents.push(toDbId(calendarEventId));
        await this.upsert({
            filter: { _id: restaurantId },
            update: { calendarEvents: restaurant.calendarEvents },
        });
    }

    private _toEntity(restaurant: IRestaurant): Restaurant {
        return new Restaurant(restaurant);
    }

    async getAllActiveRestaurantsToCheckRoiEligibility({
        startDate,
        endDate,
    }: {
        startDate?: Date;
        endDate?: Date;
    }): Promise<Pick<IRestaurant, '_id' | 'createdAt' | 'openingDate' | 'roiActivated' | 'name'>[]> {
        const filter: { active: boolean; type: BusinessCategory; $or?: any[] } = { active: true, type: BusinessCategory.LOCAL_BUSINESS };
        if (startDate || endDate) {
            const dateFilter: { $gte?: Date; $lte?: Date } = {};
            if (startDate) {
                dateFilter.$gte = startDate;
            }
            if (endDate) {
                dateFilter.$lte = endDate;
            }
            filter.$or = [{ createdAt: dateFilter }, { openingDate: dateFilter }];
        }
        return await this.find({
            filter,
            options: { lean: true },
            projection: { _id: 1, createdAt: 1, openingDate: 1, roiActivated: 1, name: 1 },
        });
    }

    async upsertManyRestaurantsRoiStatus({
        restaurantsIds,
        activateRoi,
    }: {
        restaurantsIds: string[];
        activateRoi: boolean;
    }): Promise<void> {
        await this.updateMany({
            filter: { _id: { $in: toDbIds(restaurantsIds) } },
            update: { roiActivated: activateRoi },
            options: { lean: true },
        });
    }

    async getActiveRestaurantByPlaceId(placeId: string): Promise<IRestaurant | null> {
        return this.findOne({
            filter: { placeId, active: true },
            options: { lean: true },
        });
    }

    async getActiveRestaurantIdByPlaceId(placeId: string): Promise<string | null> {
        const res = await this.findOne({
            filter: { placeId, active: true },
            options: { lean: true },
            projection: { _id: 1 },
        });
        return res?._id?.toString() ?? null;
    }

    async getOrganizationIdByRestaurantId(restaurantId: string): Promise<string | null> {
        const res = await this.findOne({
            filter: { _id: toDbId(restaurantId) },
            options: { lean: true },
            projection: { organizationId: 1 },
        });
        return res?.organizationId.toString() ?? null;
    }

    async updateRestaurantAccessByPlatformKey({
        restaurantId,
        platformKey,
        status,
        accessType,
        active,
    }: {
        restaurantId: string;
        platformKey: PlatformKey;
        status: PlatformAccessStatus;
        accessType: PlatformAccessType;
        active: boolean;
    }): Promise<void> {
        const restaurant = await this.findOne({
            filter: { _id: restaurantId },
            projection: { access: 1 },
            options: { lean: true },
        });
        assert(restaurant);

        const idx = restaurant.access.findIndex((a) => a.platformKey === platformKey);
        assert(idx !== -1);

        restaurant.access[idx] = {
            ...restaurant.access[idx],
            status,
            accessType,
            active,
            lastVerified: new Date(),
            lastUpdated: new Date(),
        };

        await this.findOneAndUpdate({
            filter: {
                _id: restaurantId,
            },
            update: { access: restaurant.access },
        });

        return;
    }

    async getRestaurantsEventCountryAndSpecialHoursByEventId(
        calendarEventId: string
    ): Promise<{ id: string; calendarEventsCountry: IRestaurant['calendarEventsCountry']; specialHours: IRestaurant['specialHours'] }[]> {
        const restaurants = await this.find({
            filter: { calendarEvents: toDbId(calendarEventId), active: true, type: BusinessCategory.LOCAL_BUSINESS },
            options: {
                lean: true,
                ReadPreference: ReadPreference.SECONDARY_PREFERRED,
                comment: 'getRestaurantsEventCountryAndSpecialHoursByEventId',
            },
            projection: { _id: 1, calendarEventsCountry: 1, specialHours: 1 },
        });
        return restaurants.map((restaurant) => ({
            id: restaurant._id.toString(),
            specialHours: restaurant.specialHours,
            calendarEventsCountry: restaurant.calendarEventsCountry,
        }));
    }

    async isYextActivatedForRestaurant(restaurantId: string): Promise<boolean> {
        const res = await this.findOne({
            filter: { _id: toDbId(restaurantId), isYextActivated: true, active: true },
            options: { lean: true, ReadPreference: ReadPreference.SECONDARY_PREFERRED },
        });
        return !!res;
    }

    async getRestaurantsNamesByIds(restaurantIds: string[]): Promise<string[]> {
        const restaurants = await this.find({
            filter: { _id: { $in: toDbIds(restaurantIds) } },
            options: { lean: true, ReadPreference: ReadPreference.SECONDARY_PREFERRED },
            projection: { _id: 1, name: 1, internalName: 1 },
        });
        return restaurants.map((restaurant) => restaurant.internalName ?? restaurant.name);
    }

    getRestaurantName(restaurantId: string): Promise<string | null> {
        return this.findOne({
            filter: {
                _id: toDbId(restaurantId),
            },
            projection: { name: 1 },
            options: { lean: true },
        }).then((restaurant) => restaurant?.name ?? null);
    }

    async getRestaurantsCreatedAtByIds(restaurantIds: string[]): Promise<{ id: string; createdAt: Date }[]> {
        const restaurants = await this.find({
            filter: { _id: { $in: toDbIds(restaurantIds) } },
            options: { lean: true },
            projection: { _id: 1, createdAt: 1 },
        });
        return restaurants.map(({ _id, createdAt }) => ({ id: _id.toString(), createdAt: new Date(createdAt) }));
    }
}
