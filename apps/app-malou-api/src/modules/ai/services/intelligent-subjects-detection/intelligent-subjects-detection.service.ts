import { uniq } from 'lodash';
import { singleton } from 'tsyringe';

import { IR<PERSON>urant, IReview, toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    IntelligentSubjectAutomationRelatedEntity,
    isNotNil,
    ReviewAnalysisSentiment,
    REVIEWS_INTELLIGENT_SUBJECTS_TO_DETECT,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { ReviewsIntelligentSubjectsDetectionService } from ':microservices/reviews-intelligent-subjects-detection.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall } from ':modules/ai/helpers';
import { DetectIntelligentSubjectsPayload } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { IntelligentSubjectAutomation } from ':modules/automations/features/intelligent-subjects/entities/intelligent-subject-automation.entity';
import IntelligentSubjectAutomationsRepository from ':modules/automations/features/intelligent-subjects/repositories/intelligent-subjects.repository';
import { IntelligentSubjectsAutomationActionHandlerService } from ':modules/automations/features/intelligent-subjects/services/intelligent-subjects-automation-action-handler/intelligent-subjects-automation-action-handler.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';

@singleton()
export class IntelligentSubjectsDetectionService {
    constructor(
        private readonly _reviewsIntelligentSubjectsDetectionService: ReviewsIntelligentSubjectsDetectionService,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _intelligentSubjectAutomationsRepository: IntelligentSubjectAutomationsRepository,
        private readonly _intelligentSubjectsAutomationActionHandlerService: IntelligentSubjectsAutomationActionHandlerService
    ) {}

    async detectIntelligentSubjectsForReviews({ restaurantId, reviews }: { restaurantId: string; reviews: IReview[] }): Promise<void> {
        const platformKeys = uniq(reviews.map((review) => review.key));
        try {
            const isFeatureEnabledForRestaurant = await isFeatureAvailableForRestaurant({
                restaurantId,
                featureName: 'release-reviews-intelligent-subjects',
            });

            if (!isFeatureEnabledForRestaurant) {
                return;
            }

            const automations = await this._intelligentSubjectAutomationsRepository.getIntelligentSubjectAutomationsBySubjects({
                restaurantId: restaurantId,
                relatedEntities: [IntelligentSubjectAutomationRelatedEntity.REVIEWS],
                subjects: REVIEWS_INTELLIGENT_SUBJECTS_TO_DETECT,
            });

            const activeAutomations = automations.filter((automation) => automation.active);

            if (!activeAutomations?.length) {
                logger.info('[IntelligentSubjectsDetectionService] - No intelligent subjects automations enabled for restaurant', {
                    restaurantId,
                });
                return;
            }

            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                projection: { _id: 1, ai: 1 },
                options: { lean: true },
            });

            if (!restaurant) {
                logger.info('[IntelligentSubjectsDetectionService] - Restaurant not found', { restaurantId });
                return;
            }

            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const oldestActiveAutomationDate = automations
                .filter((automation) => automation.active)
                .map((automation) => automation.lastActiveUpdatedAt)
                .sort((a, b) => a.getTime() - b.getTime())?.[0];

            const reviewsWithoutIntelligentSubjects = reviews.filter((review) =>
                this._shouldDetectIntelligentSubjects(review, oldestActiveAutomationDate)
            );

            if (!reviewsWithoutIntelligentSubjects.length) {
                logger.info('[IntelligentSubjectsDetectionService] - No reviews with intelligent subjects to detect', {
                    restaurantId,
                    reviewIds: reviews.map((review) => review._id.toString()),
                    platformKeys,
                });
                return;
            }

            await Promise.all(
                reviewsWithoutIntelligentSubjects.map((review) =>
                    this._detectIntelligentSubjectsForReview({ review, restaurant, automations: activeAutomations })
                )
            );
        } catch (error: any) {
            logger.info('[IntelligentSubjectsDetectionService] - Error while detecting intelligent subjects', {
                error: error.stack,
                restaurantId,
                platformKeys,
            });
        }
    }

    private async _detectIntelligentSubjectsForReview({
        review,
        restaurant,
        automations,
    }: {
        review: IReview;
        restaurant: Pick<IRestaurant, 'ai' | '_id'>;
        automations: IntelligentSubjectAutomation[];
    }): Promise<void> {
        const restaurantId = restaurant._id.toString();
        const aiInteraction = await this._aiInteractionsRepository.createAiInteraction({
            type: AiInteractionType.INTELLIGENT_SUBJECTS_DETECTION,
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            relatedEntityId: review._id.toString(),
            restaurantId,
        });

        try {
            const payload: DetectIntelligentSubjectsPayload = this._computePayload(review);

            const response = await this._reviewsIntelligentSubjectsDetectionService.detectIntelligentSubjects(payload);

            const { aiResponse, aiInteractionDetails } = response;

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurant._id,
                feature: AiInteractionType.INTELLIGENT_SUBJECTS_DETECTION,
            });

            if (isNotNil(aiInteractionDetails)) {
                const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                    aiInteractionDetails?.[0],
                    restaurant._id
                );

                await this._aiInteractionsRepository.findOneAndUpdate({
                    filter: { _id: aiInteraction.id },
                    update: updatedAiInteraction,
                    options: { new: true },
                });
            }

            const reviewIntelligentSubjects = aiResponse.map((subject) => ({
                subject: subject.intelligentSubject,
                isDetected: subject.isDetected,
                sentiment: subject.sentiment || undefined,
            }));

            await this._reviewsRepository.findOneAndUpdate({
                filter: { _id: review._id },
                update: { intelligentSubjects: reviewIntelligentSubjects },
            });

            const reviewDetectedIntelligentSubjects = reviewIntelligentSubjects.filter(
                (subject) => subject.isDetected && subject.sentiment === ReviewAnalysisSentiment.NEGATIVE
            );
            const activeAutomationsWithMatchingSubjects = automations.filter((automation) => {
                return reviewDetectedIntelligentSubjects.some(
                    (intelligentSubject) => intelligentSubject.subject === automation.subject.toString()
                );
            });

            if (!activeAutomationsWithMatchingSubjects.length) {
                return;
            }

            await this._intelligentSubjectsAutomationActionHandlerService.handleIntelligentSubjectsAutomationsAction({
                automations: activeAutomationsWithMatchingSubjects,
                entityId: review._id.toString(),
            });
        } catch (error: any) {
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });

            throw error;
        }
    }

    private _computePayload(review: IReview): DetectIntelligentSubjectsPayload {
        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: AiInteractionType.INTELLIGENT_SUBJECTS_DETECTION,
            restaurantData: {
                reviewText: review.text ?? '',
                intelligentSubjects: REVIEWS_INTELLIGENT_SUBJECTS_TO_DETECT.map((s) => s.toLowerCase()),
            },
        };
    }

    private _shouldDetectIntelligentSubjects(review: IReview, oldestActiveAutomationDate: Date): boolean {
        const socialAvailableDate = review.socialUpdatedAt || review.socialCreatedAt;

        if (socialAvailableDate.getTime() < oldestActiveAutomationDate.getTime()) {
            return false;
        }
        if (review.intelligentSubjects?.length) {
            return false;
        }
        if (!review.text?.length) {
            return false;
        }
        if (review.comments?.length) {
            return false;
        }
        return true;
    }
}
