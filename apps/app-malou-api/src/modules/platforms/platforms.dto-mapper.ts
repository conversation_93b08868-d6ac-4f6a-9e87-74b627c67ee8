import { singleton } from 'tsyringe';

import { GetPlatformsByRestaurantIdsResponseDto, LatlngDto, PlatformDto } from '@malou-io/package-dto';
import { IPlatform, IRestaurant } from '@malou-io/package-models';

import { DescriptionDtoMapper } from ':modules/restaurants/mappers/description.dto-mapper';

@singleton()
export class PlatformsDtoMapper {
    constructor(private readonly descriptionDtoMapper: DescriptionDtoMapper) {}

    toDto(model: IPlatform): PlatformDto {
        return {
            id: model._id.toString(),
            key: model.key,
            restaurantId: model.restaurantId.toString(),
            socialId: model.socialId ?? undefined,
            parentSocialId: model.parentSocialId,
            socialLink: model.socialLink ?? undefined,
            businessSocialLinks: model.businessSocialLinks ?? undefined,
            address: model.address as PlatformDto['address'],
            category: model.category?.toString(),
            categoryList: model.categoryList?.map((id) => id.toString()) ?? [],
            descriptions:
                model.descriptions?.map((description) =>
                    this.descriptionDtoMapper.toDescriptionDto(description as IRestaurant['descriptions'][0])
                ) ?? [],
            isClaimed: model.isClaimed ?? false,
            menuUrl: model.menuUrl ?? undefined,
            name: model.name ?? undefined,
            openingDate: model.openingDate?.toISOString(),
            phone: model.phone ?? undefined,
            rating: model.rating ?? undefined,
            menu: model.menu,
            regularHours: model.regularHours ?? [],
            specialHours: model.specialHours
                ? model.specialHours.map((hour) => ({
                      startDate: hour.startDate,
                      endDate: hour.endDate ?? hour.startDate,
                      openTime: hour.openTime,
                      closeTime: hour.closeTime,
                      isClosed: hour.isClosed,
                  }))
                : [],
            website: model.website ?? undefined,
            latlng: model.latlng as LatlngDto,
            email: model.email ?? undefined,
            isClosedTemporarily: model.isClosedTemporarily ?? false,
            platformPropertiesToUpdate: model.platformPropertiesToUpdate ?? [],
            apiEndpoint: model.apiEndpoint ?? undefined,
            apiEndpointV2: model.apiEndpointV2,
            attributes: model.attributes ?? [],
            credentials: model.credentials?.map((id) => id.toString()) ?? [],
            lockedFields: model.lockedFields ?? [],
            unmappedCategories: model.unmappedCategories ?? [],
            unmappedHours: model.unmappedHours ?? undefined,
            watchedAccounts: model.watchedAccounts ?? [],
            hasTransitionedToNewPageExperience: model.hasTransitionedToNewPageExperience ?? false,
            createdAt: model.createdAt.toISOString(),
            updatedAt: model.updatedAt.toISOString(),
        };
    }

    toGetPlatformsByRestaurantIdsResponseDto(
        models: Pick<IPlatform, '_id' | 'key' | 'restaurantId' | 'credentials'>[]
    ): GetPlatformsByRestaurantIdsResponseDto[] {
        return models.map((model) => ({
            id: model._id.toString(),
            key: model.key,
            restaurantId: model.restaurantId.toString(),
            credentials: model.credentials?.map((id) => id.toString()) ?? [],
        }));
    }
}
