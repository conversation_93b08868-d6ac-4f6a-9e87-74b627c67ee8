import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { AppleBusinessConnectMapperService } from ':modules/platforms/platforms/apple-business-connect/apple-business-connect.mapper';
import {
    PublishOnPlatformResponse,
    RestaurantPopulatedToPublish,
} from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import AppleBusinessConnectApiProvider from ':providers/apple-business-connect/apple-business-connect.provider';

@singleton()
export class PublishOnAppleBusinessConnectPlatformUseCase {
    constructor(
        private readonly _appleBusinessConnectMapperService: AppleBusinessConnectMapperService,
        private readonly _appleBusinessConnectApiProvider: AppleBusinessConnectApiProvider
    ) {}

    async execute(restaurant: RestaurantPopulatedToPublish): Promise<PublishOnPlatformResponse> {
        assert(restaurant.appleBusinessConnect?.locationId);

        // Get Location details
        const locationId = restaurant.appleBusinessConnect?.locationId;
        const location = await this._appleBusinessConnectApiProvider.getLocationById({ locationId });

        // Generate new details according to restaurant data
        const newLocationDetails = await this._appleBusinessConnectMapperService.execute(restaurant);

        // Update information and location assets if logo or cover changed
        await this._appleBusinessConnectApiProvider.updateLocation({
            locationId,
            etag: location.etag,
            data: {
                id: location.id,
                locationDetails: {
                    ...location.locationDetails,
                    ...newLocationDetails,
                },
            },
        });

        return {
            success: true,
        };
    }
}
