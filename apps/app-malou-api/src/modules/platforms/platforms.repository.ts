import { ProjectionFields } from 'mongoose';
import { singleton } from 'tsyringe';

import { DbId, EntityRepository, IPlatform, IPlatformWithCredentials, PlatformModel, toDbId, toDbIds } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { filterByRequiredKeys } from ':helpers/validators/filter-by-required-keys';
import { SimilarRestaurantsRatingWithCount } from ':modules/diagnostics/diagnostic.interfaces';
import { MINIMUM_GOOGLE_RATING } from ':modules/platforms/platforms.constants';
import { Platform } from ':modules/platforms/platforms.entity';
import { PlatformWithRestaurantDetails } from ':modules/posts/posts.interface';

@singleton()
export default class PlatformsRepository extends EntityRepository<IPlatform> {
    constructor() {
        super(PlatformModel);
    }

    getPlatformById = async (platformId: string, projection: ProjectionFields<IPlatform> = {}): Promise<Platform | null> => {
        const platform = await this.findOne({
            filter: {
                _id: toDbId(platformId),
            },
            projection,
            options: { lean: true },
        });
        if (!platform) {
            return null;
        }
        return new Platform(platform);
    };

    getPlatformByRestaurantIdAndPlatformKey = async (
        restaurantId: string,
        platformKey: PlatformKey,
        projection: ProjectionFields<IPlatform> = {}
    ): Promise<Platform | null> => {
        const platform = await this.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
                key: platformKey,
            },
            projection,
            options: { lean: true },
        });
        if (!platform) {
            return null;
        }
        return new Platform(platform);
    };

    async getPlatformWithCredentials<T extends PlatformKey>({
        restaurantId,
        platformKey,
    }: {
        restaurantId: string;
        platformKey: T;
    }): Promise<IPlatformWithCredentials<T>> {
        const platform = (await this.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
                key: platformKey,
            },
            options: {
                populate: [{ path: 'credentials' }],
                lean: true,
            },
        })) as unknown as IPlatformWithCredentials<T>;

        return platform;
    }

    getPlatformsByRestaurantId = async (restaurantId: string): Promise<Platform[]> => {
        const platforms = await this.find({
            filter: {
                restaurantId: toDbId(restaurantId),
            },
            options: { lean: true },
        });
        return platforms.map((platform) => new Platform(platform));
    };

    getPlatformsByRestaurantIdAndPlatformKeys = async (restaurantId: string, platformKeys: PlatformKey[]): Promise<Platform[]> => {
        const platforms = await this.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                key: { $in: platformKeys },
            },
            options: { lean: true },
        });
        return platforms.map((platform) => new Platform(platform));
    };

    getPlatformsByRestaurantIdsAndPlatformKey = async (restaurantIds: string[], platformKey: PlatformKey): Promise<Platform[]> => {
        const platforms = await this.find({
            filter: {
                restaurantId: { $in: restaurantIds.map(toDbId) },
                key: platformKey,
            },
            options: { lean: true },
        });
        return platforms.map((platform) => new Platform(platform));
    };

    getPlatformsByRestaurantIdsAndPlatformKeys = async (restaurantIds: string[], platformKeys: PlatformKey[]): Promise<Platform[]> => {
        const platforms = await this.find({
            filter: {
                restaurantId: { $in: restaurantIds.map(toDbId) },
                key: { $in: platformKeys },
            },
            options: { lean: true },
        });
        return platforms.map((platform) => new Platform(platform));
    };

    getPlatformsBySocialIdAndPlatformKey = async (socialId: string, platformKey: PlatformKey): Promise<Platform[]> => {
        const platforms = await this.find({
            filter: {
                socialId,
                key: platformKey,
            },
            options: { lean: true },
        });
        return platforms.map((platform) => new Platform(platform));
    };

    async getUrlsByRestaurantId(restaurantId: string): Promise<{ url: string; platformKey: PlatformKey }[]> {
        const platforms = await this.find({
            filter: {
                restaurantId: toDbId(restaurantId),
            },
            projection: { socialLink: 1, key: 1 },
            options: { lean: true },
        });

        return filterByRequiredKeys(platforms, ['socialLink']).map(({ socialLink, key }) => ({ url: socialLink, platformKey: key }));
    }

    unshiftCredentials = async (platformId: DbId, credentialId: string): Promise<void> => {
        const platform = await this.findOne({ filter: { _id: platformId } });
        const creds = platform?.credentials ?? [];
        const newCredentials = creds.filter((c) => c.toString() !== credentialId.toString());
        newCredentials.unshift(toDbId(credentialId));
        await this.findOneAndUpdate({ filter: { _id: platformId }, update: { credentials: newCredentials } });
    };

    getWatchedAccountCountByRestaurantIdAndPlatformKey = async (restaurantId: string, platformKey: PlatformKey): Promise<number> => {
        const platform = await this.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
                key: platformKey,
            },
            options: { lean: true },
        });
        if (!platform) {
            return 0;
        }
        return platform.watchedAccounts?.length ?? 0;
    };

    async getAverageGoogleRatingAndBetterAheadCompetitorsAheadOfRating(
        restaurantIds: string[],
        rating: number
    ): Promise<SimilarRestaurantsRatingWithCount> {
        const aggregation = await this.aggregate([
            {
                $match: {
                    restaurantId: { $in: toDbIds(restaurantIds) },
                    key: PlatformKey.GMB,
                    rating: { $gt: rating },
                },
            },
            {
                $facet: {
                    averageRating: [
                        {
                            $match: {
                                rating: { $gt: MINIMUM_GOOGLE_RATING },
                            },
                        },
                        {
                            $group: {
                                _id: null,
                                averageRating: { $avg: '$rating' },
                            },
                        },
                    ],
                    betterCompetitorCount: [
                        {
                            $count: 'betterCompetitorCount',
                        },
                    ],
                },
            },
            {
                $project: {
                    averageRating: {
                        $first: '$averageRating.averageRating',
                    },
                    betterCompetitorCount: {
                        $first: '$betterCompetitorCount.betterCompetitorCount',
                    },
                },
            },
        ]);

        return {
            averageRating: aggregation[0]?.averageRating ?? MINIMUM_GOOGLE_RATING,
            betterCompetitorCount: aggregation[0]?.betterCompetitorCount ?? 0,
        };
    }

    async getPlatformsWithRestaurantDetailsByRestaurantIdsAndPlatformKeys(
        restaurantIds: string[],
        platformKeys: PlatformKey[]
    ): Promise<PlatformWithRestaurantDetails[]> {
        return (
            await this.find({
                filter: {
                    restaurantId: { $in: toDbIds(restaurantIds) },
                    key: { $in: platformKeys },
                },
                projection: { key: 1, socialId: 1, restaurantId: 1, _id: 1 },
                options: { lean: true, populate: [{ path: 'restaurant', select: { name: 1, internalName: 1, address: 1 } }] },
            })
        ).map((platform) => ({
            ...platform,
            id: platform._id.toString(),
            restaurantId: platform.restaurantId.toString(),
            socialId: platform.socialId?.toString(),
        }));
    }
}
