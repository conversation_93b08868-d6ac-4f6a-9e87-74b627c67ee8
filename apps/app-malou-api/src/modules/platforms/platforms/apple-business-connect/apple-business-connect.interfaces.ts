interface AppleBusinessConnectLocationBaseDetails {
    partnersLocationId: string; // unique identifier for the location in the partner's system
    partnersLocationVersion?: string;
    businessId?: string; // associated Apple-generated business ID, optional
    displayNames: AppleBusinessConnectLocationDisplayName[];
    locationStatus: AppleBusinessConnectLocationStatusWithDates;
    categories: string[]; // list of one or more Apple Maps categories: https://businessconnect.apple.com/docs/data-specification/resources/apple-categories.csv
    openingHoursByDay?: AppleBusinessConnectLocationHoursByDay[]; // recommended
    phoneNumbers?: AppleBusinessConnectLocationPhoneNumber[]; // recommended
    urls?: AppleBusinessConnectLocationUrl[];
    accessPoints?: AppleBusinessConnectLocationAccessPoint[];
    actionLinkDetails?: AppleBusinessConnectLocationActionLinkDetails;
    confidenceLevel?: number; // confidence in the accuracy of the location data at the time of delivery
    geometry?: string; // well-known binary (WKB) representation of a location as a hexadecimal string: https://businessconnect.apple.com/docs/data-specification/v1/location/optional#geometry
    internalNicknames?: AppleBusinessConnectLocationInternalNickname[]; // only one entry, even if it is an array
    locationAttributes?: AppleBusinessConnectLocationAttribute[];
    locationDescriptions?: AppleBusinessConnectLocationDescriptions[];
    locationKeywords?: AppleBusinessConnectLocationKeywords;
    paymentMethods?: AppleBusinessConnectLocationPaymentMethods[];
    serviceHours?: AppleBusinessConnectLocationServiceHours[];
    specialHours?: AppleBusinessConnectLocationSpecialHours[];
    storeCode?: string;
}

export type AppleBusinessConnectLocationPoint = {
    displayPoint?: AppleBusinessConnectLocationDisplayPoint;
    mainAddress?: AppleBusinessConnectLocationMainAddress;
};

export type AppleBusinessConnectLocationDetails = AppleBusinessConnectLocationBaseDetails & AppleBusinessConnectLocationPoint;

export interface AppleBusinessConnectLocationDisplayName {
    name: string;
    locale: string; // eg: "en-US"
    primary: boolean;
    pronunciation?: string;
}

interface AppleBusinessConnectLocationDisplayPoint {
    coordinates: AppleBusinessConnectLocationPointCoordinates;
    source: AppleBusinessConnectLocationPointSource;
}

interface AppleBusinessConnectLocationPointCoordinates {
    latitude: string; // must be RFC 7946-compliant values in the reference system WGS-84
    longitude: string; // must be RFC 7946-compliant values in the reference system WGS-84
}

export enum AppleBusinessConnectLocationPointSource {
    CALCULATED = 'CALCULATED',
    MANUALLY_PLACED = 'MANUALLY_PLACED',
}

interface AppleBusinessConnectLocationMainAddress {
    fullAddress?: string; // required IF structuredAddress is not present
    structuredAddress?: AppleBusinessConnectLocationStructuredAddress; // required IF fullAddress is not present
    locale: string;
}

interface AppleBusinessConnectLocationStructuredAddress {
    locality: string;
    countryCode: string; // must be uppercase, ISO 3166 Country Codes: https://www.iso.org/iso-3166-country-codes.html https://www.iso.org/obp/ui/#search
    administrativeArea?: string; // required when defined by ISO for the Country
    postCode?: string; // recommended
    unit?: string; // eg: 'Appartement 2A'
    floor?: string;
    building?: string;
    thoroughfare?: string; // street name
    subThoroughfare?: string; // house number
    fullThoroughfare?: string; // house number + street name
    dependentLocality?: string[];
    subLocality?: string;
    subAdministrativeArea?: string;
}

export interface AppleBusinessConnectLocationStatusWithDates {
    status: AppleBusinessConnectLocationStatus;
    closedDate?: string; // required IF status is CLOSED or TEMPORARILY_CLOSED, should be formatted as yyyy-MM-dd
    reopenDate?: string; // required IF status is TEMPORARILY_CLOSED, should be formatted as yyyy-MM-dd, must be a future date
}

export enum AppleBusinessConnectLocationStatus {
    OPEN = 'OPEN',
    CLOSED = 'CLOSED',
    TEMPORARILY_CLOSED = 'TEMPORARILY_CLOSED',
}

export interface AppleBusinessConnectLocationHoursByDay {
    day: AppleBusinessConnectLocationDay;
    times: AppleBusinessConnectLocationTimes[];
}

export enum AppleBusinessConnectLocationDay {
    MONDAY = 'MONDAY',
    TUESDAY = 'TUESDAY',
    WEDNESDAY = 'WEDNESDAY',
    THURSDAY = 'THURSDAY',
    FRIDAY = 'FRIDAY',
    SATURDAY = 'SATURDAY',
    SUNDAY = 'SUNDAY',
}

interface AppleBusinessConnectLocationTimes {
    startTime: string; // format HH:mm
    endTime: string; // format HH:mm
}

export interface AppleBusinessConnectLocationPhoneNumber {
    phoneNumber: string; // eg: +*********** -> E.164-compliant value: https://en.wikipedia.org/wiki/E.164
    phoneExt?: string;
    type: AppleBusinessConnectLocationPhoneNumberType;
    primary: boolean;
    descriptions?: string[];
}

export enum AppleBusinessConnectLocationPhoneNumberType {
    FAX = 'FAX',
    LANDLINE = 'LANDLINE',
    MOBILE = 'MOBILE',
    TOLL_FREE = 'TOLL_FREE',
    TTY = 'TTY',
    VOICE = 'VOICE',
}

export interface AppleBusinessConnectLocationUrl {
    url: string; // when type is IOS_APP, must be the location of the provider's application in the App Store
    type: AppleBusinessConnectLocationUrlType;
}

export enum AppleBusinessConnectLocationUrlType {
    FACEBOOK = 'FACEBOOK',
    HOMEPAGE = 'HOMEPAGE',
    INSTAGRAM = 'INSTAGRAM',
    IOS_APP = 'IOS_APP',
    TWITTER = 'TWITTER',
    YELP = 'YELP',
}

interface AppleBusinessConnectLocationAccessPoint {
    coordinates: AppleBusinessConnectLocationPointCoordinates;
    navigation: AppleBusinessConnectLocationNavigation;
    source: AppleBusinessConnectLocationPointSource;
}

enum AppleBusinessConnectLocationNavigation {
    DRIVING = 'DRIVING',
    WALKING = 'WALKING',
}

interface AppleBusinessConnectLocationActionLinkDetails {
    quicklinks: AppleBusinessConnectLocationQuickLink[];
}

interface AppleBusinessConnectLocationQuickLink {
    category: string; // possible values: https://businessconnect.apple.com/docs/data-specification/resources/quicklink-categories.csv
    quicklinkUrl: string;
    appStoreUrl: string;
    relationship: AppleBusinessConnectLocationRelationship; // the relationship between the provider of a quicklink and the location
}

export enum AppleBusinessConnectLocationRelationship {
    AUTHORIZED = 'AUTHORIZED',
    OTHER = 'OTHER',
    OWNER = 'OWNER',
}

export interface AppleBusinessConnectLocationInternalNickname {
    name: string;
    locale: string;
}

export interface AppleBusinessConnectLocationAttribute {
    name: string; // possible values: https://businessconnect.apple.com/docs/data-specification/resources/location-attributes-categories.csv
    value: string;
}

export interface AppleBusinessConnectLocationDescriptions {
    type: 'ABOUT';
    descriptions: AppleBusinessConnectLocationTextLocaleValue[];
}

export interface AppleBusinessConnectLocationTextLocaleValue {
    text: string;
    locale: string;
}

export interface AppleBusinessConnectLocationKeywords {
    products?: AppleBusinessConnectLocationKeyword[];
    services?: AppleBusinessConnectLocationKeyword[];
    other?: AppleBusinessConnectLocationKeyword[];
}

export interface AppleBusinessConnectLocationKeyword {
    keyword: string;
    locale: string;
}

export enum AppleBusinessConnectLocationPaymentMethods {
    AMERICAN_EXPRESS = 'AMERICAN_EXPRESS',
    BITCOIN = 'BITCOIN',
    CASH_PAYMENT = 'CASH_PAYMENT',
    CHECK = 'CHECK',
    UNION_PAY = 'UNION_PAY',
    DEBIT_CARDS = 'DEBIT_CARDS',
    DINERS_CLUB = 'DINERS_CLUB',
    DISCOVER = 'DISCOVER',
    FINANCING = 'FINANCING',
    INVOICE = 'INVOICE',
    JCB = 'JCB',
    MAESTRO = 'MAESTRO',
    MASTERCARD = 'MASTERCARD',
    MASTERCARD_DEBIT = 'MASTERCARD_DEBIT',
    PAYPAL = 'PAYPAL',
    RUPAY = 'RUPAY',
    STORE_CARD = 'STORE_CARD',
    VISA = 'VISA',
    VISA_DEBIT = 'VISA_DEBIT',
}

export interface AppleBusinessConnectLocationServiceHours {
    category: string; // possible values: https://businessconnect.apple.com/docs/data-specification/resources/service-hours-categories.csv
    hoursByDay: AppleBusinessConnectLocationHoursByDay[];
}

export type AppleBusinessConnectLocationSpecialHours = {
    startDate: string; // should be formatted as yyyy-MM-dd
    endDate: string; // should be formatted as yyyy-MM-dd
    descriptions: AppleBusinessConnectLocationTextLocaleValue[];
} & ({ closed: true } | { closed: false; hoursByDay: AppleBusinessConnectLocationHoursByDay[] });

export enum AppleBusinessConnectLocationState {
    DELETED = 'DELETED',
    FAILED = 'FAILED',
    PROCESSING = 'PROCESSING',
    PUBLISHED = 'PUBLISHED',
    REJECTED = 'REJECTED',
    SUBMITTED = 'SUBMITTED',
}

export interface AppleBusinessConnectValidationReportPayload {
    code: string;
    severity: AppleBusinessConnectValidationReportSeverity;
    message: string;
    context: AppleBusinessConnectValidationReportContext;
    details: AppleBusinessConnectValidationReportDetails;
}

export interface AppleBusinessConnectValidationReportContext {
    key: string; //	A name-value map of a variable and its value necessary to construct the message.
    field: string; // Property provided by Business Connect that may be used to construct the message
    minValue: string; // A minimum value provided by Business Connect that may be used to construct the message
    maxValue: string; // A maximum value provided by Business Connect that may be used to construct the message
    value: string; // Value(s) provided by the API caller which may be used to construct the message
    validValue: string; //	Value(s) provided by Business Connect that may be used to construct the message
}

export interface AppleBusinessConnectValidationReportDetails {
    createdAt: string;
    submitted: AppleBusinessConnectValidationReportFieldsValue[];
}

interface AppleBusinessConnectValidationReportFieldsValue {
    fields: string;
    value: unknown;
}

export enum AppleBusinessConnectValidationReportSeverity {
    INFO = 'INFO',
    WARNING = 'WARNING',
    VIOLATION = 'VIOLATION',
}

// Custom interfaces
export enum AppleBusinessConnectMalouOperationType {
    CREATE = 'CREATE',
    UPDATE = 'UPDATE',
    DELETE = 'DELETE',
}

export interface AppleBusinessConnectResourceDetails {
    resourceType: AppleBusinessConnectResourceType;
    resourceId: string;
    etag: string;
    state: AppleBusinessConnectLocationState;
    delegationDetails?: AppleBusinessConnectDelegationDetails;
}

/*********************************
 * Sub-interfaces and enums
 ********************************/

interface AppleBusinessConnectDelegationDetails {
    companyId: string;
    companyLegalName: string;
    state: DelegationState;
}

export enum AppleBusinessConnectResourceType {
    BUSINESS = 'BUSINESS',
    BUSINESS_ASSET = 'BUSINESS_ASSET',
    LOCATION = 'LOCATION',
    LOCATION_ASSET = 'LOCATION_ASSET',
    SHOWCASE = 'SHOWCASE',
    SHOWCASE_CREATIVE = 'SHOWCASE_CREATIVE',
    AGGREGATE_RATING = 'AGGREGATE_RATING',
    IMAGE = 'IMAGE',
    REVIEW = 'REVIEW',
}

enum DelegationState {
    ACTIVE = 'ACTIVE',
    NOT_ACTIVE = 'NOT_ACTIVE',
    PENDING = 'PENDING',
}

export enum AppleBusinessConnectMediaState {
    APPROVED = 'APPROVED',
    DELETED = 'DELETED',
    IN_REVIEW = 'IN_REVIEW',
    REJECTED = 'REJECTED',
    SUBMITTED = 'SUBMITTED',
}
