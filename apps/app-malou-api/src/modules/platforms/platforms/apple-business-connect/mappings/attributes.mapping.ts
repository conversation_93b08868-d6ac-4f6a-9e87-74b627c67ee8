import { MalouAttributesEnum } from ':modules/attributes/mappings/attributes.malou';

export const appleBusinessConnectAttributesMapping = {
    [MalouAttributesEnum.HAS_WHEELCHAIR_ACCESSIBLE_ENTRANCE]: 'crossbusiness.accessibility_features.wheelchair_accessible',
    [MalouAttributesEnum.WELCOMES_FAMILIES]: 'crossbusiness.goodfor.families',
    [MalouAttributesEnum.HAS_DANCING]: 'crossbusiness.nightlife.music.dancing',
    [MalouAttributesEnum.HAS_ALL_YOU_CAN_EAT_ALWAYS]: 'dining.mealoptions.all_you_can_eat',
    [MalouAttributesEnum.SERVES_HAPPY_HOUR_FOOD]: 'crossbusiness.barservice.happyhour',
    [MalouAttributesEnum.SERVES_HALAL_FOOD]: 'dining.specialdiets.halal',
    [MalouAttributesEnum.REQUIRES_CASH_ONLY]: 'crossbusiness.payments.cash.cash_only',
    [MalouAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED]: 'crossbusiness.payments.creditcard',
    [MalouAttributesEnum.ACCEPTS_RESERVATIONS]: 'crossbusiness.reservations.accepts_reservations',
    [MalouAttributesEnum.HAS_AREA_PLAY]: 'crossbusiness.family_kids.kids_playarea',
    [MalouAttributesEnum.HAS_FIREPLACE]: 'hotel.property_amenity.onsite_firepit',
    [MalouAttributesEnum.HAS_LIVE_MUSIC]: 'crossbusiness.nightlife.livemusic',
    [MalouAttributesEnum.HAS_PRIVATE_DINING_ROOM]: 'crossbusiness.event_service.private_parties',
    [MalouAttributesEnum.HAS_KARAOKE_NIGHTS]: 'crossbusiness.event_service.karaoke',
    [MalouAttributesEnum.HAS_LIVE_PERFORMANCES]: 'crossbusiness.nightlife.livemusic',
    [MalouAttributesEnum.SERVES_BRUNCH]: 'dining.mealoptions.brunch',
    [MalouAttributesEnum.SERVES_LUNCH]: 'dining.mealoptions.lunch',
    [MalouAttributesEnum.SERVES_DESSERT]: 'dining.mealoptions.dessert',
    [MalouAttributesEnum.SERVES_DINNER]: 'dining.mealoptions.dinner',
    [MalouAttributesEnum.HAS_DELIVERY]: 'crossbusiness.services.delivery',
    [MalouAttributesEnum.SERVES_BREAKFAST]: 'dining.mealoptions.breakfast',
    [MalouAttributesEnum.HAS_TAKEOUT]: 'crossbusiness.services.take_out',
    [MalouAttributesEnum.HAS_HIGH_CHAIRS]: 'crossbusiness.family_kids.highchair',
    [MalouAttributesEnum.HAS_AIR_CONDITIONING]: 'crossbusiness.techfeatures.airconditioning',
    [MalouAttributesEnum.IS_SMOKE_FREE_PROPERTY]: 'hotel.room.non_smokingroom',
    [MalouAttributesEnum.HAS_NO_CONTACT_DELIVERY]: 'crossbusiness.services.delivery.no_contact_delivery',
    [MalouAttributesEnum.SERVES_DINE_IN]: 'crossbusiness.restaurant_onsite',
    [MalouAttributesEnum.HAS_DRIVE_THROUGH]: 'crossbusiness.services.drive_through',
    [MalouAttributesEnum.IS_SANITIZING_BETWEEN_CUSTOMERS]: 'crossbusiness.safety_features.sanitized_rooms',
    [MalouAttributesEnum.SERVES_VEGETARIAN]: 'dining.specialdiets.vegetarian',
    [MalouAttributesEnum.SERVES_LATE_NIGHT_FOOD]: 'crossbusiness.nightlife.latenightmeals',
    [MalouAttributesEnum.WELCOMES_CHILDREN]: 'crossbusiness.family_kids.kidsamenities',
    [MalouAttributesEnum.PAY_DEBIT_CARD]: 'crossbusiness.payments.debitcard',
    [MalouAttributesEnum.PAY_CREDIT_CARD]: 'crossbusiness.payments.creditcard',
    [MalouAttributesEnum.ACCEPTS_MEAL_COUPONS]: 'crossbusiness.accessibility_features',
    [MalouAttributesEnum.SERVES_BEER]: 'crossbusiness.barservice.beer_wine',
    [MalouAttributesEnum.SERVES_WINE]: 'crossbusiness.barservice.beer_wine',
    [MalouAttributesEnum.WELCOMES_DOGS]: 'crossbusiness.pets.pets_welcome',
    [MalouAttributesEnum.WELCOMES_PETS]: 'crossbusiness.pets.pets_welcome',
    [MalouAttributesEnum.ALLOWS_DOGS_INSIDE]: 'crossbusiness.pets.pets_welcome',
    [MalouAttributesEnum.HAS_CURBSIDE_PICKUP]: 'crossbusiness.services.curbside_pickup',
    [MalouAttributesEnum.SERVES_HAPPY_HOUR_DRINKS]: 'crossbusiness.barservice.happyhour',
    [MalouAttributesEnum.HAS_ALL_YOU_CAN_DRINK]: 'dining.mealoptions.all_you_can_drink',
    [MalouAttributesEnum.HAS_BAR_ONSITE]: 'crossbusiness.barservice',
    [MalouAttributesEnum.HAS_CATERING]: 'dining.catering_service',
    [MalouAttributesEnum.HAS_CHILDRENS_MENU]: 'dining.mealoptions.kidsmenu',
    [MalouAttributesEnum.HAS_COUNTER_SERVICE]: 'crossbusiness.services.counter',
    [MalouAttributesEnum.HAS_RESTAURANT]: 'crossbusiness.restaurant_onsite',
    [MalouAttributesEnum.HAS_RESTROOM]: 'crossbusiness.restrooms',
    [MalouAttributesEnum.HAS_RESTROOM_PUBLIC]: 'crossbusiness.restrooms',
    [MalouAttributesEnum.HAS_RESTROOM_UNISEX]: 'crossbusiness.restrooms.gender_neutral_restroom',
    [MalouAttributesEnum.HAS_SEATING]: 'crossbusiness.seating.indoor_seating',
    [MalouAttributesEnum.HAS_WHEELCHAIR_ACCESSIBLE_ELEVATOR]: 'crossbusiness.accessibility_features.wheelchair_accessible',
    [MalouAttributesEnum.HAS_WHEELCHAIR_ACCESSIBLE_PARKING]: 'crossbusiness.accessibility_features.wheelchair_accessible',
    [MalouAttributesEnum.HAS_WHEELCHAIR_ACCESSIBLE_RESTROOM]: 'crossbusiness.accessibility_features.wheelchair_accessible',
    [MalouAttributesEnum.HAS_WHEELCHAIR_ACCESSIBLE_SEATING]: 'crossbusiness.accessibility_features.wheelchair_accessible',
    [MalouAttributesEnum.HAS_SEATING_OUTDOORS]: 'dining.outdoordining',
    [MalouAttributesEnum.PAID_WI_FI]: 'crossbusiness.techfeatures.wifi',
    [MalouAttributesEnum.PAY_MOBILE_NFC]: 'crossbusiness.payments.contactless_pay',
    [MalouAttributesEnum.REQUIRES_RESERVATIONS]: 'crossbusiness.reservations.reservations_only',
    [MalouAttributesEnum.SERVES_COFFEE]: 'dining.onsite_coffeeshop',
    [MalouAttributesEnum.FREE_WI_FI]: 'crossbusiness.techfeatures.wifi.freewifi',
};
