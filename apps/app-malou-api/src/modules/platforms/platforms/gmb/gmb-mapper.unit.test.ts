import { container } from 'tsyringe';

import { IHoursType, IRestaurantAttributeWithAttribute } from '@malou-io/package-models';
import { PlatformKey, RestaurantAttributeValue, SocialNetworkKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { GmbAttributesEnum } from ':modules/attributes/mappings/attributes.gmb';
import { MalouAttributesEnum } from ':modules/attributes/mappings/attributes.malou';
import { getDefaultHoursTypes } from ':modules/hour-types/tests/hours-types.builder';
import { GmbMapper } from ':modules/platforms/platforms/gmb/gmb-mapper';
import { AttributeValueType, GmbSpecialHours, GmbValue } from ':modules/platforms/platforms/gmb/gmb.types';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SlackService } from ':services/slack.service';

describe('GmbMapper', () => {
    beforeEach(() => {
        const slackServiceMock = {
            sendMessage: jest.fn(),
            createContextForSlack: jest.fn(),
            sendAlert: jest.fn(),
        } as unknown as SlackService;
        container.register(SlackService, { useValue: slackServiceMock });
    });

    it('should have some properties', () => {
        const mapper = container.resolve(GmbMapper);
        expect(mapper).toHaveProperty('supportedFields');
        expect(mapper.supportedFields).toContain('name');
        expect(mapper.supportedFields).toContain('address');
        expect(mapper.supportedFields).toContain('regularHours');
        expect(mapper.supportedFields).toContain('specialHours');
        expect(mapper.supportedFields).toContain('descriptions');
        expect(mapper.supportedFields).toContain('phone');
        expect(mapper.supportedFields).toContain('website');
        expect(mapper.supportedFields).toContain('openingDate');
        expect(mapper.supportedFields).toContain('placeId');
        expect(mapper.supportedFields).toContain('menu');
        expect(mapper.supportedFields).toContain('category');
        expect(mapper.supportedFields).toContain('categoryList');
        expect(mapper.supportedFields).toContain('rating');
    });

    it('should map latitude longitude', async () => {
        const dataFromGmb = {
            name: 'locations/4672440100539773573',
            languageCode: 'fr',
            title: 'Bon Gueletonnn',
            phoneNumbers: {
                primaryPhone: '06 62 37 12 39',
            },
            categories: {
                primaryCategory: {
                    name: 'categories/gcid:bar',
                    displayName: 'Bar',
                    moreHoursTypes: [
                        {
                            hoursTypeId: 'ACCESS',
                            displayName: 'Access',
                            localizedDisplayName: 'Accès',
                        },
                        {
                            hoursTypeId: 'BREAKFAST',
                            displayName: 'Breakfast',
                            localizedDisplayName: 'Petit-déjeuner',
                        },
                        {
                            hoursTypeId: 'BRUNCH',
                            displayName: 'Brunch',
                            localizedDisplayName: 'Brunch',
                        },
                        {
                            hoursTypeId: 'DELIVERY',
                            displayName: 'Delivery',
                            localizedDisplayName: 'Livraison',
                        },
                        {
                            hoursTypeId: 'DINNER',
                            displayName: 'Dinner',
                            localizedDisplayName: 'Dîner',
                        },
                        {
                            hoursTypeId: 'DRIVE_THROUGH',
                            displayName: 'Drive through',
                            localizedDisplayName: 'Service de drive',
                        },
                        {
                            hoursTypeId: 'HAPPY_HOUR',
                            displayName: 'Happy hours',
                            localizedDisplayName: 'Happy hours',
                        },
                        {
                            hoursTypeId: 'KITCHEN',
                            displayName: 'Kitchen',
                            localizedDisplayName: 'Cuisine',
                        },
                        {
                            hoursTypeId: 'LUNCH',
                            displayName: 'Lunch',
                            localizedDisplayName: 'Déjeuner',
                        },
                        {
                            hoursTypeId: 'ONLINE_SERVICE_HOURS',
                            displayName: 'Online service hours',
                            localizedDisplayName: 'Service en ligne',
                        },
                        {
                            hoursTypeId: 'TAKEOUT',
                            displayName: 'Takeout',
                            localizedDisplayName: 'Vente à emporter',
                        },
                        {
                            hoursTypeId: 'SENIOR_HOURS',
                            displayName: 'Senior hours',
                            localizedDisplayName: 'Horaires pour les seniors',
                        },
                    ],
                },
                additionalCategories: [
                    {
                        name: 'categories/gcid:tapas_bar',
                        displayName: 'Bar à tapas',
                        moreHoursTypes: [
                            {
                                hoursTypeId: 'ACCESS',
                                displayName: 'Access',
                                localizedDisplayName: 'Accès',
                            },
                            {
                                hoursTypeId: 'BREAKFAST',
                                displayName: 'Breakfast',
                                localizedDisplayName: 'Petit-déjeuner',
                            },
                            {
                                hoursTypeId: 'BRUNCH',
                                displayName: 'Brunch',
                                localizedDisplayName: 'Brunch',
                            },
                            {
                                hoursTypeId: 'DELIVERY',
                                displayName: 'Delivery',
                                localizedDisplayName: 'Livraison',
                            },
                            {
                                hoursTypeId: 'DINNER',
                                displayName: 'Dinner',
                                localizedDisplayName: 'Dîner',
                            },
                            {
                                hoursTypeId: 'DRIVE_THROUGH',
                                displayName: 'Drive through',
                                localizedDisplayName: 'Service de drive',
                            },
                            {
                                hoursTypeId: 'HAPPY_HOUR',
                                displayName: 'Happy hours',
                                localizedDisplayName: 'Happy hours',
                            },
                            {
                                hoursTypeId: 'KITCHEN',
                                displayName: 'Kitchen',
                                localizedDisplayName: 'Cuisine',
                            },
                            {
                                hoursTypeId: 'LUNCH',
                                displayName: 'Lunch',
                                localizedDisplayName: 'Déjeuner',
                            },
                            {
                                hoursTypeId: 'ONLINE_SERVICE_HOURS',
                                displayName: 'Online service hours',
                                localizedDisplayName: 'Service en ligne',
                            },
                            {
                                hoursTypeId: 'TAKEOUT',
                                displayName: 'Takeout',
                                localizedDisplayName: 'Vente à emporter',
                            },
                            {
                                hoursTypeId: 'SENIOR_HOURS',
                                displayName: 'Senior hours',
                                localizedDisplayName: 'Horaires pour les seniors',
                            },
                        ],
                    },
                    {
                        name: 'categories/gcid:bakso_restaurant',
                        displayName: 'Restaurant spécialisé dans le bakso',
                        moreHoursTypes: [
                            {
                                hoursTypeId: 'ACCESS',
                                displayName: 'Access',
                                localizedDisplayName: 'Accès',
                            },
                            {
                                hoursTypeId: 'BREAKFAST',
                                displayName: 'Breakfast',
                                localizedDisplayName: 'Petit-déjeuner',
                            },
                            {
                                hoursTypeId: 'BRUNCH',
                                displayName: 'Brunch',
                                localizedDisplayName: 'Brunch',
                            },
                            {
                                hoursTypeId: 'DELIVERY',
                                displayName: 'Delivery',
                                localizedDisplayName: 'Livraison',
                            },
                            {
                                hoursTypeId: 'DINNER',
                                displayName: 'Dinner',
                                localizedDisplayName: 'Dîner',
                            },
                            {
                                hoursTypeId: 'DRIVE_THROUGH',
                                displayName: 'Drive through',
                                localizedDisplayName: 'Service de drive',
                            },
                            {
                                hoursTypeId: 'HAPPY_HOUR',
                                displayName: 'Happy hours',
                                localizedDisplayName: 'Happy hours',
                            },
                            {
                                hoursTypeId: 'KITCHEN',
                                displayName: 'Kitchen',
                                localizedDisplayName: 'Cuisine',
                            },
                            {
                                hoursTypeId: 'LUNCH',
                                displayName: 'Lunch',
                                localizedDisplayName: 'Déjeuner',
                            },
                            {
                                hoursTypeId: 'ONLINE_SERVICE_HOURS',
                                displayName: 'Online service hours',
                                localizedDisplayName: 'Service en ligne',
                            },
                            {
                                hoursTypeId: 'TAKEOUT',
                                displayName: 'Takeout',
                                localizedDisplayName: 'Vente à emporter',
                            },
                            {
                                hoursTypeId: 'SENIOR_HOURS',
                                displayName: 'Senior hours',
                                localizedDisplayName: 'Horaires pour les seniors',
                            },
                        ],
                    },
                    {
                        name: 'categories/gcid:african_restaurant',
                        displayName: 'Restaurant africain',
                        moreHoursTypes: [
                            {
                                hoursTypeId: 'ACCESS',
                                displayName: 'Access',
                                localizedDisplayName: 'Accès',
                            },
                            {
                                hoursTypeId: 'BREAKFAST',
                                displayName: 'Breakfast',
                                localizedDisplayName: 'Petit-déjeuner',
                            },
                            {
                                hoursTypeId: 'BRUNCH',
                                displayName: 'Brunch',
                                localizedDisplayName: 'Brunch',
                            },
                            {
                                hoursTypeId: 'DELIVERY',
                                displayName: 'Delivery',
                                localizedDisplayName: 'Livraison',
                            },
                            {
                                hoursTypeId: 'DINNER',
                                displayName: 'Dinner',
                                localizedDisplayName: 'Dîner',
                            },
                            {
                                hoursTypeId: 'DRIVE_THROUGH',
                                displayName: 'Drive through',
                                localizedDisplayName: 'Service de drive',
                            },
                            {
                                hoursTypeId: 'HAPPY_HOUR',
                                displayName: 'Happy hours',
                                localizedDisplayName: 'Happy hours',
                            },
                            {
                                hoursTypeId: 'KITCHEN',
                                displayName: 'Kitchen',
                                localizedDisplayName: 'Cuisine',
                            },
                            {
                                hoursTypeId: 'LUNCH',
                                displayName: 'Lunch',
                                localizedDisplayName: 'Déjeuner',
                            },
                            {
                                hoursTypeId: 'ONLINE_SERVICE_HOURS',
                                displayName: 'Online service hours',
                                localizedDisplayName: 'Service en ligne',
                            },
                            {
                                hoursTypeId: 'TAKEOUT',
                                displayName: 'Takeout',
                                localizedDisplayName: 'Vente à emporter',
                            },
                            {
                                hoursTypeId: 'SENIOR_HOURS',
                                displayName: 'Senior hours',
                                localizedDisplayName: 'Horaires pour les seniors',
                            },
                        ],
                    },
                    {
                        name: 'categories/gcid:armenian_restaurant',
                        displayName: 'Restaurant arménien',
                        moreHoursTypes: [
                            {
                                hoursTypeId: 'ACCESS',
                                displayName: 'Access',
                                localizedDisplayName: 'Accès',
                            },
                            {
                                hoursTypeId: 'BREAKFAST',
                                displayName: 'Breakfast',
                                localizedDisplayName: 'Petit-déjeuner',
                            },
                            {
                                hoursTypeId: 'BRUNCH',
                                displayName: 'Brunch',
                                localizedDisplayName: 'Brunch',
                            },
                            {
                                hoursTypeId: 'DELIVERY',
                                displayName: 'Delivery',
                                localizedDisplayName: 'Livraison',
                            },
                            {
                                hoursTypeId: 'DINNER',
                                displayName: 'Dinner',
                                localizedDisplayName: 'Dîner',
                            },
                            {
                                hoursTypeId: 'DRIVE_THROUGH',
                                displayName: 'Drive through',
                                localizedDisplayName: 'Service de drive',
                            },
                            {
                                hoursTypeId: 'HAPPY_HOUR',
                                displayName: 'Happy hours',
                                localizedDisplayName: 'Happy hours',
                            },
                            {
                                hoursTypeId: 'KITCHEN',
                                displayName: 'Kitchen',
                                localizedDisplayName: 'Cuisine',
                            },
                            {
                                hoursTypeId: 'LUNCH',
                                displayName: 'Lunch',
                                localizedDisplayName: 'Déjeuner',
                            },
                            {
                                hoursTypeId: 'ONLINE_SERVICE_HOURS',
                                displayName: 'Online service hours',
                                localizedDisplayName: 'Service en ligne',
                            },
                            {
                                hoursTypeId: 'TAKEOUT',
                                displayName: 'Takeout',
                                localizedDisplayName: 'Vente à emporter',
                            },
                            {
                                hoursTypeId: 'SENIOR_HOURS',
                                displayName: 'Senior hours',
                                localizedDisplayName: 'Horaires pour les seniors',
                            },
                        ],
                    },
                ],
            },
            storefrontAddress: {
                regionCode: 'FR',
                languageCode: 'fr',
                postalCode: '75020',
                locality: 'Paris',
                addressLines: ['17 rue de la plaine'],
            },
            websiteUri: 'https://app.kikoo.io/',
            regularHours: {
                periods: [
                    {
                        openDay: 'MONDAY',
                        openTime: {
                            hours: 10,
                        },
                        closeDay: 'MONDAY',
                        closeTime: {
                            hours: 22,
                            minutes: 30,
                        },
                    },
                    {
                        openDay: 'TUESDAY',
                        openTime: {
                            hours: 10,
                        },
                        closeDay: 'TUESDAY',
                        closeTime: {
                            hours: 22,
                            minutes: 30,
                        },
                    },
                    {
                        openDay: 'WEDNESDAY',
                        openTime: {
                            hours: 10,
                        },
                        closeDay: 'WEDNESDAY',
                        closeTime: {
                            hours: 22,
                            minutes: 30,
                        },
                    },
                ],
            },
            openInfo: {
                status: 'OPEN',
                canReopen: true,
            },
            metadata: {
                canDelete: true,
                canHaveFoodMenus: true,
                placeId: 'ChIJu8GhHLLXVA0ROyQYiWsdv5Q',
                mapsUri: 'https://maps.google.com/maps?cid=10718317985863836731',
                newReviewUri: 'https://search.google.com/local/writereview?placeid=ChIJu8GhHLLXVA0ROyQYiWsdv5Q',
            },
            profile: {
                description: "Bon gueleton, c'est une cuisine familiale et maison !",
            },
            type: 'local_business',
            placeId: 'ChIJu8GhHLLXVA0ROyQYiWsdv5Q',
            organizationId: '6710d3c31fdf8e5ec612b408',
        };

        const latitude = 48.864716;
        const longitude = 2.349014;

        const mapper = container.resolve(GmbMapper);
        const result = await mapper.toMalouMapper({
            ...dataFromGmb,
            latlng: {
                latitude,
                longitude,
            },
        });

        expect(result).toMatchObject({
            latlng: {
                lat: latitude,
                lng: longitude,
            },
        });
    });

    describe('toGmbDate', () => {
        it('should get day, month and year from a date', () => {
            const date = new Date();

            const mapper = container.resolve(GmbMapper);
            const result = mapper.toGmbDate(date);

            const expectedResult = { day: date.getDate(), month: date.getMonth(), year: date.getFullYear() };
            expect(result).toEqual(expectedResult);
        });
    });

    describe('addClosedDays', () => {
        it('should add missing days as closed days', () => {
            const hours = [
                {
                    openDay: 'MONDAY',
                    closeDay: 'MONDAY',
                },
                {
                    openDay: 'TUESDAY',
                    closeDay: 'TUESDAY',
                },
                {
                    openDay: 'WEDNESDAY',
                    closeDay: 'WEDNESDAY',
                },
                {
                    openDay: 'THURSDAY',
                    closeDay: 'THURSDAY',
                },
                {
                    openDay: 'FRIDAY',
                    closeDay: 'FRIDAY',
                },
            ];
            const expectedHours = [
                ...hours,
                {
                    openDay: 'SATURDAY',
                    closeDay: 'SATURDAY',
                    isClosed: true,
                },
                {
                    openDay: 'SUNDAY',
                    closeDay: 'SUNDAY',
                    isClosed: true,
                },
            ];

            const mapper = container.resolve(GmbMapper);
            const result = mapper.addClosedDays(hours);

            expect(result).toEqual(expectedHours);
        });
    });

    describe('mapSpecialHoursToTarget', () => {
        it('should map Malou hours to Gmb hours', () => {
            const hours = [
                {
                    startDate: { day: 18, month: 2, year: 2020 },
                    openTime: '09:00',
                    endDate: { day: 18, month: 2, year: 2020 },
                    closeTime: '14:30',
                    isClosed: false,
                },
                {
                    startDate: { day: 19, month: 2, year: 2020 },
                    openTime: '23:00',
                    endDate: { day: 20, month: 2, year: 2020 },
                    closeTime: '10:00',
                    isClosed: false,
                },
                {
                    startDate: { day: 14, month: 8, year: 2020 },
                    openTime: null,
                    endDate: { day: 14, month: 8, year: 2020 },
                    closeTime: null,
                    isClosed: true,
                },
            ];
            const expectedHours = [
                {
                    startDate: { day: 18, month: 3, year: 2020 },
                    openTime: {
                        hours: 9,
                        minutes: 0,
                    },
                    endDate: { day: 18, month: 3, year: 2020 },
                    closeTime: {
                        hours: 14,
                        minutes: 30,
                    },
                    closed: false,
                },
                {
                    startDate: { day: 19, month: 3, year: 2020 },
                    openTime: {
                        hours: 23,
                        minutes: 0,
                    },
                    endDate: { day: 20, month: 3, year: 2020 },
                    closeTime: {
                        hours: 10,
                        minutes: 0,
                    },
                    closed: false,
                },
                {
                    startDate: { day: 14, month: 9, year: 2020 },
                    closed: true,
                },
            ];

            const mapper = container.resolve(GmbMapper);
            const result = mapper.mapSpecialHoursToTarget(hours as any);

            expect(result).toEqual(expectedHours);
        });
    });

    describe('mapSpecialHoursToOrigin', () => {
        it('should map Gmb hours to Malou hours', () => {
            const specialHours: GmbSpecialHours = {
                specialHourPeriods: [
                    {
                        startDate: { day: 18, month: 3, year: 2020 },
                        openTime: {
                            hours: 9,
                            minutes: 0,
                        },
                        endDate: { day: 18, month: 3, year: 2020 },
                        closeTime: {
                            hours: 14,
                            minutes: 30,
                        },
                        closed: false,
                    },
                    {
                        startDate: { day: 19, month: 2, year: 2020 },
                        openTime: {
                            hours: 23,
                            minutes: 0,
                        },
                        endDate: { day: 20, month: 2, year: 2020 },
                        closeTime: {
                            hours: 10,
                            minutes: 0,
                        },
                        closed: false,
                    },
                    {
                        startDate: { day: 19, month: 1, year: 2020 },
                        openTime: {
                            hours: 23,
                            minutes: 0,
                        },
                        endDate: { day: 20, month: 1, year: 2020 },
                        closeTime: {
                            hours: 10,
                            minutes: 0,
                        },
                        closed: false,
                    },
                    {
                        startDate: { day: 14, month: 8, year: 2020 },
                        closed: true,
                    },
                ],
            };
            const expectedHours = [
                {
                    startDate: { day: 18, month: 2, year: 2020 },
                    openTime: '09:00',
                    endDate: { day: 18, month: 2, year: 2020 },
                    closeTime: '14:30',
                    isClosed: false,
                },
                {
                    startDate: { day: 19, month: 1, year: 2020 },
                    openTime: '23:00',
                    endDate: { day: 20, month: 1, year: 2020 },
                    closeTime: '10:00',
                    isClosed: false,
                },
                {
                    startDate: { day: 19, month: 0, year: 2020 },
                    openTime: '23:00',
                    endDate: { day: 20, month: 0, year: 2020 },
                    closeTime: '10:00',
                    isClosed: false,
                },
                {
                    startDate: { day: 14, month: 7, year: 2020 },
                    closeTime: '00:00',
                    isClosed: true,
                    openTime: '00:00',
                    endDate: { day: 14, month: 7, year: 2020 },
                },
            ];

            const mapper = container.resolve(GmbMapper);
            const result = mapper.mapSpecialHoursToOrigin(specialHours);

            expect(result).toEqual(expectedHours);
        });
    });

    describe('getGmbAttributesList', () => {
        it('should return []', () => {
            const list = [];
            const mapper = container.resolve(GmbMapper);
            const gmbAttributes = mapper.getGmbAttributesList(list);
            expect(gmbAttributes).toEqual([]);
        });

        const list = [
            {
                attribute: {
                    attributeId: MalouAttributesEnum.ACCEPTS_MEAL_COUPONS,
                },
                attributeValue: RestaurantAttributeValue.NO,
            },
            {
                attribute: {
                    attributeId: MalouAttributesEnum.ALLOWS_DOGS_OUTSIDE,
                },
                attributeValue: RestaurantAttributeValue.YES,
            },
        ];
        const expected = list.map((a) => ({
            name: `attributes/${a.attribute.attributeId}`,
            values: [a.attributeValue === RestaurantAttributeValue.YES],
        }));

        it('should return values: false and true for random attr with value no and yes', () => {
            const mapper = container.resolve(GmbMapper);
            const gmbAttributes = mapper.getGmbAttributesList(list as IRestaurantAttributeWithAttribute[]);

            expect(gmbAttributes.sort((a, b) => a.name.localeCompare(b.name))).toEqual(
                expected.sort((a, b) => a.name.localeCompare(b.name))
            );
        });

        it('should have enumValues settled for creditCard', () => {
            const listWithCreditCard = [
                ...list,
                {
                    attribute: {
                        attributeId: MalouAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED,
                    },
                    attributeValue: RestaurantAttributeValue.YES,
                },
            ];

            const mapper = container.resolve(GmbMapper);
            const gmbAttributes = mapper.getGmbAttributesList(listWithCreditCard as IRestaurantAttributeWithAttribute[]);

            const expectedWithCredit = [
                ...expected,
                {
                    name: `attributes/${GmbAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED}`,
                    repeatedEnumValue: {
                        setValues: ['visa', 'mastercard'],
                        unsetValues: [],
                    },
                },
            ];

            expect(gmbAttributes.sort((a, b) => a.name.localeCompare(b.name))).toEqual(
                expectedWithCredit.sort((a, b) => a.name.localeCompare(b.name))
            );
        });

        it('should have enumValues settled for creditCard', () => {
            const listWithCreditCard = [
                ...list,
                {
                    attribute: {
                        attributeId: MalouAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED,
                    },
                    attributeValue: RestaurantAttributeValue.YES,
                },
                {
                    attribute: {
                        attributeId: MalouAttributesEnum.CHINA_UNION_PAY,
                    },
                    attributeValue: RestaurantAttributeValue.YES,
                },
                {
                    attribute: {
                        attributeId: MalouAttributesEnum.JCB,
                    },
                    attributeValue: RestaurantAttributeValue.NO,
                },
                {
                    attribute: {
                        attributeId: MalouAttributesEnum.PAID_WI_FI,
                    },
                    attributeValue: RestaurantAttributeValue.YES,
                },
            ];

            const mapper = container.resolve(GmbMapper);
            const gmbAttributes = mapper.getGmbAttributesList(listWithCreditCard as IRestaurantAttributeWithAttribute[]);

            const expectedWithCredit = [
                ...expected,
                {
                    name: `attributes/${GmbAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED}`,
                    repeatedEnumValue: {
                        setValues: ['visa', 'mastercard', MalouAttributesEnum.CHINA_UNION_PAY],
                        unsetValues: [MalouAttributesEnum.JCB],
                    },
                },
                {
                    name: `attributes/${GmbAttributesEnum.WI_FI}`,
                    values: [MalouAttributesEnum.PAID_WI_FI],
                },
            ];

            expect(gmbAttributes.sort((a, b) => a.name.localeCompare(b.name))).toEqual(
                expectedWithCredit.sort((a, b) => a.name.localeCompare(b.name))
            );
        });
    });

    describe('getMappedAttributeForCreditCards', () => {
        it('should return setValues with visa and mastercard', () => {
            const attribute = {
                attributeValue: RestaurantAttributeValue.YES,
                attribute: {
                    attributeId: GmbAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED,
                },
            };
            const mapper = container.resolve(GmbMapper);
            const mappedCreditCardAttribute = mapper.getMappedAttributeForCreditCards(attribute as IRestaurantAttributeWithAttribute);

            const expected = {
                setValues: ['visa', 'mastercard'],
            };

            expect(mappedCreditCardAttribute).toEqual(expected);
        });

        it('should return unsetValues with visa and mastercard', () => {
            const attribute = {
                attributeValue: RestaurantAttributeValue.NO,
                attribute: {
                    attributeId: GmbAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED,
                },
            };
            const mapper = container.resolve(GmbMapper);
            const mappedCreditCardAttribute = mapper.getMappedAttributeForCreditCards(attribute as IRestaurantAttributeWithAttribute);

            const expected = {
                unsetValues: ['visa', 'mastercard'],
            };

            expect(mappedCreditCardAttribute).toEqual(expected);
        });

        it.each([
            MalouAttributesEnum.AMERICAN_EXPRESS,
            MalouAttributesEnum.CHINA_UNION_PAY,
            MalouAttributesEnum.DINERS_CLUB,
            MalouAttributesEnum.DISCOVER,
            MalouAttributesEnum.JCB,
        ])('should return setValues with specific credit card key if attributeValue is yes', (creditCardKey) => {
            const attribute = {
                attributeValue: RestaurantAttributeValue.YES,
                attribute: {
                    attributeId: creditCardKey,
                },
            };
            const mapper = container.resolve(GmbMapper);
            const mappedCreditCardAttribute = mapper.getMappedAttributeForCreditCards(attribute as IRestaurantAttributeWithAttribute);

            const expected = {
                setValues: [creditCardKey],
            };

            expect(mappedCreditCardAttribute).toEqual(expected);
        });

        it.each([
            MalouAttributesEnum.AMERICAN_EXPRESS,
            MalouAttributesEnum.CHINA_UNION_PAY,
            MalouAttributesEnum.DINERS_CLUB,
            MalouAttributesEnum.DISCOVER,
            MalouAttributesEnum.JCB,
        ])('should return unsetValues with specific credit card key if attributeValue is no', (creditCardKey) => {
            const attribute = {
                attributeValue: RestaurantAttributeValue.NO,
                attribute: {
                    attributeId: creditCardKey,
                },
            };
            const mapper = container.resolve(GmbMapper);
            const mappedCreditCardAttribute = mapper.getMappedAttributeForCreditCards(attribute as IRestaurantAttributeWithAttribute);

            const expected = {
                unsetValues: [creditCardKey],
            };

            expect(mappedCreditCardAttribute).toEqual(expected);
        });
    });

    describe('getGmbAttributeForCreditCards', () => {
        it('should return proper formatted attribute', () => {
            const attributes = [
                {
                    attributeValue: RestaurantAttributeValue.YES,
                    attribute: {
                        attributeId: MalouAttributesEnum.AMERICAN_EXPRESS,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.YES,
                    attribute: {
                        attributeId: MalouAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.YES,
                    attribute: {
                        attributeId: MalouAttributesEnum.DINERS_CLUB,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.NO,
                    attribute: {
                        attributeId: MalouAttributesEnum.JCB,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.NOT_CONCERNED,
                    attribute: {
                        attributeId: MalouAttributesEnum.CHINA_UNION_PAY,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.YES,
                    attribute: {
                        attributeId: MalouAttributesEnum.ACCEPTS_RESERVATIONS,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.YES,
                    attribute: {
                        attributeId: MalouAttributesEnum.ALLOWS_GRILLING,
                    },
                },
            ];
            const mapper = container.resolve(GmbMapper);
            const mappedCreditCardGmbAttribute = mapper.getGmbAttributeForCreditCards(attributes as IRestaurantAttributeWithAttribute[]);

            const expected = {
                name: GmbAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED,
                repeatedEnumValue: {
                    setValues: [
                        MalouAttributesEnum.AMERICAN_EXPRESS,
                        MalouAttributesEnum.VISA,
                        MalouAttributesEnum.MASTERCARD,
                        MalouAttributesEnum.DINERS_CLUB,
                    ],
                    unsetValues: [MalouAttributesEnum.JCB, MalouAttributesEnum.CHINA_UNION_PAY],
                },
            };

            expect(mappedCreditCardGmbAttribute).toEqual(expected);
        });

        it('should return undefined if no values are setup', () => {
            const attributes = [
                {
                    attributeValue: RestaurantAttributeValue.NO,
                    attribute: {
                        attributeId: MalouAttributesEnum.ALLOWS_DOGS_INSIDE,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.YES,
                    attribute: {
                        attributeId: MalouAttributesEnum.SERVES_BEER,
                    },
                },
            ];
            const mapper = container.resolve(GmbMapper);
            const mappedCreditCardGmbAttribute = mapper.getGmbAttributeForCreditCards(attributes as IRestaurantAttributeWithAttribute[]);
            expect(mappedCreditCardGmbAttribute).toBe(undefined);
        });
    });

    describe('getGmbAttributeForWifi', () => {
        it('should return proper formatted attribute', () => {
            const attributes = [
                {
                    attributeValue: RestaurantAttributeValue.YES,
                    attribute: {
                        attributeId: MalouAttributesEnum.FREE_WI_FI,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.NO,
                    attribute: {
                        attributeId: MalouAttributesEnum.PAID_WI_FI,
                    },
                },
            ];
            const mapper = container.resolve(GmbMapper);
            const mappedWifiGmbAttribute = mapper.getGmbAttributeForWifi(attributes as IRestaurantAttributeWithAttribute[]);

            const expected = {
                name: GmbAttributesEnum.WI_FI,
                values: [MalouAttributesEnum.FREE_WI_FI],
            };

            expect(mappedWifiGmbAttribute).toEqual(expected);
        });

        it('should return both values for formatted attribute', () => {
            const attributes = [
                {
                    attributeValue: RestaurantAttributeValue.YES,
                    attribute: {
                        attributeId: MalouAttributesEnum.FREE_WI_FI,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.YES,
                    attribute: {
                        attributeId: MalouAttributesEnum.PAID_WI_FI,
                    },
                },
            ];
            const mapper = container.resolve(GmbMapper);
            const mappedWifiGmbAttribute = mapper.getGmbAttributeForWifi(attributes as IRestaurantAttributeWithAttribute[]);

            const expected = {
                name: GmbAttributesEnum.WI_FI,
                values: [MalouAttributesEnum.FREE_WI_FI, MalouAttributesEnum.PAID_WI_FI],
            };

            expect(mappedWifiGmbAttribute?.name).toEqual(expected.name);
            expect(mappedWifiGmbAttribute?.values?.sort((a: GmbValue, b: GmbValue) => (a as string).localeCompare(b as string))).toEqual(
                expected.values.sort((a: string, b: string) => a.localeCompare(b))
            );
        });

        it('should return undefined if no values are setup', () => {
            const attributes = [
                {
                    attributeValue: RestaurantAttributeValue.NO,
                    attribute: {
                        attributeId: MalouAttributesEnum.ALLOWS_DOGS_INSIDE,
                    },
                },
                {
                    attributeValue: RestaurantAttributeValue.NO,
                    attribute: {
                        attributeId: MalouAttributesEnum.FREE_WI_FI,
                    },
                },
            ];
            const mapper = container.resolve(GmbMapper);
            const mappedWifiGmbAttribute = mapper.getGmbAttributeForWifi(attributes as IRestaurantAttributeWithAttribute[]);
            expect(mappedWifiGmbAttribute).toBe(undefined);
        });
    });

    describe('toPlatformMapper', () => {
        beforeAll(() => {
            registerRepositories(['HourTypesRepository']);
        });

        it('should return empty object when input is null', async () => {
            const malouData = null;

            const mapper = container.resolve(GmbMapper);
            const result = await mapper.toPlatformMapper(malouData);

            expect(result).toEqual({});
        });

        it('should return empty object when input is undefined', async () => {
            const malouData = undefined;

            const mapper = container.resolve(GmbMapper);
            const result = await mapper.toPlatformMapper(malouData);

            expect(result).toEqual({});
        });

        it('should return empty object when input is empty object', async () => {
            const malouData = {};

            const mapper = container.resolve(GmbMapper);
            const result = await mapper.toPlatformMapper(malouData);

            expect(result).toEqual({});
        });

        it('should return gmb special hours', async () => {
            const malouData = {
                specialHours: [
                    {
                        isClosed: true,
                        startDate: {
                            day: 1,
                            month: 1,
                            year: 2020,
                        },
                    },
                ],
            };
            const restaurantId = null;
            const diffFields = ['specialHours'];

            const mapper = container.resolve(GmbMapper);
            const result = await mapper.toPlatformMapper(malouData, restaurantId, diffFields);

            const expectedResult = {
                specialHours: {
                    specialHourPeriods: [
                        {
                            closed: true,
                            startDate: {
                                day: 1,
                                month: 2,
                                year: 2020,
                            },
                        },
                    ],
                },
            };
            expect(result).toEqual(expectedResult);
        });

        it('should return additionalCategories with primaryCategory if only category is passed as diffFields', async () => {
            const malouData = {
                category: {
                    _id: {
                        $oid: '5f0ed4b62ef30c1105ee7625',
                    },
                    platformCategories: [],
                    platformKey: PlatformKey.GMB,
                    categoryName: {
                        backup: 'Restaurant nicaraguayen',
                        fr: 'Restaurant nicaraguayen',
                        en: 'Nicaraguan restaurant',
                        es: null,
                    },
                    categoryId: 'gcid:nicaraguan_restaurant',
                    isFood: true,
                    __v: 0,
                },
            };
            const additionalCategories = [
                {
                    _id: {
                        $oid: '2',
                    },
                    platformCategories: [],
                    platformKey: PlatformKey.GMB,
                    categoryName: {
                        backup: 'Restaurant super',
                        fr: 'Restaurant super',
                        en: 'Super restaurant',
                        es: null,
                    },
                    categoryId: 'gcid:super_restaurant',
                    isFood: true,
                    __v: 0,
                },
            ];
            const restaurantId = '5f0ed4b62ef30c1105ee7625';
            const diffFields = ['category'];
            const RestaurantsRepositoryMock = {
                findOne: jest.fn().mockResolvedValue({
                    categoryList: additionalCategories,
                }),
            };
            container.register(RestaurantsRepository, { useValue: RestaurantsRepositoryMock as any });
            const newMapper = container.resolve(GmbMapper);

            const result = await newMapper.toPlatformMapper(malouData, restaurantId, diffFields);

            const expectedResult = {
                categories: {
                    primaryCategory: {
                        displayName: malouData.category.categoryName.backup,
                        name: `categories/${malouData.category.categoryId}`,
                    },
                    additionalCategories: additionalCategories.map((cat) => ({
                        displayName: cat.categoryName.backup,
                        name: `categories/${cat.categoryId}`,
                    })),
                },
            };
            expect(result).toEqual(expectedResult);
        });

        it('should return additionalCategories with primaryCategory if only categoryList is passed as diffFields', async () => {
            const category = {
                _id: {
                    $oid: '5f0ed4b62ef30c1105ee7625',
                },
                platformCategories: [],
                platformKey: PlatformKey.GMB,
                categoryName: {
                    backup: 'Restaurant nicaraguayen',
                    fr: 'Restaurant nicaraguayen',
                    en: 'Nicaraguan restaurant',
                    es: null,
                },
                categoryId: 'gcid:nicaraguan_restaurant',
                isFood: true,
                __v: 0,
            };
            const additionalCategories = {
                categoryList: [
                    {
                        _id: {
                            $oid: '2',
                        },
                        platformCategories: [],
                        platformKey: PlatformKey.GMB,
                        categoryName: {
                            backup: 'Restaurant super',
                            fr: 'Restaurant super',
                            en: 'Super restaurant',
                            es: null,
                        },
                        categoryId: 'gcid:super_restaurant',
                        isFood: true,
                        __v: 0,
                    },
                ],
            };
            const restaurantId = '5f0ed4b62ef30c1105ee7625';
            const diffFields = ['categoryList'];
            const RestaurantsRepositoryMock = {
                findOne: jest.fn().mockResolvedValue({
                    category,
                }),
            };
            container.register(RestaurantsRepository, { useValue: RestaurantsRepositoryMock as any });
            const newMapper = container.resolve(GmbMapper);

            const result = await newMapper.toPlatformMapper(additionalCategories, restaurantId, diffFields);

            const expectedResult = {
                categories: {
                    primaryCategory: {
                        displayName: category.categoryName.backup,
                        name: `categories/${category.categoryId}`,
                    },
                    additionalCategories: additionalCategories.categoryList.map((cat) => ({
                        displayName: cat.categoryName.backup,
                        name: `categories/${cat.categoryId}`,
                    })),
                },
            };
            expect(result).toEqual(expectedResult);
        });

        it('should not map latlng because Google does not allow it', async () => {
            const malouData = {
                latlng: {
                    lat: 48.864716,
                    lng: 2.349014,
                },
            };

            const mapper = container.resolve(GmbMapper);
            const result = await mapper.toPlatformMapper(malouData);

            expect(result).not.toHaveProperty('latlng');
        });

        it('should return socialNetworkUrls with all provider available when user remove all his social network urls from his restaurant', async () => {
            const malouData = {
                socialNetworkUrls: [],
            };
            const mapper = container.resolve(GmbMapper);
            const result = await mapper.toPlatformMapper(malouData);

            expect(result.attributes).toHaveLength(Object.keys(SocialNetworkKey).length);
        });

        it('regularHours - should return only open regular hours', async () => {
            const mapper = container.resolve(GmbMapper);
            const malouData = {
                regularHours: [
                    {
                        openDay: 'WEDNESDAY',
                        openTime: null,
                        closeDay: 'WEDNESDAY',
                        closeTime: null,
                        isClosed: true,
                    },
                    {
                        openDay: 'THURSDAY',
                        openTime: null,
                        closeDay: 'THURSDAY',
                        closeTime: null,
                        isClosed: true,
                    },
                    {
                        openDay: 'FRIDAY',
                        openTime: null,
                        closeDay: 'FRIDAY',
                        closeTime: null,
                        isClosed: true,
                    },
                ],
            };
            const restaurantId = null;
            const diffFields = ['regularHours'];

            const result = await mapper.toPlatformMapper(malouData, restaurantId, diffFields);

            const expectedResult = {
                regularHours: {
                    periods: [],
                },
            };
            expect(result).toEqual(expectedResult);
        });

        it('regularHours - it can handle overlapping days', async () => {
            const mapper = container.resolve(GmbMapper);
            const malouData = {
                regularHours: [
                    {
                        openDay: 'MONDAY',
                        openTime: '11:30',
                        closeDay: 'TUESDAY',
                        closeTime: '01:30',
                        isClosed: false,
                    },
                    {
                        openDay: 'TUESDAY',
                        openTime: '08:00',
                        closeDay: 'TUESDAY',
                        closeTime: '12:00',
                        isClosed: false,
                    },
                    {
                        openDay: 'WEDNESDAY',
                        openTime: null,
                        closeDay: 'WEDNESDAY',
                        closeTime: null,
                        isClosed: true,
                    },
                    {
                        openDay: 'THURSDAY',
                        openTime: null,
                        closeDay: 'THURSDAY',
                        closeTime: null,
                        isClosed: true,
                    },
                    {
                        openDay: 'FRIDAY',
                        openTime: null,
                        closeDay: 'FRIDAY',
                        closeTime: null,
                        isClosed: true,
                    },
                    {
                        openDay: 'SUNDAY',
                        openTime: '19:30',
                        closeDay: 'MONDAY',
                        closeTime: '00:30',
                        isClosed: false,
                    },
                    {
                        openDay: 'SATURDAY',
                        openTime: '19:30',
                        closeDay: 'SUNDAY',
                        closeTime: '00:30',
                        isClosed: false,
                    },
                ],
            };
            const restaurantId = null;
            const diffFields = ['regularHours'];

            const result = await mapper.toPlatformMapper(malouData, restaurantId, diffFields);

            const expectedResult = {
                regularHours: {
                    periods: [
                        {
                            openDay: 'MONDAY',
                            closeDay: 'TUESDAY',
                            openTime: {
                                hours: 11,
                                minutes: 30,
                            },
                            closeTime: {
                                hours: 1,
                                minutes: 30,
                            },
                        },
                        {
                            openDay: 'TUESDAY',
                            closeDay: 'TUESDAY',
                            openTime: {
                                hours: 8,
                                minutes: 0,
                            },
                            closeTime: {
                                hours: 12,
                                minutes: 0,
                            },
                        },
                        {
                            openDay: 'SUNDAY',
                            closeDay: 'MONDAY',
                            openTime: {
                                hours: 19,
                                minutes: 30,
                            },
                            closeTime: {
                                hours: 0,
                                minutes: 30,
                            },
                        },
                        {
                            openDay: 'SATURDAY',
                            closeDay: 'SUNDAY',
                            openTime: {
                                hours: 19,
                                minutes: 30,
                            },
                            closeTime: {
                                hours: 0,
                                minutes: 30,
                            },
                        },
                    ],
                },
            };
            expect(result).toEqual(expectedResult);
        });

        it('regularHours - it can handle multiple laps on the same day', async () => {
            const mapper = container.resolve(GmbMapper);
            const malouData = {
                regularHours: [
                    {
                        openDay: 'MONDAY',
                        openTime: '11:30',
                        closeDay: 'TUESDAY',
                        closeTime: '01:30',
                        isClosed: false,
                    },
                    {
                        openDay: 'TUESDAY',
                        openTime: '08:00',
                        closeDay: 'TUESDAY',
                        closeTime: '12:00',
                        isClosed: false,
                    },
                    {
                        openDay: 'TUESDAY',
                        openTime: '19:00',
                        closeDay: 'WEDNESDAY',
                        closeTime: '01:00',
                        isClosed: false,
                    },
                    {
                        openDay: 'WEDNESDAY',
                        openTime: null,
                        closeDay: 'WEDNESDAY',
                        closeTime: null,
                        isClosed: true,
                    },
                    {
                        openDay: 'THURSDAY',
                        openTime: null,
                        closeDay: 'THURSDAY',
                        closeTime: null,
                        isClosed: true,
                    },
                    {
                        openDay: 'FRIDAY',
                        openTime: '08:00',
                        closeDay: 'FRIDAY',
                        closeTime: '11:30',
                        isClosed: false,
                    },
                    {
                        openDay: 'FRIDAY',
                        openTime: '14:00',
                        closeDay: 'FRIDAY',
                        closeTime: '19:00',
                        isClosed: false,
                    },
                    {
                        openDay: 'SUNDAY',
                        openTime: '19:30',
                        closeDay: 'MONDAY',
                        closeTime: '00:30',
                        isClosed: false,
                    },
                    {
                        openDay: 'SATURDAY',
                        openTime: '19:30',
                        closeDay: 'SUNDAY',
                        closeTime: '00:30',
                        isClosed: false,
                    },
                ],
            };
            const restaurantId = null;
            const diffFields = ['regularHours'];

            const result = await mapper.toPlatformMapper(malouData, restaurantId, diffFields);

            const expectedResult = {
                regularHours: {
                    periods: [
                        {
                            openDay: 'MONDAY',
                            closeDay: 'TUESDAY',
                            openTime: {
                                hours: 11,
                                minutes: 30,
                            },
                            closeTime: {
                                hours: 1,
                                minutes: 30,
                            },
                        },
                        {
                            openDay: 'TUESDAY',
                            closeDay: 'TUESDAY',
                            openTime: {
                                hours: 8,
                                minutes: 0,
                            },
                            closeTime: {
                                hours: 12,
                                minutes: 0,
                            },
                        },
                        {
                            openDay: 'TUESDAY',
                            closeDay: 'WEDNESDAY',
                            openTime: {
                                hours: 19,
                                minutes: 0,
                            },
                            closeTime: {
                                hours: 1,
                                minutes: 0,
                            },
                        },
                        {
                            openDay: 'FRIDAY',
                            closeDay: 'FRIDAY',
                            openTime: {
                                hours: 8,
                                minutes: 0,
                            },
                            closeTime: {
                                hours: 11,
                                minutes: 30,
                            },
                        },
                        {
                            openDay: 'FRIDAY',
                            closeDay: 'FRIDAY',
                            openTime: {
                                hours: 14,
                                minutes: 0,
                            },
                            closeTime: {
                                hours: 19,
                                minutes: 0,
                            },
                        },
                        {
                            openDay: 'SUNDAY',
                            closeDay: 'MONDAY',
                            openTime: {
                                hours: 19,
                                minutes: 30,
                            },
                            closeTime: {
                                hours: 0,
                                minutes: 30,
                            },
                        },
                        {
                            openDay: 'SATURDAY',
                            closeDay: 'SUNDAY',
                            openTime: {
                                hours: 19,
                                minutes: 30,
                            },
                            closeTime: {
                                hours: 0,
                                minutes: 30,
                            },
                        },
                    ],
                },
            };
            expect(result).toEqual(expectedResult);
        });

        it('serviceHours - it can handle overlapping days', async () => {
            const mapper = container.resolve(GmbMapper);
            const testCase = new TestCaseBuilderV2({
                seeds: {
                    hoursTypes: {
                        data() {
                            return [
                                getDefaultHoursTypes().build(),
                                getDefaultHoursTypes().hoursType('HAPPY_HOUR').build(),
                                getDefaultHoursTypes().hoursType('BREAKFAST').build(),
                            ];
                        },
                    },
                },
            });
            await testCase.build();

            const { hoursTypes } = testCase.getSeededObjects() as { hoursTypes: IHoursType[] };
            const brunchHoursTypeId = hoursTypes.find((ht) => ht.hoursType === 'HAPPY_HOUR')?._id?.toString();
            const breakfastHoursTypeId = hoursTypes.find((ht) => ht.hoursType === 'BREAKFAST')?._id?.toString();

            const malouData = {
                otherHours: [
                    {
                        hoursTypeId: brunchHoursTypeId,
                        periods: [
                            {
                                openDay: 'TUESDAY',
                                openTime: '14:00',
                                closeDay: 'TUESDAY',
                                closeTime: '20:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'WEDNESDAY',
                                openTime: '14:00',
                                closeDay: 'WEDNESDAY',
                                closeTime: '20:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'THURSDAY',
                                openTime: '14:00',
                                closeDay: 'THURSDAY',
                                closeTime: '20:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'FRIDAY',
                                openTime: '14:00',
                                closeDay: 'FRIDAY',
                                closeTime: '20:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'MONDAY',
                                openTime: '14:00',
                                closeDay: 'MONDAY',
                                closeTime: '20:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'SATURDAY',
                                openTime: '20:00',
                                closeDay: 'SUNDAY',
                                closeTime: '00:30',
                                isClosed: false,
                            },
                            {
                                openDay: 'SUNDAY',
                                openTime: '20:00',
                                closeDay: 'MONDAY',
                                closeTime: '00:30',
                                isClosed: false,
                            },
                        ],
                    },
                    {
                        hoursTypeId: breakfastHoursTypeId,
                        periods: [
                            {
                                openDay: 'TUESDAY',
                                openTime: '11:00',
                                closeDay: 'TUESDAY',
                                closeTime: '13:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'TUESDAY',
                                openTime: '18:00',
                                closeDay: 'TUESDAY',
                                closeTime: '21:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'THURSDAY',
                                openTime: '11:00',
                                closeDay: 'THURSDAY',
                                closeTime: '13:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'THURSDAY',
                                openTime: '18:00',
                                closeDay: 'THURSDAY',
                                closeTime: '21:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'FRIDAY',
                                openTime: '11:00',
                                closeDay: 'FRIDAY',
                                closeTime: '13:00',
                                isClosed: false,
                            },
                            {
                                openDay: 'FRIDAY',
                                openTime: '18:00',
                                closeDay: 'FRIDAY',
                                closeTime: '21:00',
                                isClosed: false,
                            },
                        ],
                    },
                ],
            };
            const restaurantId = null;
            const diffFields = ['otherHours'];

            const result = await mapper.toPlatformMapper(malouData, restaurantId, diffFields);

            const expectedResult = {
                moreHours: [
                    {
                        hoursTypeId: 'HAPPY_HOUR',
                        periods: [
                            {
                                openDay: 'TUESDAY',
                                closeDay: 'TUESDAY',
                                openTime: {
                                    hours: 14,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 20,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'WEDNESDAY',
                                closeDay: 'WEDNESDAY',
                                openTime: {
                                    hours: 14,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 20,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'THURSDAY',
                                closeDay: 'THURSDAY',
                                openTime: {
                                    hours: 14,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 20,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'FRIDAY',
                                closeDay: 'FRIDAY',
                                openTime: {
                                    hours: 14,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 20,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'MONDAY',
                                closeDay: 'MONDAY',
                                openTime: {
                                    hours: 14,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 20,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'SATURDAY',
                                closeDay: 'SUNDAY',
                                openTime: {
                                    hours: 20,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 0,
                                    minutes: 30,
                                },
                            },
                            {
                                openDay: 'SUNDAY',
                                closeDay: 'MONDAY',
                                openTime: {
                                    hours: 20,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 0,
                                    minutes: 30,
                                },
                            },
                        ],
                    },
                    {
                        hoursTypeId: 'BREAKFAST',
                        periods: [
                            {
                                openDay: 'TUESDAY',
                                closeDay: 'TUESDAY',
                                openTime: {
                                    hours: 11,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 13,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'TUESDAY',
                                closeDay: 'TUESDAY',
                                openTime: {
                                    hours: 18,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 21,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'THURSDAY',
                                closeDay: 'THURSDAY',
                                openTime: {
                                    hours: 11,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 13,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'THURSDAY',
                                closeDay: 'THURSDAY',
                                openTime: {
                                    hours: 18,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 21,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'FRIDAY',
                                closeDay: 'FRIDAY',
                                openTime: {
                                    hours: 11,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 13,
                                    minutes: 0,
                                },
                            },
                            {
                                openDay: 'FRIDAY',
                                closeDay: 'FRIDAY',
                                openTime: {
                                    hours: 18,
                                    minutes: 0,
                                },
                                closeTime: {
                                    hours: 21,
                                    minutes: 0,
                                },
                            },
                        ],
                    },
                ],
            };
            expect(result).toEqual(expectedResult);
        });
    });

    describe('mapGmbAttributesToRestaurantAttributes', () => {
        it(`should filter attributes if we don't retrieve them from DB`, async () => {
            const gmbAttributes = [
                {
                    name: 'attributes/serves_wine',
                    valueType: AttributeValueType.BOOL,
                    values: [false],
                },
                {
                    name: 'attributes/serves_beer',
                    valueType: AttributeValueType.BOOL,
                    values: [false],
                },
                {
                    name: 'attributes/has_seating',
                    valueType: AttributeValueType.BOOL,
                    values: [true],
                },
                {
                    name: 'attributes/serves_liquor',
                    valueType: AttributeValueType.BOOL,
                    values: [false],
                },
                {
                    name: 'attributes/has_onsite_services',
                    valueType: AttributeValueType.BOOL,
                    values: [true],
                },
                {
                    name: 'attributes/pay_credit_card',
                    valueType: AttributeValueType.BOOL,
                    values: [true],
                },
                {
                    name: 'attributes/welcomes_families',
                    valueType: AttributeValueType.BOOL,
                    values: [true],
                },
                {
                    name: 'attributes/has_high_chairs',
                    valueType: AttributeValueType.BOOL,
                    values: [true],
                },
            ];
            const gmbMapper = container.resolve(GmbMapper);
            const result = await gmbMapper.mapGmbAttributesToRestaurantAttributes(gmbAttributes);

            expect(result).toBeEmpty();
        });
    });
});
