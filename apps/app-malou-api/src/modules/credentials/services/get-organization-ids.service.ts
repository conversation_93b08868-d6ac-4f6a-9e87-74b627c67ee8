import lodash from 'lodash';
import { singleton } from 'tsyringe';

import { isNotNil, PlatformKey } from '@malou-io/package-utils';

import CredentialsRepository from ':modules/credentials/credentials.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class GetOrganizationIdsService {
    constructor(
        private readonly _credentialsRepository: CredentialsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute({
        platformKey,
        authId,
        userId,
        organizationId,
        restaurantId,
    }: {
        platformKey: PlatformKey;
        authId: string;
        userId: string;
        organizationId?: string;
        restaurantId?: string;
    }): Promise<string[]> {
        let organizationIds: string[] = [];

        const knownCredential = await this._credentialsRepository.findOne({
            filter: {
                authId,
                key: platformKey,
                userId,
            },
            options: { lean: true },
        });

        if (knownCredential) {
            const platforms = await this._platformsRepository.find({
                filter: { 'credentials.0': knownCredential._id },
                projection: { restaurantId: 1 },
                options: { lean: true },
            });
            const restaurantIds = lodash.uniq(platforms.map((p) => p.restaurantId));
            const restaurants = await this._restaurantsRepository.find({
                filter: { _id: { $in: restaurantIds } },
                projection: { organizationId: 1 },
                options: { lean: true },
            });
            organizationIds = restaurants.map((r) => r.organizationId.toString());
            if (knownCredential.organizationIds) {
                organizationIds = organizationIds.concat(
                    knownCredential.organizationIds.map((orgId) => orgId?.toString()).filter(isNotNil)
                );
            }
        }

        if (organizationId) {
            organizationIds.push(organizationId);
        }

        if (restaurantId) {
            const { organizationId: currentRestaurantOrganizationId } = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: restaurantId },
                projection: { organizationId: 1 },
                options: { lean: true },
            });
            if (currentRestaurantOrganizationId) {
                organizationIds.push(currentRestaurantOrganizationId.toString());
            }
        }

        return organizationIds;
    }
}
