import { RoiSettingsDto } from '@malou-io/package-dto';
import { IRoiSettingsWithRestaurant } from '@malou-io/package-models';
import { Currency } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';

export class RoiSettings {
    id?: string;
    restaurantId: string;
    currency: Currency;
    averageTicket: number;
    minRevenue: number;
    maxRevenue: number;
    duplicatedFromRestaurantId?: string;
    restaurant?: { _id: string };
    createdAt: Date;
    updatedAt: Date;

    platform?: Platform;

    constructor(data: IRoiSettingsWithRestaurant) {
        this.id = data._id?.toString();
        this.restaurantId = data.restaurantId.toString();
        this.currency = data.currency;
        this.averageTicket = data.averageTicket;
        this.maxRevenue = data.maxRevenue;
        this.minRevenue = data.minRevenue;
        this.duplicatedFromRestaurantId = data.duplicatedFromRestaurantId?.toString();
        this.createdAt = data.createdAt;
        this.updatedAt = data.updatedAt;
        this.restaurant = data.restaurant ? { _id: data.restaurant._id.toString() } : undefined;
    }

    toDto(): RoiSettingsDto {
        return {
            id: this.id,
            restaurantId: this.restaurantId,
            currency: this.currency,
            averageTicket: this.averageTicket,
            maxRevenue: this.maxRevenue,
            minRevenue: this.minRevenue,
            duplicatedFromRestaurantId: this.duplicatedFromRestaurantId,
            restaurant: this.restaurant ? { _id: this.restaurant._id } : undefined,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }

    isComplete(): boolean {
        return !!this.minRevenue && !!this.maxRevenue && !!this.averageTicket;
    }
}
