import { createObjectCsvWriter as createCsvWriter } from 'csv-writer';
import { NextFunction, Request, Response } from 'express';
import { inject, singleton } from 'tsyringe';
import { DeepRequired } from 'utility-types';

import {
    DownloadInsightsAsPdfBodyDto,
    downloadInsightsAsPdfBodyValidator,
    DownloadInsightsAsPdfResponseDto,
    GetInsightsAggregatedRequestBodyDto,
    getInsightsAggregatedRequestBodyValidator,
    GetStoredInsightsAggregatedRequestBodyDto,
    getStoredInsightsAggregatedRequestBodyValidator,
    GetStoredInsightsRequestBodyDto,
    getStoredInsightsRequestBodyValidator,
    StoredInsightsAggregatedResponseDto,
    StoredInsightsResponseDto,
} from '@malou-io/package-dto';
import { ApiResultV2, errorReplacer, MalouErrorCode, PartialRecord, PlatformKey, TimeInSeconds } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Body } from ':helpers/decorators/validators';
import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { DownloadInsightsAsPdfBody, TimeScaleToMetricToDataValues } from ':modules/platform-insights/platform-insights.types';
import { DownloadInsightsAsPdfUseCase } from ':modules/platform-insights/use-cases/download-insights-as-pdf/download-insights-as-pdf.use-case';
import { GetAggregatedInsightsForRestaurantAndPlatformUseCase } from ':modules/platform-insights/use-cases/get-aggregated-insights-for-restaurant-and-platform/get-aggregated-insights-for-restaurant-and-platform.use-case';
import { GetAggregatedInsightsForRestaurantsUseCase } from ':modules/platform-insights/use-cases/get-aggregated-insights-for-restaurants/get-aggregated-inisghts-for-restaurants.use-case';
import { GetStoredInsightsAggregatedUseCase } from ':modules/platform-insights/use-cases/get-stored-insights-aggregated/get-stored-insights-aggregated.use-case';
import { GetStoredInsightsUseCase } from ':modules/platform-insights/use-cases/get-stored-insights/get-stored-insights.use-case';
import { Cache } from ':plugins/cache';
import { CachePrefixKey, computeCacheKey } from ':plugins/cache-middleware';

type InsightsByPlatformByRestaurant = PartialRecord<string, PartialRecord<PlatformKey, TimeScaleToMetricToDataValues>>;

interface InsightsWithDetails {
    restaurantId: string;
    platformKey: PlatformKey;
    insights: TimeScaleToMetricToDataValues;
}

@singleton()
export class PlatformInsightsController {
    constructor(
        private readonly _downloadInsightsAsPdfUseCase: DownloadInsightsAsPdfUseCase,
        private readonly _getAggregatedInsightsForRestaurantsUseCase: GetAggregatedInsightsForRestaurantsUseCase,
        private readonly _getAggregatedInsightsForRestaurantAndPlatformUseCase: GetAggregatedInsightsForRestaurantAndPlatformUseCase,
        private readonly _getStoredInsightsUseCase: GetStoredInsightsUseCase,
        private readonly _getStoredInsightsAggregatedUseCase: GetStoredInsightsAggregatedUseCase,
        @inject(InjectionToken.Cache) private readonly _cache: Cache
    ) {}

    handleWriteInsights = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { filename } = req.params;
            const { body: result } = req;
            const csvWriter = createCsvWriter({
                path: `${__dirname}/${filename}.csv`,
                header: [
                    { id: 'name', title: 'Name' },
                    { id: '_id', title: '_id' },
                    { id: 'queries_indirect', title: 'queries_indirect' },
                    { id: 'queries_direct', title: 'queries_direct' },
                    { id: 'actions_website', title: 'actions_website' },
                    { id: 'actions_phone', title: 'actions_phone' },
                    { id: 'actions_driving_directions', title: 'actions_driving_directions' },
                    { id: 'year', title: 'year' },
                ],
            });
            await csvWriter.writeRecords(result);
            res.json({ msg: 'ok' });
        } catch (err) {
            next(err);
        }
    };

    @Body(getInsightsAggregatedRequestBodyValidator)
    async handleGetInsightsAggregated(req: Request<any, any, GetInsightsAggregatedRequestBodyDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantIds } = req.body;

            if (!restaurantIds || !restaurantIds?.length) {
                return res.status(204).json({
                    data: null,
                });
            }

            const { startDate, endDate, comparisonPeriod, previousPeriod, platformKeys, aggregators, metrics } = req.body;
            const insightsConditions = { metrics, aggregators };
            const periodConditions = {
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                previousPeriod: previousPeriod ?? false,
                comparisonPeriod: comparisonPeriod ?? undefined,
            };

            const insightsWithDetailsPromises = restaurantIds.flatMap((restaurantId): Promise<InsightsWithDetails>[] =>
                platformKeys.map(async (platformKey): Promise<InsightsWithDetails> => {
                    const suffixKey = `${restaurantId}-${platformKey}-${JSON.stringify(insightsConditions)}-${JSON.stringify(
                        periodConditions
                    )}`;
                    const key: string = computeCacheKey(CachePrefixKey.GET_INSIGHTS_AGGREGATED_BY_RESTAURANT, suffixKey);
                    const cachedData: TimeScaleToMetricToDataValues = await this._cache.get(key);
                    if (cachedData && !cachedData.error) {
                        return { restaurantId, platformKey, insights: cachedData };
                    }
                    const insights = await this._getAggregatedInsightsForRestaurantAndPlatformUseCase.execute({
                        restaurantId,
                        platformKey,
                        insightsConditions,
                        periodConditions,
                    });
                    if (insights && !insights.error) {
                        await this._cache.set(key, insights, 10 * TimeInSeconds.MINUTE);
                    }
                    return { restaurantId, platformKey, insights };
                })
            );

            const insightsWithDetails = await Promise.all(insightsWithDetailsPromises);
            if (insightsWithDetails.length > 0 && insightsWithDetails.every(({ insights }) => insights?.error)) {
                throw new MalouError(MalouErrorCode.PLATFORM_INSIGHTS_ERROR, {
                    metadata: {
                        index: insightsWithDetails.findIndex((insight) => insight.insights.error),
                        detail: insightsWithDetails.find((insight) => insight.insights.error)?.insights.message,
                        key: insightsWithDetails.find((insight) => insight.insights.error)?.platformKey,
                    },
                });
            }

            const insightsByPlatformByRestaurant: InsightsByPlatformByRestaurant = insightsWithDetails.reduce(
                (acc: InsightsByPlatformByRestaurant, { restaurantId, platformKey, insights }) => {
                    if (!acc[restaurantId]) {
                        acc[restaurantId] = {};
                    }
                    (acc[restaurantId] as Partial<Record<PlatformKey, TimeScaleToMetricToDataValues>>)[platformKey] = insights;
                    return acc;
                },
                {}
            );

            return res.json({
                data: insightsByPlatformByRestaurant,
            });
        } catch (err) {
            next(err);
        }
    }

    @Body(getInsightsAggregatedRequestBodyValidator)
    async handleGetInsightsAggregatedV2(req: Request<any, any, GetInsightsAggregatedRequestBodyDto>, res: Response, next: NextFunction) {
        try {
            const insightsByPlatformByRestaurant = await this._getAggregatedInsightsForRestaurantsUseCase.execute(req.body, true);
            return res.json({
                data: insightsByPlatformByRestaurant,
            });
        } catch (err) {
            logger.error('[ERROR_AGGREGATED_STATS] [PLATFORM_INSIGHTS]', {
                error: JSON.stringify(err, errorReplacer),
                query: JSON.stringify(req.query),
            });
            next(err);
        }
    }

    @Body(downloadInsightsAsPdfBodyValidator)
    async handleDownloadInsightsAsPdf(
        req: Request<never, never, DownloadInsightsAsPdfBodyDto>,
        res: Response<ApiResultV2<DownloadInsightsAsPdfResponseDto>>,
        next: NextFunction
    ) {
        try {
            const body: DownloadInsightsAsPdfBody = {
                callbackUrl: req.body.callbackUrl,
                params: req.body.params,
                jwtToken: req.body.jwtToken,
                restaurantId: req.body.restaurantId,
                insightTab: req.body.insightTab,
                userId: req.user._id.toString(),
            };
            const pdfUrl = await this._downloadInsightsAsPdfUseCase.execute(body);
            return res.status(200).json({
                data: pdfUrl,
            });
        } catch (error) {
            next(error);
        }
    }

    @Body(getStoredInsightsRequestBodyValidator)
    async handleGetStoredInsights(
        req: Request<any, any, DeepRequired<GetStoredInsightsRequestBodyDto>>,
        res: Response<ApiResultV2<StoredInsightsResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds, startDate, endDate, metrics, platformKeys, comparisonPeriod } = req.body;
            const result = await this._getStoredInsightsUseCase.execute({
                restaurantIds,
                startDate,
                endDate,
                metrics,
                platformKeys,
                comparisonPeriod,
            });
            return res.status(200).json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(getStoredInsightsAggregatedRequestBodyValidator)
    async handleGetStoredInsightsAggregated(
        req: Request<any, any, GetStoredInsightsAggregatedRequestBodyDto>,
        res: Response<ApiResultV2<StoredInsightsAggregatedResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds, startDate, endDate, metrics, platformKeys, previousPeriod, aggregationType } = req.body;
            const result = await this._getStoredInsightsAggregatedUseCase.execute({
                restaurantIds,
                startDate,
                endDate,
                metrics,
                platformKeys,
                previousPeriod,
                aggregationType,
            });
            return res.status(200).json({ data: result });
        } catch (err) {
            next(err);
        }
    }
}
