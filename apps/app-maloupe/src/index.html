<!doctype html>
<html lang="en">
    <head>
        <!-- Google Tag Manager -->
        <script>
            if (window.location.href.includes('diagnostic.malou')) {
                (function (w, d, s, l, i) {
                    w[l] = w[l] || [];
                    w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
                    var f = d.getElementsByTagName(s)[0],
                        j = d.createElement(s),
                        dl = l != 'dataLayer' ? '&l=' + l : '';
                    j.async = true;
                    j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                    f.parentNode.insertBefore(j, f);
                })(window, document, 'script', 'dataLayer', 'GTM-WPCS4D3');
            }
        </script>
        <!-- End Google Tag Manager -->
        <meta charset="utf-8" />
        <!-- title-->
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const lang = localStorage.getItem('lang') ?? 'en';
                const titles = {
                    en: 'Online Visibility Score',
                    fr: 'Diagnostic de visibilité',
                };
                document.title = titles[lang];
            });
        </script>
        <title>Diagnostic de visibilité</title>
        <!-- End Title-->

        <base href="/" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />

        <!--favicons-->
        <link href="/assets/favicons/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
        <link type="image/png" href="/assets/favicons/favicon-32x32.png" rel="icon" sizes="32x32" />
        <link type="image/png" href="/assets/favicons/favicon-16x16.png" rel="icon" sizes="16x16" />
        <link href="/assets/favicons/site.webmanifest" rel="manifest" />
        <link href="/assets/favicons/safari-pinned-tab.svg" rel="mask-icon" color="#5bbad5" />
        <link href="/assets/favicons/favicon.ico" rel="shortcut icon" />
        <meta name="apple-mobile-web-app-title" content="Malou" />
        <meta name="application-name" content="Malou" />
        <meta name="msapplication-TileColor" content="#da532c" />
        <meta name="msapplication-config" content="/assets/favicons/browserconfig.xml" />
        <meta name="theme-color" content="#ffffff" />
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />

        <!-- Start Heap -->
        <script type="text/javascript">
            if (window.location.href.includes('diagnostic.malou')) {
                (window.heap = window.heap || []),
                    (heap.load = function (e, t) {
                        (window.heap.appid = e), (window.heap.config = t = t || {});
                        var r = document.createElement('script');
                        (r.type = 'text/javascript'), (r.async = !0), (r.src = 'https://cdn.heapanalytics.com/js/heap-' + e + '.js');
                        var a = document.getElementsByTagName('script')[0];
                        a.parentNode.insertBefore(r, a);
                        for (
                            var n = function (e) {
                                    return function () {
                                        heap.push([e].concat(Array.prototype.slice.call(arguments, 0)));
                                    };
                                },
                                p = [
                                    'addEventProperties',
                                    'addUserProperties',
                                    'clearEventProperties',
                                    'identify',
                                    'resetIdentity',
                                    'removeEventProperty',
                                    'setEventProperties',
                                    'track',
                                    'unsetEventProperty',
                                ],
                                o = 0;
                            o < p.length;
                            o++
                        )
                            heap[p[o]] = n(p[o]);
                    });
                heap.load('2391935765');
            } else {
                window.heap = {};
            }
        </script>
        <!-- End Heap -->

        <!-- Start of HubSpot Embed Code -->
        <script id="hs-script-loader" src="//js-eu1.hs-scripts.com/25820972.js" type="text/javascript" async defer></script>
        <!-- End of HubSpot Embed Code -->
    </head>
    <body>
        <!-- Google Tag Manager (noscript) -->
        <noscript
            ><iframe
                src="https://www.googletagmanager.com/ns.html?id=GTM-WPCS4D3"
                height="0"
                width="0"
                style="display: none; visibility: hidden"></iframe
        ></noscript>
        <!-- End Google Tag Manager (noscript) -->
        <app-root></app-root>
        <noscript>Please enable JavaScript to continue using this application.</noscript>
    </body>
</html>
