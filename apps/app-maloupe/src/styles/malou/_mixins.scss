@use 'sass:map';
@use 'malou_variables' as *;

@mixin malou-shadow-up {
    box-shadow: 0px 14px 28px rgba($color: $malou-color-black, $alpha: 0.25);
}

@mixin disabled($textColor) {
    &:disabled {
        color: $textColor;
        --mdc-protected-button-disabled-label-text-color: #{$textColor};
        --mdc-text-button-disabled-label-text-color: #{$textColor};
        --mdc-filled-button-disabled-label-text-color: #{$textColor};
        filter: opacity(0.5);

        &:hover::before {
            opacity: 0;
        }
    }
}

/// Mixin to manage responsive breakpoints
@mixin malou-respond-to($breakpoint) {
    // If the key exists in the map
    @if map.has-key($malou-breakpoints, $breakpoint) {
        // Prints a media query based on the value
        @media (max-width: map.get($malou-breakpoints, $breakpoint)) {
            @content;
        }
    }

    // If the key doesn't exist in the map
    @else {
        @warn "Unfortunately, no value could be retrieved from `#{$breakpoint}`. "
        + "Available breakpoints are: #{map.keys($malou-breakpoints)}.";
    }
}
