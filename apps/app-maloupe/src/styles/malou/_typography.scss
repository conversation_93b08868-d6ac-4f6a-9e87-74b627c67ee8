@use 'sass:map';
@use 'malou_variables.scss' as var;

// Font Weight

@each $weight in map.keys(var.$font-weights) {
    .malou-text-weight-#{$weight} {
        font-weight: map.get(var.$font-weights, $weight);
    }
}

// Font Size

@each $size in var.$font-sizes {
    .malou-text-#{$size} {
        font-size: #{$size}px;

        @each $weight in map.keys(var.$font-weights) {
            &--#{$weight} {
                font-size: #{$size}px;
                font-weight: map.get(var.$font-weights, $weight);
            }
        }
    }
}
