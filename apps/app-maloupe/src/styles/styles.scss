@use 'sass:map';
@use './malou/_malou.scss' as *;

@tailwind base;
@tailwind components;
@tailwind utilities;

body,
html {
    line-height: 1.5;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    min-height: 100%;
    font-family: 'Poppins', sans-serif;
    --mdc-typography-body1-font-family: 'Poppins', sans-serif;
    --mdc-typography-body2-font-family: 'Poppins', sans-serif;
    --mat-menu-item-label-text-font: 'Poppins', sans-serif;
    --mat-table-header-headline-font: 'Poppins', sans-serif;
    --mat-table-row-item-label-text-font: 'Poppins', sans-serif;
    --mat-table-footer-supporting-text-font: 'Poppins', sans-serif;
    // all values here : https://m2.material.io/develop/web/guides/typography#typography-styles
    --mdc-typography-headline1-letter-spacing: normal;
    --mdc-typography-headline2-letter-spacing: normal;
    --mdc-typography-headline3-letter-spacing: normal;
    --mdc-typography-headline4-letter-spacing: normal;
    --mdc-typography-headline5-letter-spacing: normal;
    --mdc-typography-headline6-letter-spacing: normal;
    --mdc-typography-subtitle1-letter-spacing: normal;
    --mdc-typography-subtitle2-letter-spacing: normal;
    --mdc-typography-body1-letter-spacing: normal;
    --mdc-typography-body2-letter-spacing: normal;
    --mdc-typography-caption-letter-spacing: normal;
    --mdc-typography-button-letter-spacing: normal;
    --mdc-typography-overline-letter-spacing: normal;
    --mat-tab-header-label-text-tracking: 0;
    --mat-menu-item-label-text-tracking: 0;
    --mat-option-label-text-tracking: 0;
    --mat-select-trigger-text-tracking: 0;
    --mat-expansion-container-text-tracking: 0;
    --mat-paginator-container-text-tracking: 0;
    --mat-table-row-item-label-text-tracking: 0;
}

// we have to duplicate the styles here because you can't use "@ tailwind utilities" in any files
// you can use a preprocessor but it seems to slow down the build process : https://tailwindcss.com/docs/using-with-preprocessors#using-sass-less-or-stylus
@layer utilities {
    // Font Weight

    @each $weight in map.keys($font-weights) {
        .malou-text-weight-#{$weight} {
            font-weight: map.get($font-weights, $weight);
        }
    }

    // Font Size

    @each $size in $font-sizes {
        .malou-text-#{$size} {
            font-size: #{$size}px;

            @each $weight in map.keys($font-weights) {
                &--#{$weight} {
                    font-size: #{$size}px;
                    font-weight: map.get($font-weights, $weight);
                }
            }
        }
    }
}
