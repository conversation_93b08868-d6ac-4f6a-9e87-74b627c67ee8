import { Routes } from '@angular/router';

import { AggregatedRestaurantsDiagnosticsLoaderComponent } from ':modules/aggregated-restaurants-diagnostics-loader/aggregated-restaurants-diagnostics-loader.component';
import { DiagnosticResultComponent } from ':modules/diagnostic-result/diagnostic-result.component';
import { LoginFormComponent } from ':modules/login-form/login-form.component';
import { RestaurantDiagnosticEventComponent } from ':modules/restaurant-diagnostic-events/restaurant-diagnostic-event.component';
import { RestaurantDiagnosticComponent } from ':modules/restaurant-diagnostic-form/restaurant-diagnostic.component';
import { RestaurantDiagnosticSearchComponent } from ':modules/restaurant-diagnostic-search/restaurant-diagnostic-search.component';

export const LOGGED_OUT_ROUTES: Routes = [
    {
        path: '',
        redirectTo: 'search',
        pathMatch: 'full',
    },
    {
        path: 'search',
        component: RestaurantDiagnosticSearchComponent,
    },
    {
        path: 'form',
        component: RestaurantDiagnosticComponent,
    },
    {
        path: 'compute-diagnostics/:diagnostic_ids',
        component: AggregatedRestaurantsDiagnosticsLoaderComponent,
    },
    {
        path: 'diagnostic',
        component: DiagnosticResultComponent,
    },
    {
        path: 'login',
        component: LoginFormComponent,
    },
    {
        path: 'event',
        component: RestaurantDiagnosticEventComponent,
    },
    {
        path: '**',
        redirectTo: 'search',
    },
];
