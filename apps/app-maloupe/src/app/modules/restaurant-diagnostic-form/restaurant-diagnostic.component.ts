import { Component, inject, OnInit, signal, WritableSignal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { HeapEventName } from '@malou-io/package-utils';

import { DiagnosticHttpService } from ':core/http-services/diagnostic.http-service';
import { AggregatedDiagnosticsScoreComponent } from ':modules/aggregated-diagnostics-score/aggregated-diagnostics-score.component';
import { DiagnosticScoreComponent } from ':modules/diagnostic-score/diagnostic-score.component';
import { DiagnosticInformationFormComponent } from ':modules/restaurant-diagnostic-form/diagnostic-information-form/diagnostic-information-form.component';
import { Diagnostic } from ':shared/models';
import { getUserIdentityFromLocalStorage } from ':shared/utils/manage-user-uuid';

@Component({
    selector: 'app-restaurant-diagnostic',
    imports: [DiagnosticInformationFormComponent, DiagnosticScoreComponent, AggregatedDiagnosticsScoreComponent],
    templateUrl: './restaurant-diagnostic.component.html',
    styleUrl: './restaurant-diagnostic.component.scss',
})
export class RestaurantDiagnosticComponent implements OnInit {
    private readonly _diagnosticHttpService = inject(DiagnosticHttpService);
    private readonly _activatedRoute = inject(ActivatedRoute);

    readonly diagnostics: WritableSignal<Diagnostic[] | null> = signal(null);

    ngOnInit(): void {
        this._activatedRoute.queryParamMap.subscribe((params) => {
            const diagnosticIds = params.getAll('diagnostic_id');
            if (diagnosticIds.length !== 0) {
                this._diagnosticHttpService.getDiagnosticsByIds$(diagnosticIds).subscribe(({ diagnostics }) => {
                    this.diagnostics.set(diagnostics.map((diagnostic) => new Diagnostic(diagnostic)));
                });
                this._diagnosticHttpService
                    .trackDiagnosticAction$({
                        identity: getUserIdentityFromLocalStorage() ?? '',
                        eventName: HeapEventName.MALOUPE_TRACKING_GET_DIAGNOSTIC_SNIPPET,
                        diagnosticIds: diagnosticIds.join(','),
                    })
                    .subscribe();
            }
        });
    }
}
