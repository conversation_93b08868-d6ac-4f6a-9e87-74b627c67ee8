import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { Component, computed, DestroyRef, inject, OnInit, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, forkJoin, map, switchMap } from 'rxjs';

import { DiagnosticDto, InstagramPageInfoDto } from '@malou-io/package-dto';
import { HeapEventName, MaloupeLocale, PlatformKey } from '@malou-io/package-utils';

import { AuthContext } from ':core/context/auth.context';
import { CategoryHttpService } from ':core/http-services/category.http-service';
import { DiagnosticHttpService } from ':core/http-services/diagnostic.http-service';
import { ToastService } from ':core/services/toast.service';
import {
    DataCount,
    SearchStep,
    Step,
} from ':modules/restaurant-diagnostic-search/main-restaurant-diagnostic-search/main-restaurant-diagnostic-search.interface';
import { DiagnosticHeaderComponent } from ':shared/components/diagnostic-header/diagnostic-header.component';
import { InputGoogleMapsAutocompleteComponent } from ':shared/components/input-google-maps-autocomplete/input-google-maps-autocomplete.component';
import { InputInstagramSearchComponent } from ':shared/components/input-instagram-search/input-instagram-search.component';
import { ToastDuration } from ':shared/components/toast/toast-item/toast-item.component';
import { DELAY_TO_SCROLL_TO_SECTION, LIMITS_AND_TIME_BY_LOCAL_KEY } from ':shared/constants';
import { SvgIcon } from ':shared/enums';
import { RequestLocalStorageKey } from ':shared/enums/storage-key.enum';
import { Category, Diagnostic } from ':shared/models';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';
import { buildNewRequestDetails, increaseRequestCount } from ':shared/utils';
import { getUserIdentityFromLocalStorage } from ':shared/utils/manage-user-uuid';

const DEFAULT_CATEGORY_ID = 'gcid:restaurant';

@Component({
    selector: 'app-main-restaurant-diagnostic-search',
    templateUrl: './main-restaurant-diagnostic-search.component.html',
    styleUrl: './main-restaurant-diagnostic-search.component.scss',
    imports: [
        DiagnosticHeaderComponent,
        InputGoogleMapsAutocompleteComponent,
        InputInstagramSearchComponent,
        FormsModule,
        MatAutocompleteModule,
        MatButtonModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatSelectModule,
        MatSlideToggleModule,
        ReactiveFormsModule,
        TranslateModule,
        NgTemplateOutlet,
        NgClass,
        PluralTranslatePipe,
    ],
})
export class MainRestaurantDiagnosticSearchComponent implements OnInit {
    private readonly _categoryHttpService = inject(CategoryHttpService);
    private readonly _diagnosticHttpService = inject(DiagnosticHttpService);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _authContext = inject(AuthContext);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _translateService = inject(TranslateService);
    private readonly _router = inject(Router);
    private readonly _formBuilder = inject(FormBuilder);
    private readonly _toastService = inject(ToastService);

    readonly SvgIcon = SvgIcon;
    readonly PlatformKey = PlatformKey;
    readonly SearchStep = SearchStep;
    readonly DataCount = DataCount;
    readonly MAX_LOCATION_COUNT = 5;
    readonly locale: MaloupeLocale = this._translateService.currentLang as MaloupeLocale;

    readonly searchSteps: WritableSignal<Step[]> = signal([
        {
            step: SearchStep.LOCATION_COUNT,
            title: 'maloupe.diagnostic_search.main.steps.step_one.title',
            shouldBeDisplayed: true,
        },
        {
            step: SearchStep.INSTAGRAM_ACCOUNT_COUNT,
            title: 'maloupe.diagnostic_search.main.steps.step_two.title',
            shouldBeDisplayed: false,
            instagramAccountCount: DataCount.MANY,
        },
        {
            step: SearchStep.LOCATION_SCAN,
            title: 'maloupe.diagnostic_search.main.steps.step_three.title',
            shouldBeDisplayed: false,
        },
    ]);
    readonly isOneLocation = signal<boolean | null>(null);
    readonly instagramAccountCount = signal<DataCount | null>(null);
    readonly diagnostics = signal<(Diagnostic | null)[]>([]);
    readonly autocompleteErrorMessage = signal<string[]>([]);
    readonly instagramPages: WritableSignal<(InstagramPageInfoDto | null)[]> = signal([]);
    readonly instagramPagesValidated: WritableSignal<boolean[]> = signal([]);
    readonly locationsAddresses: WritableSignal<string[]> = signal([]);
    readonly resetControls = signal<boolean>(false);
    readonly sharedInstagramPage = signal<string | null>(null);
    readonly isLoadingUpdateInstagramPage = signal<boolean>(false);

    readonly currentDisplayedSteps = computed(() =>
        this.searchSteps()
            .filter((searchStep) => searchStep.shouldBeDisplayed)
            .map((searchStep) => searchStep.step)
    );

    readonly categories = toSignal(
        this._categoryHttpService.getAllCategories$().pipe(
            map((res) =>
                res.data.sort((a, b) => a.getCategoryNameForLang(this.locale).localeCompare(b.getCategoryNameForLang(this.locale)))
            ),
            takeUntilDestroyed(this._destroyRef)
        ),
        { initialValue: [] }
    );
    readonly defaultCategory = computed(() => this.categories().find((c) => c.categoryId === DEFAULT_CATEGORY_ID) ?? this.categories()[0]);

    readonly shouldDisableSubmit = computed(() => {
        const getPlaceDiagnosticRateLimitPerDayExceeded = this.getPlaceDiagnosticRateLimitPerDayExceeded();
        const isThereAtLeastOneDiagnostic = this.diagnostics().some((diagnostic) => diagnostic !== null);
        const areAllInstagramPagesValidated = this.instagramPages().every(
            (page, index) => page === null || (page !== null && this.instagramPagesValidated()[index])
        );
        return getPlaceDiagnosticRateLimitPerDayExceeded || !isThereAtLeastOneDiagnostic || !areAllInstagramPagesValidated;
    });

    readonly placeIdsToFilter: WritableSignal<string[]> = signal([]);

    readonly formGroup: WritableSignal<FormGroup> = signal(
        this._formBuilder.group({
            locations: this._formBuilder.array([
                this._formBuilder.group({
                    category: this._formBuilder.control({ value: '', disabled: true }, { validators: [Validators.required] }),
                    instagram: this._formBuilder.control<string | null>({ value: null, disabled: true }),
                }),
            ]),
        })
    );

    get locations(): FormArray {
        return this.formGroup().get('locations') as FormArray;
    }

    /**
     * Rate limit the number of requests
     */
    readonly getPlaceDiagnosticRateLimitPerDayExceeded: WritableSignal<boolean> = signal(false);

    /**
     * Query params for hubspot form
     */
    protected _utmSource = '';
    protected _utmMedium = '';
    protected _utmCampaign = '';

    ngOnInit(): void {
        this._diagnosticHttpService
            .trackDiagnosticAction$({
                identity: getUserIdentityFromLocalStorage() ?? '',
                eventName: HeapEventName.MALOUPE_TRACKING_OPENING_LANDING_PAGE,
            })
            .subscribe();
        this.getPlaceDiagnosticRateLimitPerDayExceeded.set(this._isGetPlaceDiagnosticLimitExceeded());
        this._activatedRoute.queryParamMap.subscribe((params) => {
            this._utmSource = params.get('utm_source') ?? '';
            this._utmMedium = params.get('utm_medium') ?? '';
            this._utmCampaign = params.get('utm_campaign') ?? '';
        });
    }

    getCategoryControl(index: number): FormControl<string | null> {
        return this.locations.at(index)?.get('category') as FormControl<string | null>;
    }

    getInstagramControl(index: number): FormControl<string | null> {
        return this.locations.at(index)?.get('instagram') as FormControl<string | null>;
    }

    hasCategory(index: number) {
        return this.getCategoryControl(index)?.value !== '';
    }

    filteredCategories(index: number) {
        const categoryValue = this.getCategoryControl(index)?.value;
        return categoryValue ? this._filterCategories(categoryValue) : this.categories().slice();
    }

    getLocationAddress(index: number): string {
        return this.locationsAddresses()[index];
    }

    addLocation(): void {
        if (this.locations.controls.length >= this.MAX_LOCATION_COUNT) {
            return;
        }
        this.instagramPages.update((pages) => [...pages, null]);
        this.autocompleteErrorMessage.update((messages) => [...messages, '']);
        this.instagramPagesValidated.update((validated) => [...validated, false]);
        this.locationsAddresses.update((addresses) => [...addresses, '']);
        this.locations.push(
            this._formBuilder.group({
                category: this._formBuilder.control({ value: '', disabled: true }),
                instagram: this._formBuilder.control<string | null>({ value: null, disabled: true }),
            })
        );
    }

    removeLocation(index: number) {
        this.formGroup.update((formGroup) => {
            const locations = formGroup.get('locations') as FormArray;
            locations.removeAt(index);
            return this._formBuilder.group({
                locations,
            });
        });

        this.diagnostics.update((diagnostics) => {
            const newDiagnostics = [...diagnostics];
            newDiagnostics.splice(index, 1);
            return newDiagnostics;
        });
        this.placeIdsToFilter.set(this.diagnostics().map((d) => d!.placeId));
        this.instagramPages.update((pages) => {
            const newPages = [...pages];
            newPages.splice(index, 1);
            return newPages;
        });
        this.autocompleteErrorMessage.update((messages) => {
            const newMessages = [...messages];
            newMessages.splice(index, 1);
            return newMessages;
        });
        this.locationsAddresses.update((addresses) => {
            const newAddresses = [...addresses];
            newAddresses.splice(index, 1);
            return newAddresses;
        });
        this.instagramPagesValidated.update((validated) => {
            const newValidated = [...validated];
            newValidated.splice(index, 1);
            return newValidated;
        });
    }

    _removeAllLocations(): void {
        this.locations.disable();
        this.locations.reset();
        this.diagnostics.set([]);
        this.instagramPages.set([]);
        this.autocompleteErrorMessage.set([]);
        this.instagramPagesValidated.set([]);
        this.locationsAddresses.set([]);
        this.resetControls.set(true);
        this.locations.clear();
        setTimeout(() => {
            this.resetControls.set(false);
            this.addLocation();
        }, 100);
    }

    onActionButtonClick = (step: SearchStep, count: DataCount): void => {
        switch (step) {
            case SearchStep.LOCATION_COUNT:
                this._updateIsOneLocation(count);
                this._updateStepsToDisplay(SearchStep.INSTAGRAM_ACCOUNT_COUNT, true);
                if (this.currentDisplayedSteps().includes(SearchStep.LOCATION_SCAN)) {
                    this._updateStepsToDisplay(SearchStep.LOCATION_SCAN, false);
                    this.instagramAccountCount.set(null);
                    this._removeAllLocations();
                }
                break;
            case SearchStep.INSTAGRAM_ACCOUNT_COUNT:
                this._updateInstagramAccountCount(count);
                this._updateStepsToDisplay(SearchStep.LOCATION_SCAN, true);
                this._removeAllLocations();
                break;
            default:
                break;
        }
    };

    onLocationSelected(event: google.maps.places.PlaceResult, index: number): void {
        if (!event || !event.place_id || this.getPlaceDiagnosticRateLimitPerDayExceeded()) {
            return;
        }
        const placeId = event.place_id;
        this._diagnosticHttpService
            .createDiagnostic$(placeId, this.locale)
            .pipe(
                catchError((_error) => {
                    this._toastService.openErrorToast('Une erreur est survenue, veuillez réessayer plus tard.', ToastDuration.MEDIUM);
                    return [];
                }),
                switchMap((diagnostic) => {
                    const sharedInstagramPage = this.sharedInstagramPage();
                    if (sharedInstagramPage) {
                        return this._diagnosticHttpService
                            .updateWithInstagramPageInfo$(diagnostic.id, sharedInstagramPage)
                            .pipe(map(() => diagnostic));
                    }
                    return [diagnostic];
                })
            )
            .subscribe((diagnostic) => {
                this.diagnostics.update((diagnostics) => {
                    const newDiagnostic = new Diagnostic(diagnostic);
                    const existingDiagnostic = this.diagnostics()[index] ?? null;
                    if (existingDiagnostic) {
                        diagnostics.splice(index, 1, newDiagnostic);
                        return diagnostics;
                    }
                    return [...diagnostics, newDiagnostic];
                });
                this.placeIdsToFilter.set(this.diagnostics().map((d) => d!.placeId));
                const category = this._getCategoryFromDiagnostic(diagnostic);
                this.getCategoryControl(index)?.setValue(category.getCategoryNameForLang(this.locale));

                this.autocompleteErrorMessage.update((messages) => {
                    const newMessages = [...messages];
                    const existingMessage = newMessages[index] ?? null;
                    if (existingMessage !== null) {
                        newMessages.splice(index, 1, '');
                        return newMessages;
                    }
                    return [...newMessages, ''];
                });

                this.locationsAddresses.update((addresses) => {
                    const newAddresses = [...addresses];
                    const existingAddress = newAddresses[index] ?? null;
                    if (existingAddress !== null) {
                        newAddresses.splice(index, 1, event.formatted_address ?? '');
                        return newAddresses;
                    }
                    return [...newAddresses, event.formatted_address ?? ''];
                });

                this.getCategoryControl(index)?.enable();
                this.getInstagramControl(index)?.enable();
            });
    }

    onInputChanged(event: string, index: number): void {
        if (event === '') {
            this.getCategoryControl(index)?.disable();
            this.getCategoryControl(index)?.setValue('');
            this.getInstagramControl(index)?.disable();
            this.getInstagramControl(index)?.setValue(null);
            return;
        }
    }

    onLocationCategoryChanged(category: Category, index: number): void {
        const diagnostic = this.diagnostics()[index];
        if (diagnostic) {
            this._diagnosticHttpService.updateRestaurantCategory$(diagnostic.id, category.categoryId).subscribe({
                next: (diagnosticResult) => {
                    this.diagnostics.update((diagnostics) => {
                        const newDiagnostic = new Diagnostic(diagnosticResult);
                        diagnostics.splice(index, 1, newDiagnostic);
                        return diagnostics;
                    });
                },
                error: (error) => {
                    console.error(error);
                },
            });
        }
    }

    onSelectedInstagramPage(index: number): void {
        this.instagramPagesValidated.update((validated) => {
            const newValidated = [...validated];
            newValidated.splice(index, 1, true);
            return newValidated;
        });
    }

    onInstagramPageChanged(instagramPage: InstagramPageInfoDto | null, index: number): void {
        if (instagramPage === null) {
            this.instagramPagesValidated.update((validated) => {
                const newValidated = [...validated];
                newValidated.splice(index, 1, false);
                return newValidated;
            });
        } else {
            this.instagramPages.update((pages) => {
                const newPages = [...pages];
                newPages.splice(index, 1, instagramPage);
                return newPages;
            });
        }
    }

    onShouldUpdateInstagramPage(instagramAccountName: string): void {
        this.sharedInstagramPage.set(instagramAccountName);
        this.isLoadingUpdateInstagramPage.set(true);
        forkJoin(
            this.diagnostics().map((diagnostic) => {
                if (diagnostic) {
                    return this._diagnosticHttpService.updateWithInstagramPageInfo$(diagnostic.id, instagramAccountName);
                }
                return [];
            })
        ).subscribe({
            next: () => {
                this.isLoadingUpdateInstagramPage.set(false);
            },
            error: (error) => {
                console.error(error);
                this.isLoadingUpdateInstagramPage.set(false);
            },
        });
    }

    computeMyDiagnostic(): void {
        const diagnosticIds = this.diagnostics()
            .map((diagnostic) => diagnostic?.id)
            .filter((id) => id !== null) as string[];
        if (diagnosticIds.length === 0 || this._isGetPlaceDiagnosticLimitExceeded()) {
            return;
        }
        increaseRequestCount({ key: RequestLocalStorageKey.GET_PLACE_DIAGNOSTIC_REQUEST });
        this._router.navigate(['/compute-diagnostics', JSON.stringify(diagnosticIds)], {
            queryParams: {
                utm_source: this._utmSource,
                utm_medium: this._utmMedium,
                utm_campaign: this._utmCampaign,
            },
        });
    }

    private _filterCategories(value: string): Category[] {
        const filterValue = value.toLowerCase();
        return this.categories().filter((c) => c.getCategoryNameForLang(this.locale).toLowerCase().includes(filterValue));
    }

    private _getCategoryFromDiagnostic(diagnostic: DiagnosticDto): Category {
        const restaurantCategory = diagnostic.restaurant.category;
        const findCategory = this.categories().find((c) => c.categoryId === restaurantCategory.categoryId);
        return findCategory ?? this.defaultCategory();
    }

    protected _isGetPlaceDiagnosticLimitExceeded(): boolean {
        const key = RequestLocalStorageKey.GET_PLACE_DIAGNOSTIC_REQUEST;
        const newPlaceDiagnosticRequestDetails = buildNewRequestDetails({
            key,
            shouldIncreaseCount: false,
        });

        localStorage.setItem(key, JSON.stringify(newPlaceDiagnosticRequestDetails));

        if (newPlaceDiagnosticRequestDetails.requestCount < LIMITS_AND_TIME_BY_LOCAL_KEY[key].limit || this._authContext.isTokenValid()) {
            this.getPlaceDiagnosticRateLimitPerDayExceeded.set(false);
            return false;
        }
        this.getPlaceDiagnosticRateLimitPerDayExceeded.set(true);
        return true;
    }

    private _updateIsOneLocation(count: DataCount): void {
        if (count === DataCount.ONE) {
            this.isOneLocation.set(true);
            return;
        }
        this.isOneLocation.set(false);
    }

    private _updateInstagramAccountCount(count: DataCount): void {
        if (count === DataCount.NONE) {
            this.instagramAccountCount.set(DataCount.NONE);
            return;
        }
        if (this.isOneLocation()) {
            this.instagramAccountCount.set(DataCount.ONE);
            return;
        }
        this.instagramAccountCount.set(count);
    }

    private _updateStepsToDisplay(step: SearchStep, shouldBeDisplayed: boolean): void {
        this.searchSteps.update((steps) => {
            const newSteps = steps.map((s) => {
                if (s.step === step) {
                    return { ...s, shouldBeDisplayed };
                }
                return s;
            });
            return newSteps;
        });
        setTimeout(() => {
            this._scrollToStep(step);
        }, DELAY_TO_SCROLL_TO_SECTION);
    }

    private _scrollToStep(searchStep: SearchStep): void {
        const step = document.getElementById(searchStep);
        if (step) {
            step.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}
