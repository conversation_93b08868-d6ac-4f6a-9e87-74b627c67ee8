<div class="flex h-full flex-col items-center gap-16 px-36 py-20 md:gap-10 md:px-6 md:py-8">
    <div class="flex flex-col md:gap-4">
        <app-diagnostic-header [subtitle]="'maloupe.diagnostic_search.main.subtitle' | translate"></app-diagnostic-header>
        @for (step of searchSteps(); track $index) {
            @if (step.shouldBeDisplayed) {
                <ng-container
                    [ngTemplateOutlet]="searchStepTemplate"
                    [ngTemplateOutletContext]="{
                        index: $index + 1,
                        stepData: step,
                    }"></ng-container>
            }
        }
    </div>
</div>

<ng-template let-index="index" let-stepData="stepData" #searchStepTemplate>
    <div class="mt-10 flex h-full w-full flex-col items-center" [id]="stepData.step">
        <div class="just flex w-full items-baseline gap-3 md:items-center">
            <span class="malou-text-50--semibold text-malou-text-title">0{{ index }}.</span>
            @if (index === 3) {
                <div class="inline-block">
                    <span class="malou-text-18--semibold text-malou-text-title">
                        @if (isOneLocation()) {
                            {{ stepData.title | pluralTranslate: 1 }}
                        } @else {
                            {{ stepData.title | pluralTranslate: 5 }}
                        }
                    </span>
                    <span class="malou-text-15--regular italic">{{
                        'maloupe.diagnostic_search.main.steps.step_three.max_locations' | translate
                    }}</span>
                </div>
            } @else {
                <span class="malou-text-18--semibold text-malou-text-title">
                    {{ stepData.title | translate }}
                </span>
            }
        </div>
        <div class="ml-14 flex h-full w-full border-l border-dashed border-malou-white--dark">
            @if (stepData.step === SearchStep.LOCATION_SCAN) {
                <ng-container [ngTemplateOutlet]="locationScanTemplate"></ng-container>
            } @else {
                <ng-container
                    [ngTemplateOutlet]="buttonsTemplate"
                    [ngTemplateOutletContext]="{
                        searchStep: stepData.step,
                    }"></ng-container>
            }
        </div>
    </div>
</ng-template>

<ng-template let-searchStep="searchStep" #buttonsTemplate>
    @switch (searchStep) {
        @case (SearchStep.LOCATION_COUNT) {
            <div class="flex h-full w-full gap-5 p-10 md:flex-col md:p-4">
                <ng-container
                    [ngTemplateOutlet]="buttonTemplate"
                    [ngTemplateOutletContext]="{
                        title: 'maloupe.diagnostic_search.main.steps.step_one' | pluralTranslate: 1,
                        isSelected: isOneLocation(),
                        searchStep: searchStep,
                        count: DataCount.ONE,
                        onClick: onActionButtonClick,
                    }"></ng-container>
                <ng-container
                    [ngTemplateOutlet]="buttonTemplate"
                    [ngTemplateOutletContext]="{
                        title: 'maloupe.diagnostic_search.main.steps.step_one' | pluralTranslate: 2,
                        isSelected: !isOneLocation() && isOneLocation() !== null,
                        searchStep: searchStep,
                        count: DataCount.MANY,
                        onClick: onActionButtonClick,
                    }"></ng-container>
            </div>
        }
        @case (SearchStep.INSTAGRAM_ACCOUNT_COUNT) {
            <div class="flex h-full w-full gap-5 p-10 md:flex-col md:p-4">
                <ng-container
                    [ngTemplateOutlet]="buttonTemplate"
                    [ngTemplateOutletContext]="{
                        title: 'maloupe.diagnostic_search.main.steps.step_two' | pluralTranslate: 1,
                        isSelected: instagramAccountCount() === DataCount.ONE,
                        searchStep: searchStep,
                        count: DataCount.ONE,
                        onClick: onActionButtonClick,
                    }"></ng-container>
                @if (!isOneLocation()) {
                    <ng-container
                        [ngTemplateOutlet]="buttonTemplate"
                        [ngTemplateOutletContext]="{
                            title: 'maloupe.diagnostic_search.main.steps.step_two' | pluralTranslate: 2,
                            isSelected: instagramAccountCount() === DataCount.MANY,
                            searchStep: searchStep,
                            count: DataCount.MANY,
                            onClick: onActionButtonClick,
                        }"></ng-container>
                }

                <ng-container
                    [ngTemplateOutlet]="buttonTemplate"
                    [ngTemplateOutletContext]="{
                        title: 'maloupe.diagnostic_search.main.steps.step_two' | pluralTranslate: 0,
                        isSelected: instagramAccountCount() === DataCount.NONE,
                        searchStep: searchStep,
                        count: DataCount.NONE,
                        onClick: onActionButtonClick,
                    }"></ng-container>
            </div>
        }
    }
</ng-template>

<ng-template
    let-title="title"
    let-searchStep="searchStep"
    let-count="count"
    let-isSelected="isSelected"
    let-onClick="onClick"
    #buttonTemplate>
    <button
        class="w-fit sm:w-full"
        mat-flat-button
        [ngClass]="{
            'malou-btn-flat-search-form': !isSelected,
            'malou-btn-flat': isSelected,
        }"
        (click)="!isSelected ? onClick(searchStep, count) : $event.preventDefault()">
        {{ title }}
    </button>
</ng-template>

<ng-template #locationScanTemplate>
    <form class="h-full w-full" [formGroup]="formGroup()">
        <div class="flex flex-col py-10 pl-[58px] md:pl-[20px]" formArrayName="locations">
            @for (location of locations.controls; track $index) {
                @let locationIndex = $index;
                <div
                    class="mb-8 flex w-full items-center justify-between"
                    [ngClass]="{ 'mb-0': locationIndex === locations.controls.length - 1 }">
                    <div class="flex w-[90%] flex-col items-start" [formGroupName]="locationIndex">
                        <div class="flex w-full justify-center gap-3 md:flex-col">
                            <div class="max-w-2/3 w-full">
                                <div class="malou-text-14--bold md:malou-text-16--bold mb-2 text-malou-text">
                                    {{ 'maloupe.diagnostic_search.main.search_bar.title' | translate }}*
                                </div>
                                <app-input-google-maps-autocomplete
                                    [placeholder]="'maloupe.diagnostic_search.common.search_bar.placeholder' | translate"
                                    [errorMessage]="autocompleteErrorMessage()[locationIndex]"
                                    [getPlaceDiagnosticRateLimitPerDayExceeded]="getPlaceDiagnosticRateLimitPerDayExceeded()"
                                    [reset]="resetControls()"
                                    [value]="getLocationAddress(locationIndex)"
                                    [placeIdsToFilter]="placeIdsToFilter()"
                                    (locationSelected)="onLocationSelected($event, locationIndex)"
                                    (inputGoogleMapsAutocompleteChange)="
                                        onInputChanged($event, locationIndex)
                                    "></app-input-google-maps-autocomplete>
                            </div>
                            <div>
                                <div
                                    class="malou-text-14--bold md:malou-text-16--bold mb-2 text-malou-text"
                                    [class.opacity-50]="getCategoryControl(locationIndex).disabled">
                                    {{ 'maloupe.diagnostic_search.main.category.title' | translate }}*
                                </div>
                                <mat-form-field class="malou-filled-form-field w-full">
                                    <input
                                        matInput
                                        formControlName="category"
                                        aria-label="Catégorie"
                                        [placeholder]="'maloupe.diagnostic_search.common.category.placeholder' | translate"
                                        [class.italic]="hasCategory(locationIndex)"
                                        [class.malou-text-12--medium]="hasCategory(locationIndex)"
                                        [matAutocomplete]="auto" />
                                    <div class="flex items-center pr-5" matSuffix>
                                        <mat-icon class="!h-4 !w-4 text-malou-primary" [svgIcon]="SvgIcon.CHEVRON_DOWN"></mat-icon>
                                    </div>
                                    <mat-autocomplete #auto="matAutocomplete">
                                        @for (category of filteredCategories(locationIndex); track category) {
                                            <mat-option
                                                [value]="category.getCategoryNameForLang(locale)"
                                                (onSelectionChange)="onLocationCategoryChanged(category, locationIndex)">
                                                <span>{{ category.getCategoryNameForLang(locale) }}</span>
                                            </mat-option>
                                        }
                                    </mat-autocomplete>
                                </mat-form-field>
                            </div>
                        </div>

                        @if (instagramAccountCount() === DataCount.MANY) {
                            <div class="w-full">
                                <div class="malou-text-14--bold mb-2 text-malou-text">
                                    {{ 'maloupe.diagnostic_search.main.instagram.title' | translate }}
                                </div>
                                <app-input-instagram-search
                                    [diagnostic]="diagnostics()[locationIndex]"
                                    [instagramControl]="getInstagramControl(locationIndex)"
                                    [instagramPage]="instagramPages()[locationIndex]"
                                    [instagramPageValidated]="instagramPagesValidated()[locationIndex]"
                                    [reset]="resetControls()"
                                    (instagramPageSelection)="onSelectedInstagramPage(locationIndex)"
                                    (instagramPageChange)="onInstagramPageChanged($event, locationIndex)"></app-input-instagram-search>
                            </div>
                        }
                    </div>

                    @if (locationIndex !== 0) {
                        <div class="w-[5%]">
                            <button
                                class="flex !h-8 !w-8 items-center justify-center rounded-full !bg-malou-pink--light/30"
                                (click)="removeLocation(locationIndex)">
                                <mat-icon class="!h-4 text-malou-pink" [svgIcon]="SvgIcon.TRASH"></mat-icon>
                            </button>
                        </div>
                    }
                </div>
            }

            @if (!isOneLocation()) {
                <div
                    class="mb-8 flex items-center gap-1"
                    [ngClass]="{
                        'cursor-pointer': locations.controls.length < MAX_LOCATION_COUNT,
                        'cursor-not-allowed opacity-50': locations.controls.length >= MAX_LOCATION_COUNT,
                    }"
                    (click)="addLocation()">
                    <mat-icon class="!h-4 text-malou-primary" [svgIcon]="SvgIcon.ADD"></mat-icon>
                    <span class="malou-text-10--semibold text-malou-primary">{{
                        'maloupe.diagnostic_search.main.add_location' | translate
                    }}</span>
                </div>
            }

            @if (instagramAccountCount() === DataCount.ONE && getInstagramControl(0)) {
                <div class="w-[90%]">
                    <div class="malou-text-14--bold mb-2 text-malou-text">
                        {{ 'maloupe.diagnostic_search.main.instagram.title' | translate }}
                    </div>
                    <app-input-instagram-search
                        [diagnostic]="diagnostics()[0]"
                        [instagramControl]="getInstagramControl(0)"
                        [instagramPage]="instagramPages()[0]"
                        [instagramPageValidated]="instagramPagesValidated()[0]"
                        (instagramPageSelection)="onSelectedInstagramPage(0)"
                        (onShouldUpdateInstagramPage)="onShouldUpdateInstagramPage($event)"
                        (instagramPageChange)="onInstagramPageChanged($event, 0)"></app-input-instagram-search>
                </div>
            }
        </div>

        <div class="flex justify-center md:w-[1/2]">
            <button
                class="malou-btn-flat md:w-[1/2]"
                mat-flat-button
                [disabled]="shouldDisableSubmit() || !locations.valid || isLoadingUpdateInstagramPage()"
                (click)="computeMyDiagnostic()">
                {{ 'maloupe.diagnostic_search.common.submit_button' | translate }}
            </button>
        </div>
    </form>
</ng-template>
