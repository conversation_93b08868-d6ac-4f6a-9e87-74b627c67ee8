import { Component, inject, signal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { MaloupeLocale } from '@malou-io/package-utils';

import { LogoPathResolverPipe } from ':shared/pipes';

@Component({
    selector: 'app-restaurant-logo-footer',
    imports: [TranslateModule, LogoPathResolverPipe],
    templateUrl: './restaurant-logo-footer.component.html',
    styleUrl: './restaurant-logo-footer.component.scss',
})
export class RestaurantLogoFooterComponent {
    private readonly _translateService = inject(TranslateService);

    readonly isEnglish = signal<boolean>(false);

    constructor() {
        this.isEnglish.set(this._translateService.currentLang === MaloupeLocale.EN);
    }
}
