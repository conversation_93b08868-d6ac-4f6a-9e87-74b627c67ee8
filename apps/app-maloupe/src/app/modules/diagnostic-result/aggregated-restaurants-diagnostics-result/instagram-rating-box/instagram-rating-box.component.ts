import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { DiagnosticRating } from '@malou-io/package-utils';

import { RatingChipComponent } from ':modules/rating-chip/rating-chip.component';
import { Image } from ':shared/enums';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-instagram-rating-box',
    standalone: true,
    imports: [NgClass, RatingChipComponent, ImagePathResolverPipe],
    templateUrl: './instagram-rating-box.component.html',
    styleUrl: './instagram-rating-box.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InstagramRatingBoxComponent {
    isDiagnosticRestaurant = input.required<boolean>();
    value = input.required<string | number>();
    description = input.required<string>();
    diagnosticRating = input<DiagnosticRating>();

    DiagnosticRating = DiagnosticRating;
    Image = Image;
}
