<div
    class="flex h-28 rounded-xl border border-malou-primary md:h-32"
    [ngClass]="{
        'bg-malou-pink/5': diagnosticRating() === DiagnosticRating.BAD,
        'border-0': diagnosticRating() === DiagnosticRating.BAD,
    }">
    @if (!isDiagnosticRestaurant()) {
        <div
            class="w-50px malou-text-40 flex items-center rounded-bl-xl rounded-tl-xl border border-malou-primary bg-malou-dark px-2 md:hidden">
            <img class="w-[36px]" [alt]="Image.STARS" [src]="Image.STARS | imagePathResolver" />
        </div>
    }
    <div class="flex w-full justify-between p-4 md:flex-col-reverse md:gap-2">
        <div class="flex h-full flex-col justify-end">
            <span class="malou-text-40--bold leading-10">{{ value() }}</span>
            <span class="malou-text-12--medium text-malou-text">{{ description() }}</span>
        </div>
        @if (isDiagnosticRestaurant()) {
            <div class="flex justify-end md:justify-start">
                <app-rating-chip [diagnosticRating]="diagnosticRating() ?? DiagnosticRating.AVERAGE"></app-rating-chip>
            </div>
        }
    </div>
</div>
