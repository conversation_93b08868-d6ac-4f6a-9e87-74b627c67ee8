<div class="relative mb-10 sm:mb-0">
    <mat-table
        class="malou-mat-table md:hidden"
        matSort
        matSortDisableClear
        [matSortActive]="defaultSort.active"
        [matSortDirection]="defaultSort.direction"
        [trackBy]="trackByIdFn"
        [dataSource]="dataSource()"
        #table="matTable">
        <ng-container [matColumnDef]="TableFieldName.RESTAURANT_NAME">
            <mat-header-cell *matHeaderCellDef class="flex-2" mat-sort-header>
                {{ 'common.location' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="flex-2">
                <div class="flex flex-col">
                    <span class="malou-text-13--semibold">{{ row.restaurantName }}</span>
                    <span class="malou-text-10--regular italic text-malou-text">{{ row.restaurantAddress }}</span>
                </div>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.RESTAURANT_RATING">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.competitors.columns.rating' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                <div
                    [ngClass]="{
                        'malou-chip--error': row.rating < row.averageRatingForSimilarRestaurants,
                    }">
                    {{ row.rating }}
                </div>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.RESTAURANT_REVIEWS">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.competitors.columns.reviews' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                <div
                    [ngClass]="{
                        'malou-chip--error': row.reviewCount < row.averageReviewCountForSimilarRestaurants,
                    }">
                    {{ row.reviewCount }}
                </div>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.COMPETITORS_RATING">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.competitors.columns.rating' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                {{ row.averageRatingForSimilarRestaurants }}
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.COMPETITORS_REVIEWS">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.competitors.columns.reviews' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                {{ row.averageReviewCountForSimilarRestaurants }}
            </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns; let i = index"> </mat-row>
    </mat-table>
    <ng-container [ngTemplateOutlet]="seoListBannerTemplate" [ngTemplateOutletContext]="{ isRestaurant: true }"></ng-container>
    <ng-container [ngTemplateOutlet]="seoListBannerTemplate" [ngTemplateOutletContext]="{ isRestaurant: false }"></ng-container>
</div>

<div class="hidden flex-col gap-y-2 md:mt-3 md:flex">
    @for (restaurantSeoData of seoData(); track $index) {
        <div class="rounded-[10px] border border-malou-primary p-4">
            <div class="flex flex-col sm:gap-y-1">
                <div class="malou-text-13--bold">{{ restaurantSeoData.restaurantName | sentenceCase }}</div>
                <span class="malou-text-10--regular mb-4 italic text-malou-text">{{ restaurantSeoData.restaurantAddress }}</span>
                <div class="mt-2 flex items-center gap-5 px-5">
                    <ng-container
                        [ngTemplateOutlet]="seoListDataTemplate"
                        [ngTemplateOutletContext]="{
                            reviewCount: restaurantSeoData.reviewCount,
                            rating: restaurantSeoData.rating,
                            averageRatingForSimilarRestaurants: restaurantSeoData.averageRatingForSimilarRestaurants,
                            averageReviewCountForSimilarRestaurants: restaurantSeoData.averageReviewCountForSimilarRestaurants,
                            isRestaurant: true,
                        }"></ng-container>
                </div>
            </div>
        </div>
    }
</div>

<ng-template
    let-reviewCount="reviewCount"
    let-rating="rating"
    let-averageRatingForSimilarRestaurants="averageRatingForSimilarRestaurants"
    let-averageReviewCountForSimilarRestaurants="averageReviewCountForSimilarRestaurants"
    #seoListDataTemplate>
    <div class="flex flex-col gap-2">
        <div class="grid grid-cols-4 grid-rows-3 items-center gap-x-2 gap-y-1">
            <span class="malou-text-12--bold text-malou-text-title">{{
                'maloupe.aggregated_diagnostic_result.competitors.you' | translate
            }}</span>
            <div></div>
            <span class="malou-text-12--bold text-malou-text-title">{{
                'maloupe.aggregated_diagnostic_result.competitors.title' | translate
            }}</span>
            <div></div>
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.competitors.columns.rating' | translate
            }}</span>
            <div
                [ngClass]="{
                    'malou-chip--error max-w-fit': rating < averageRatingForSimilarRestaurants,
                    'malou-text-13 text-malou-text-title': rating >= averageRatingForSimilarRestaurants,
                }">
                {{ rating }}
            </div>
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.competitors.columns.rating' | translate
            }}</span>
            <div class="malou-text-13 text-malou-text-title">
                {{ averageRatingForSimilarRestaurants }}
            </div>
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.competitors.columns.reviews' | translate
            }}</span>
            <div
                class="malou-text-13 text-malou-text-title"
                [ngClass]="{
                    'malou-chip--error max-w-fit': reviewCount < averageReviewCountForSimilarRestaurants,
                    'malou-text-13 text-malou-text-title': reviewCount >= averageReviewCountForSimilarRestaurants,
                }">
                {{ reviewCount }}
            </div>
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.competitors.columns.reviews' | translate
            }}</span>
            <div class="malou-text-13 text-malou-text-title">
                {{ averageReviewCountForSimilarRestaurants }}
            </div>
        </div>
    </div>
</ng-template>

<ng-template let-isRestaurant="isRestaurant" #seoListBannerTemplate>
    <div
        class="pointer-events-none absolute flex w-[30%] items-start justify-center rounded border border-malou-primary md:hidden"
        [ngStyle]="{
            height: getBannerHeight() + 'px',
        }"
        [ngClass]="{
            'left-[34.5%] top-[-60.1px]': isRestaurant,
            'right-[4%] top-[-60px]': !isRestaurant,
        }">
        <span class="malou-text-13--bold w-full bg-malou-light px-8 py-3 text-center text-malou-text-title">
            {{
                isRestaurant
                    ? ('maloupe.aggregated_diagnostic_result.competitors.your_locations' | translate)
                    : ('maloupe.aggregated_diagnostic_result.competitors.competitors_average' | translate)
            }}
        </span>
    </div>
</ng-template>
