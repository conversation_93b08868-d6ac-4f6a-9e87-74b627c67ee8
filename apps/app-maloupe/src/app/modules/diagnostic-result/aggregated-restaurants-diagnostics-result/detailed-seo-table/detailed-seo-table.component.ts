import { Ng<PERSON><PERSON>, NgStyle, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, ViewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';

import { DiagnosticRating } from '@malou-io/package-utils';

import { TableSortDirection } from ':shared/enums';
import { TrackByFunctionFactory } from ':shared/helpers';
import { DetailedSeoSectionData } from ':shared/interfaces';
import { SentenceCasePipe } from ':shared/pipes/sentence-case.pipe';

enum DetailedSeoTableFieldName {
    RESTAURANT_NAME = 'restaurantName',
    RESTAURANT_RATING = 'restaurantRating',
    RESTAURANT_REVIEWS = 'restaurantReviews',
    COMPETITORS_RATING = 'competitorsRating',
    COMPETITORS_REVIEWS = 'competitorsReviews',
}

@Component({
    selector: 'app-detailed-seo-table',
    imports: [MatIconModule, MatTableModule, TranslateModule, NgTemplateOutlet, MatSortModule, SentenceCasePipe, NgClass, NgStyle],
    templateUrl: './detailed-seo-table.component.html',
    styleUrl: './detailed-seo-table.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailedSeoTableComponent {
    readonly seoData = input<DetailedSeoSectionData[] | null>(null);

    readonly DiagnosticRating = DiagnosticRating;
    readonly TableFieldName = DetailedSeoTableFieldName;
    readonly displayedColumns: string[] = Object.values(DetailedSeoTableFieldName);

    readonly defaultSort = { active: DetailedSeoTableFieldName.RESTAURANT_RATING, direction: TableSortDirection.Desc };
    readonly dataSource = computed(() => new MatTableDataSource<DetailedSeoSectionData>(this.seoData() ?? []));

    @ViewChild(MatSort) set matSort(sort: MatSort) {
        const dataSource = this.dataSource();
        if (dataSource) {
            dataSource.sortingDataAccessor = (item, property): string | number => {
                switch (property) {
                    case DetailedSeoTableFieldName.RESTAURANT_NAME:
                        return item.restaurantName;
                    case DetailedSeoTableFieldName.RESTAURANT_RATING:
                        return item.rating ?? item.restaurantName;
                    case DetailedSeoTableFieldName.RESTAURANT_REVIEWS:
                        return item.reviewCount;
                    case DetailedSeoTableFieldName.COMPETITORS_RATING:
                        return item.averageRatingForSimilarRestaurants ?? 0;
                    case DetailedSeoTableFieldName.COMPETITORS_REVIEWS:
                        return item.averageReviewCountForSimilarRestaurants ?? 0;
                    default:
                        return '';
                }
            };
            dataSource.sort = sort;
        }
    }

    readonly getBannerHeight = computed(() => {
        const dataLength = this.seoData()?.length ?? 0;
        const bannerOffset = 90;
        const rowGap = 5;
        const rowHeight = 49;
        const endOffset = 25;
        return bannerOffset + (rowHeight + rowGap) * dataLength + endOffset;
    });

    readonly trackByIdFn = TrackByFunctionFactory.get('diagnosticId');
}
