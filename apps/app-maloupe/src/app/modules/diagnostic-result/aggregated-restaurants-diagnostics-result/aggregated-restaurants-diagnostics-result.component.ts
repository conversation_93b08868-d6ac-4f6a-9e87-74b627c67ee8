import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, Signal, signal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import groupBy from 'lodash.groupby';

import { DiagnosticRating, isNotNil, MaloupeLocale, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { ToastService } from ':core/services/toast.service';
import { AggregatedDiagnosticsScoreComponent } from ':modules/aggregated-diagnostics-score/aggregated-diagnostics-score.component';
import { DetailedInformationTableComponent } from ':modules/diagnostic-result/aggregated-restaurants-diagnostics-result/detailed-information-table/detailed-information-table.component';
import { DetailedKeywordsTableComponent } from ':modules/diagnostic-result/aggregated-restaurants-diagnostics-result/detailed-keywords-table/detailed-keywords-table.component';
import { DetailedSeoTableComponent } from ':modules/diagnostic-result/aggregated-restaurants-diagnostics-result/detailed-seo-table/detailed-seo-table.component';
import { DetailedSocialMediaTableComponent } from ':modules/diagnostic-result/aggregated-restaurants-diagnostics-result/detailed-social-media-table/detailed-social-media-table.component';
import { DiagnosticScoreComponent } from ':modules/diagnostic-score/diagnostic-score.component';
import { RatingChipComponent } from ':modules/rating-chip/rating-chip.component';
import { InstagramDiagnosticResultComponent } from ':shared/components/instagram-diagnostic-result/instagram-diagnostic-result.component';
import {
    EXPERT_DIAGNOSTIC_QUICK_CALL,
    EXPERT_DIAGNOSTIC_REDIRECT_URL,
    EXPERT_MEET_DIAGNOSTIC_REDIRECT_URL,
    MINIMUM_POST_COUNT_RECOMMENDATION,
    REVIEW_CALCULATOR_REDIRECT_FR_URL,
} from ':shared/constants';
import { Icon, MaloupePlatformKey } from ':shared/enums';
import { SvgIcon } from ':shared/enums/svg-icon.enum';
import { AggregatedDiagnostics, Diagnostic } from ':shared/models';
import {
    Illustration,
    IllustrationPathResolverPipe,
    ImagePathResolverPipe,
    LogoPathResolverPipe,
    PluralTranslatePipe,
    SentenceCasePipe,
} from ':shared/pipes';

import { DetailedSemanticAnalysisChartComponent } from './detailed-semantic-analysis-chart/detailed-semantic-analysis-chart.component';

@Component({
    selector: 'app-aggregated-restaurant-diagnostic-result',
    imports: [
        NgClass,
        NgTemplateOutlet,
        DiagnosticScoreComponent,
        RatingChipComponent,
        MatIconModule,
        MatTableModule,
        TranslateModule,
        IllustrationPathResolverPipe,
        ImagePathResolverPipe,
        ImagePathResolverPipe,
        LogoPathResolverPipe,
        PluralTranslatePipe,
        SentenceCasePipe,
        AggregatedDiagnosticsScoreComponent,
        DetailedInformationTableComponent,
        DetailedKeywordsTableComponent,
        DetailedSeoTableComponent,
        DetailedSocialMediaTableComponent,
        InstagramDiagnosticResultComponent,
        DetailedSemanticAnalysisChartComponent,
    ],
    templateUrl: './aggregated-restaurants-diagnostics-result.component.html',
    styleUrl: './aggregated-restaurants-diagnostics-result.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AggregatedRestaurantsDiagnosticsResultComponent {
    readonly aggregatedDiagnostics = input<AggregatedDiagnostics | null>(null);
    readonly receiverEmail = input<string | null>(null);
    readonly isMalouUser = input<boolean>(false);
    readonly publicDiagnosticUrl = input<string | null>(null);

    private readonly _translateService = inject(TranslateService);
    private readonly _toastService = inject(ToastService);

    readonly SvgIcon = SvgIcon;
    readonly PlatformKey = MaloupePlatformKey;
    readonly Illustration = Illustration;
    readonly DiagnosticRating = DiagnosticRating;
    readonly Icon = Icon;
    readonly MINIMUM_POST_COUNT_RECOMMENDATION = MINIMUM_POST_COUNT_RECOMMENDATION;
    readonly MAXIMUM_GOOGLE_RATING = 5;
    readonly EXPERT_DIAGNOSTIC_QUICK_CALL = EXPERT_DIAGNOSTIC_QUICK_CALL;

    readonly isEnglish = signal<boolean>(false);
    readonly isLinkCopied = signal<boolean>(false);

    readonly locale: MaloupeLocale = this._translateService.currentLang as MaloupeLocale;
    readonly diagnosticDate = computed(() => {
        const createdAt = this.aggregatedDiagnostics()?.diagnostics?.[0].createdAt;
        if (!createdAt) {
            return '';
        }
        return new Date(createdAt).toLocaleString(this.locale, { month: 'long', year: 'numeric' });
    });
    readonly diagnostics: Signal<Diagnostic[]> = computed(() => this.aggregatedDiagnostics()?.diagnostics ?? []);

    // ------------------ Inconsistencies ------------------
    readonly aggregatedInconsistencies = computed(() => this.aggregatedDiagnostics()?.getAggregatedInconsistencies());
    readonly inconsistenciesRating = computed(() => this.aggregatedDiagnostics()?.getInconsistencyRating() ?? DiagnosticRating.BAD);
    readonly inconsistenciesPlatformKeysToDisplay = computed(() => {
        const inconsistencies = this.aggregatedInconsistencies();
        if (!inconsistencies || !inconsistencies.platformKeys) {
            return [];
        }
        const platformsToDisplay: string[] = [
            MaloupePlatformKey.FACEBOOK,
            MaloupePlatformKey.TRIPADVISOR,
            MaloupePlatformKey.FOURSQUARE,
            MaloupePlatformKey.BING,
        ];
        const platformKeys = inconsistencies.platformKeys.map((key) => key.toLowerCase());
        const foundPlatforms = platformsToDisplay.filter((key) => platformKeys.includes(key));
        return [MaloupePlatformKey.GMB, MaloupePlatformKey.APPLE, ...foundPlatforms];
    });
    readonly inconsistenciesOtherPlatformCount = computed(() => {
        const inconsistencies = this.aggregatedInconsistencies();
        const displayedPlatforms = this.inconsistenciesPlatformKeysToDisplay();
        if (!inconsistencies || inconsistencies.platformCount < displayedPlatforms.length) {
            return 0;
        }
        return inconsistencies.platformCount - displayedPlatforms.length;
    });

    readonly inconsistenciesDataPerRestaurant = computed(() => this.aggregatedDiagnostics()?.getInconsistenciesDataPerRestaurant());

    // ------------------   Keywords   ------------------
    readonly keywordsDiagnosticRating = computed(() => this.aggregatedDiagnostics()?.getKeywordsRating() ?? DiagnosticRating.BAD);
    readonly keywordsDataPerRestaurant = computed(() => this.aggregatedDiagnostics()?.getKeywordsDataPerRestaurant(this.locale));

    // ------------------     Seo      ------------------
    readonly seoAggregatedGoogleRating = computed(() => this.aggregatedDiagnostics()?.getSeoAggregatedGoogleRating());
    readonly seoAggregatedRestaurantsWithHigherRatingCount = computed(
        () => this.aggregatedDiagnostics()?.getRestaurantsWithHigherRatingCount() ?? 0
    );
    readonly seoRatingsDataPerRestaurant = computed(() => this.aggregatedDiagnostics()?.getSeoRatingsDataPerRestaurant());

    // ------------------   Reviews   ------------------
    readonly aggregatedSemanticAnalysisData = computed(() => this.aggregatedDiagnostics()?.getAggregatedSemanticAnalysisData());
    readonly reviewsWithTextPerRestaurant = computed(() => this.aggregatedDiagnostics()?.getReviewsPerRestaurant() ?? []);
    readonly reviewCount = computed(() => Math.max(...this.reviewsWithTextPerRestaurant().map((reviews) => reviews?.length ?? 0)));
    readonly atLeastOneRestaurantHasReviewsWithText = computed(() => this.reviewsWithTextPerRestaurant().length > 0);
    readonly atLeastOneRestaurantHasEnoughTags = computed(() => {
        const reviewsWithTextPerRestaurant = this.reviewsWithTextPerRestaurant();
        const flatReviewsWithTextPerRestaurant = reviewsWithTextPerRestaurant.flatMap((reviews) => reviews).filter(isNotNil);
        const reviewSegments = flatReviewsWithTextPerRestaurant
            .flatMap((review) => review?.segmentAnalyses)
            .filter((segment) => {
                const isNeutral = segment.sentiment === ReviewAnalysisSentiment.NEUTRAL;
                const isOverallExperience = segment.tag === ReviewAnalysisTag.OVERALL_EXPERIENCE;
                return !isNeutral && !isOverallExperience;
            });
        if (reviewSegments.length === 0) {
            return false;
        }
        return reviewSegments.some((restaurantReviewsWithText) => Object.keys(groupBy(restaurantReviewsWithText, 'tag')).length >= 0);
    });
    readonly shouldDisplayChart = computed(() => {
        if (this.atLeastOneRestaurantHasReviewsWithText()) {
            return this.atLeastOneRestaurantHasEnoughTags();
        }
        return true;
    });
    readonly semanticAnalysisOverview = computed(() => {
        const lang = this.locale;
        return this.aggregatedDiagnostics()?.getRandomSemanticAnalysisOverviewByLang(lang);
    });

    // ------------------   Instagram   ------------------
    readonly socialMediaDataPerRestaurant = computed(() => this.aggregatedDiagnostics()?.getSocialMediaDataPerRestaurant() ?? []);
    readonly atLeastOneRestaurantHasInstagramPage = computed(() => this.socialMediaDataPerRestaurant().length > 0);
    readonly isSameInstagramAccountForAllRestaurants = computed(
        () => this.aggregatedDiagnostics()?.isSameInstagramAccountForAllRestaurants() ?? false
    );
    readonly sharedInstagramAccountData = computed(() => {
        if (!this.isSameInstagramAccountForAllRestaurants()) {
            return null;
        }
        return this.aggregatedDiagnostics()?.getSharedInstagramAccountData();
    });
    readonly doesAtLeastOneOfYourRestaurantsInstagramAccountBetterThanCompetitors = computed(
        () => this.socialMediaDataPerRestaurant().filter((data) => data.isPerformingBetterThanCompetitors).length > 0
    );

    constructor() {
        this.isEnglish.set(this._translateService.currentLang === MaloupeLocale.EN);
    }

    talkToAnExpert(): void {
        window.open(EXPERT_DIAGNOSTIC_REDIRECT_URL, '_blank');
    }

    redirectToReviewCalculator(): void {
        // Temporary solution to redirect to the quick call with an expert until the english review calculator is ready
        const url = this.locale === MaloupeLocale.EN ? EXPERT_MEET_DIAGNOSTIC_REDIRECT_URL : REVIEW_CALCULATOR_REDIRECT_FR_URL;
        window.open(url, '_blank');
    }

    copyLink(): void {
        const publicDiagnosticUrl = this.publicDiagnosticUrl();
        if (publicDiagnosticUrl) {
            navigator.clipboard.writeText(publicDiagnosticUrl).then(() => {
                this.isLinkCopied.set(true);
                this._toastService.openSuccessToast(this._translateService.instant('common.link_copied'));
            });
        }
    }
}
