<div class="relative mb-10 sm:mb-0">
    <mat-table
        class="malou-mat-table md:hidden"
        matSort
        matSortDisableClear
        [matSortActive]="defaultSort.active"
        [matSortDirection]="defaultSort.direction"
        [trackBy]="trackByIdFn"
        [dataSource]="dataSource()"
        #table="matTable">
        <ng-container [matColumnDef]="TableFieldName.RESTAURANT_NAME">
            <mat-header-cell *matHeaderCellDef class="flex-2" mat-sort-header>
                {{ 'common.location' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="flex-2">
                <div class="flex flex-col">
                    <span class="malou-text-13--semibold">{{ row.restaurantName }}</span>
                    <span class="malou-text-10--regular italic text-malou-text">{{ row.restaurantAddress }}</span>
                </div>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.FOLLOWER_COUNT">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.instagram.columns.followers' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                <div
                    [ngClass]="{
                        'malou-chip--error': row.followerCount < row.averageCompetitorFollowerCount && row.followerCount !== 0,
                    }">
                    {{ formatNumber | applyPure: row.followerCount }}
                </div>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.AVERAGE_LIKES">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.instagram.columns.average_likes' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                <div
                    [ngClass]="{
                        'malou-chip--error': row.averageLikeCount < row.averageCompetitorAverageLikes && row.averageLikeCount !== 0,
                    }">
                    {{ formatNumber | applyPure: row.averageLikeCount }}
                </div>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.POST_COUNT">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.instagram.columns.post_count' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                <div
                    [ngClass]="{
                        'malou-chip--error': row.postCount < row.averageCompetitorPostCount && row.postCount !== 0,
                    }">
                    {{ formatNumber | applyPure: row.postCount }}
                </div>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.COMPETITOR_FOLLOWER_COUNT">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.instagram.columns.followers' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                {{ row.averageCompetitorFollowerCount }}
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.COMPETITOR_AVERAGE_LIKES">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.instagram.columns.average_likes' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                {{ row.averageCompetitorAverageLikes }}
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableFieldName.COMPETITOR_POST_COUNT">
            <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
                {{ 'maloupe.aggregated_diagnostic_result.instagram.columns.post_count' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let row" class="justify-center">
                {{ row.averageCompetitorPostCount }}
            </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns; let i = index"> </mat-row>
    </mat-table>
    <ng-component [ngTemplateOutlet]="socialMediaListBannerTemplate" [ngTemplateOutletContext]="{ isRestaurant: true }"></ng-component>
    <ng-component [ngTemplateOutlet]="socialMediaListBannerTemplate" [ngTemplateOutletContext]="{ isRestaurant: false }"></ng-component>
</div>

<div class="hidden flex-col gap-y-2 md:mt-3 md:flex">
    @for (restaurantSocialMediaData of socialMediaData(); track $index) {
        <div class="rounded-[10px] border border-malou-primary p-4">
            <div class="flex flex-col sm:gap-y-1">
                <div class="malou-text-13--bold">{{ restaurantSocialMediaData.restaurantName | sentenceCase }}</div>
                <span class="malou-text-10--regular mb-4 italic text-malou-text">{{ restaurantSocialMediaData.restaurantAddress }}</span>
                <div class="mt-2 flex items-center">
                    <ng-component
                        [ngTemplateOutlet]="socialMediaListDataTemplate"
                        [ngTemplateOutletContext]="{
                            followers: restaurantSocialMediaData.followerCount,
                            likeCount: restaurantSocialMediaData.averageLikeCount,
                            postCount: restaurantSocialMediaData.postCount,
                            isFollowerLowerThanCompetitor:
                                restaurantSocialMediaData.followerCount < restaurantSocialMediaData.averageCompetitorFollowerCount,
                            isLikeCountLowerThanCompetitor:
                                restaurantSocialMediaData.averageLikeCount < restaurantSocialMediaData.averageCompetitorAverageLikes,
                            isPostCountLowerThanCompetitor:
                                restaurantSocialMediaData.postCount < restaurantSocialMediaData.averageCompetitorPostCount,
                            isRestaurant: true,
                        }"></ng-component>
                    <ng-component
                        [ngTemplateOutlet]="socialMediaListDataTemplate"
                        [ngTemplateOutletContext]="{
                            followers: restaurantSocialMediaData.averageCompetitorFollowerCount,
                            likeCount: restaurantSocialMediaData.averageCompetitorAverageLikes,
                            postCount: restaurantSocialMediaData.averageCompetitorPostCount,
                            isRestaurant: false,
                        }"></ng-component>
                </div>
            </div>
        </div>
    }
</div>

<ng-template
    let-likeCount="likeCount"
    let-followers="followers"
    let-postCount="postCount"
    let-isRestaurant="isRestaurant"
    let-isFollowerLowerThanCompetitor="isFollowerLowerThanCompetitor"
    let-isLikeCountLowerThanCompetitor="isLikeCountLowerThanCompetitor"
    let-isPostCountLowerThanCompetitor="isPostCountLowerThanCompetitor"
    #socialMediaListDataTemplate>
    <div class="flex flex-col gap-2">
        <span class="malou-text-12--bold text-malou-text-title">{{
            isRestaurant
                ? ('maloupe.aggregated_diagnostic_result.competitors.you' | translate)
                : ('maloupe.aggregated_diagnostic_result.competitors.title' | translate)
        }}</span>
        <div class="grid grid-cols-2 grid-rows-3 items-center gap-x-2 gap-y-1">
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.instagram.columns.followers' | translate
            }}</span>
            <span
                class="malou-text-13 text-malou-text-title"
                [ngClass]="{
                    'malou-chip--error max-w-fit !text-malou-pink': isFollowerLowerThanCompetitor,
                }">
                {{ followers }}
            </span>
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.instagram.columns.average_likes' | translate
            }}</span>
            <span
                class="malou-text-13 text-malou-text-title"
                [ngClass]="{
                    'malou-chip--error max-w-fit !text-malou-pink': isLikeCountLowerThanCompetitor,
                }">
                {{ likeCount }}
            </span>
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.instagram.columns.post_count' | translate
            }}</span>
            <span
                class="malou-text-13 text-malou-text-title"
                [ngClass]="{
                    'malou-chip--error max-w-fit !text-malou-pink': isPostCountLowerThanCompetitor,
                }">
                {{ postCount }}
            </span>
        </div>
    </div>
</ng-template>

<ng-template let-isRestaurant="isRestaurant" #socialMediaListBannerTemplate>
    <div
        class="pointer-events-none absolute flex w-[33%] items-start justify-center rounded border border-malou-primary md:hidden"
        [ngStyle]="{
            height: getBannerHeight() + 'px',
        }"
        [ngClass]="{
            'left-[27%] top-[-60.1px]': isRestaurant,
            'right-[4%] top-[-60px]': !isRestaurant,
        }">
        <span class="malou-text-13--bold w-full bg-malou-light px-8 py-3 text-center text-malou-text-title">
            {{
                isRestaurant
                    ? ('maloupe.aggregated_diagnostic_result.competitors.your_locations' | translate)
                    : ('maloupe.aggregated_diagnostic_result.competitors.competitors_average' | translate)
            }}
        </span>
    </div>
</ng-template>
