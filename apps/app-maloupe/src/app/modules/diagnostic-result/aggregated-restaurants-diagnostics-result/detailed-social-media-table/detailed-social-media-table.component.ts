import { Ng<PERSON><PERSON>, <PERSON><PERSON>tyle, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, ViewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';

import { DiagnosticRating } from '@malou-io/package-utils';

import { TableSortDirection } from ':shared/enums';
import { TrackByFunctionFactory } from ':shared/helpers';
import { DetailedSocialMediaSectionData } from ':shared/interfaces';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { SentenceCasePipe } from ':shared/pipes/sentence-case.pipe';

enum DetailedSocialMediaTableFieldName {
    RESTAURANT_NAME = 'restaurantName',
    FOLLOWER_COUNT = 'followerCount',
    AVERAGE_LIKES = 'averageLikes',
    POST_COUNT = 'postCount',
    COMPETITOR_FOLLOWER_COUNT = 'competitorFollowerCount',
    COMPETITOR_AVERAGE_LIKES = 'competitorAverageLikes',
    COMPETITOR_POST_COUNT = 'competitorPostCount',
}

@Component({
    selector: 'app-detailed-social-media-table',
    standalone: true,
    imports: [
        MatIconModule,
        MatTableModule,
        TranslateModule,
        NgTemplateOutlet,
        MatSortModule,
        SentenceCasePipe,
        NgClass,
        NgStyle,
        ApplyPurePipe,
    ],
    templateUrl: './detailed-social-media-table.component.html',
    styleUrl: './detailed-social-media-table.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailedSocialMediaTableComponent {
    readonly socialMediaData = input<DetailedSocialMediaSectionData[] | null>(null);

    readonly DiagnosticRating = DiagnosticRating;
    readonly TableFieldName = DetailedSocialMediaTableFieldName;
    readonly displayedColumns: string[] = Object.values(DetailedSocialMediaTableFieldName);

    readonly defaultSort = { active: DetailedSocialMediaTableFieldName.FOLLOWER_COUNT, direction: TableSortDirection.Desc };
    readonly dataSource = computed(() => new MatTableDataSource<DetailedSocialMediaSectionData>(this.socialMediaData() ?? []));

    @ViewChild(MatSort) set matSort(sort: MatSort) {
        const dataSource = this.dataSource();
        if (dataSource) {
            dataSource.sortingDataAccessor = (item, property): string | number => {
                switch (property) {
                    case DetailedSocialMediaTableFieldName.RESTAURANT_NAME:
                        return item.restaurantName;
                    case DetailedSocialMediaTableFieldName.FOLLOWER_COUNT:
                        return item.followerCount;
                    case DetailedSocialMediaTableFieldName.AVERAGE_LIKES:
                        return item.averageLikeCount;
                    case DetailedSocialMediaTableFieldName.POST_COUNT:
                        return item.postCount;
                    case DetailedSocialMediaTableFieldName.COMPETITOR_FOLLOWER_COUNT:
                        return item.averageCompetitorFollowerCount;
                    case DetailedSocialMediaTableFieldName.COMPETITOR_AVERAGE_LIKES:
                        return item.averageCompetitorAverageLikes;
                    case DetailedSocialMediaTableFieldName.COMPETITOR_POST_COUNT:
                        return item.averageCompetitorPostCount;
                    default:
                        return '';
                }
            };
            dataSource.sort = sort;
        }
    }

    readonly getBannerHeight = computed(() => {
        const dataLength = this.socialMediaData()?.length ?? 0;
        const bannerOffset = 90;
        const rowGap = 5;
        const rowHeight = 49;
        const endOffset = 25;
        return bannerOffset + (rowHeight + rowGap) * dataLength + endOffset;
    });

    readonly formatNumber = (value: number): string => {
        if (value === 0) {
            return '-';
        }
        return value.toString();
    };

    readonly trackByIdFn = TrackByFunctionFactory.get('diagnosticId');
}
