import { Component, computed, input } from '@angular/core';
import { ChartDataset, ChartOptions, ChartType } from 'chart.js';
import { NgChartsModule } from 'ng2-charts';

import { ReviewAnalysisTag } from '@malou-io/package-utils';

import { ChartDataArray, malouChartColorGreen, malouChartColorLighterBlue, malouChartColorRed } from ':shared/helpers';
import { DetailedSemanticAnalysisSectionData } from ':shared/interfaces';

type BarChartType = Extract<ChartType, 'bar'>;

@Component({
    selector: 'app-sentiments-bar-chart',
    templateUrl: './sentiments-bar-chart.component.html',
    styleUrls: ['./sentiments-bar-chart.component.scss'],
    imports: [NgChartsModule],
})
export class SentimentsBarChartComponent {
    segments = input.required<DetailedSemanticAnalysisSectionData['sentimentsPercentagesPerRestaurant']>();

    readonly analysisTag = ReviewAnalysisTag;
    readonly CHART_TYPE: BarChartType = 'bar';

    chartLabels = computed(() => this._computeChartLabels(this.segments()));
    chartDataSets = computed(() => this._computeChartData(this.segments()));
    chartOption: ChartOptions<BarChartType> = this._computeChartOptions();

    private _computeChartData(
        data: DetailedSemanticAnalysisSectionData['sentimentsPercentagesPerRestaurant']
    ): ChartDataset<BarChartType, ChartDataArray>[] {
        return [
            {
                borderColor: malouChartColorGreen,
                backgroundColor: malouChartColorGreen,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barThickness: 7,
                data: data.map((item) => item.positiveSentimentsPercentage),
            },
            {
                borderColor: malouChartColorRed,
                backgroundColor: malouChartColorRed,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barThickness: 7,
                data: data.map((item) => item.negativeSentimentsPercentage),
            },
        ];
    }

    private _computeChartOptions(): ChartOptions<BarChartType> {
        return {
            events: [],
            plugins: {
                legend: {
                    display: false,
                },
            },
            scales: {
                xAxis: {
                    axis: 'x',
                    type: 'category',
                    stacked: true,
                    display: false,
                },
                yAxis: {
                    axis: 'y',
                    type: 'linear',
                    stacked: true,
                    min: 0,
                    max: 100,
                    ticks: {
                        stepSize: 50,
                        display: false,
                    },
                    grid: {
                        display: true,
                        color: (_context): string | undefined => malouChartColorLighterBlue,
                    },
                },
            },
        };
    }

    private _computeChartLabels(data: DetailedSemanticAnalysisSectionData['sentimentsPercentagesPerRestaurant']): string[][] {
        return data.map((item) => [item.restaurantName, item.restaurantAddress ?? '']);
    }
}
