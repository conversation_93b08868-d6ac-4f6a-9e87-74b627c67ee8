import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

import { DetailedSemanticAnalysisSectionData } from ':shared/interfaces';
import ':shared/pipes/sentence-case.pipe';

import { SentimentsBarChartComponent } from './sentiments-bar-chart/sentiments-bar-chart.component';
import { SentimentsDoughnutChartComponent } from './sentiments-doughnut-chart/sentiments-doughnut-chart.component';

@Component({
    selector: 'app-detailed-semantic-analysis-chart',
    imports: [SentimentsBarChartComponent, SentimentsDoughnutChartComponent],
    templateUrl: './detailed-semantic-analysis-chart.component.html',
    styleUrl: './detailed-semantic-analysis-chart.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailedSemanticAnalysisChartComponent {
    readonly semanticAnalysisData = input<DetailedSemanticAnalysisSectionData>();

    readonly sentimentsBarChartData = computed(() => this.semanticAnalysisData()?.sentimentsPercentagesPerRestaurant);
    readonly sentimentsDoughnutChartData = computed(() => this.semanticAnalysisData()?.aggregated);
}
