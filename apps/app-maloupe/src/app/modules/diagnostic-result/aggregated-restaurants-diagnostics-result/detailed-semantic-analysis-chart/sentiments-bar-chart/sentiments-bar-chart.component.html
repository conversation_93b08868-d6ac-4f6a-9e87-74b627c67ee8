<div class="h-[450px]">
    <div class="relative h-[400px]">
        <canvas baseChart [datasets]="chartDataSets()" [labels]="chartLabels()" [options]="chartOption" [type]="CHART_TYPE"></canvas>
    </div>
    <div class="ml-[3%] flex w-[97%] flex-col">
        <div class="flex justify-around">
            @for (restaurantData of chartLabels(); track $index) {
                <div class="flex w-28 flex-col text-center">
                    <span class="malou-text-13--semibold"> {{ restaurantData[0] }}</span>
                    <span class="malou-text-10--regular italic text-malou-text"> {{ restaurantData[1] }}</span>
                </div>
            }
        </div>
    </div>
</div>
