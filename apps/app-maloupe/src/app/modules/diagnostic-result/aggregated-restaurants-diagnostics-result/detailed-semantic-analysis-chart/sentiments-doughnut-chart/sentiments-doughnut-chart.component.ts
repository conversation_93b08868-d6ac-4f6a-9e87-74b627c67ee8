import { Component, computed, inject, input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Chart, ChartDataset, ChartOptions, ChartType, Plugin } from 'chart.js';
import { NgChartsModule } from 'ng2-charts';

import { MaloupeLocale } from '@malou-io/package-utils';

import { ChartDataArray, malouChartColorGreen, malouChartColorRed, malouChartColorText1, malouChartColorText2 } from ':shared/helpers';
import { DetailedSemanticAnalysisSectionData } from ':shared/interfaces';

type DoughnutChartType = Extract<ChartType, 'doughnut'>;

const colors = [malouChartColorGreen, malouChartColorRed];

enum Quarter {
    TOP_RIGHT,
    TOP_LEFT,
    BOTTOM_RIGHT,
    BOTTOM_LEFT,
}

interface CustomChartLabel {
    value: number;
    percentageValue: number;
    subText: string[];
}

type DoughnutChartDataInput = DetailedSemanticAnalysisSectionData['aggregated'];

@Component({
    selector: 'app-sentiments-doughnut-chart',
    templateUrl: './sentiments-doughnut-chart.component.html',
    styleUrls: ['./sentiments-doughnut-chart.component.scss'],
    imports: [NgChartsModule],
})
export class SentimentsDoughnutChartComponent {
    private readonly _translateService = inject(TranslateService);

    aggregatedData = input.required<DoughnutChartDataInput>();

    readonly CHART_TYPE: DoughnutChartType = 'doughnut';

    chartDataSets = computed(() => this._computeChartData(this.aggregatedData()));
    chartLabels = computed(() => this._computeChartLabels(this.aggregatedData()));
    chartOption: ChartOptions<DoughnutChartType> = this._computeChartOptions();

    readonly CENTER_TEXT_PLUGIN: Plugin = this._getCenterTextPlugin();
    readonly DOUGHNUT_LABEL_LINE: Plugin = this._getDoughnutLabelLinePlugin();

    private _computeChartData(data: DoughnutChartDataInput): ChartDataset<DoughnutChartType, ChartDataArray>[] {
        const { positiveSentimentsPercentage, negativeSentimentsPercentage } = data;

        return [
            {
                backgroundColor: colors,
                borderColor: colors,
                data: [positiveSentimentsPercentage, negativeSentimentsPercentage],
                borderWidth: 0,
            },
        ];
    }

    private _computeChartLabels(data: DoughnutChartDataInput): CustomChartLabel[] {
        const { positiveSentimentCount, negativeSentimentCount, positiveSentimentsPercentage, negativeSentimentsPercentage } = data;
        const currentLanguage = this._translateService.currentLang as MaloupeLocale;
        return [
            {
                value: positiveSentimentCount,
                percentageValue: positiveSentimentsPercentage,
                subText: this._getSubText(currentLanguage, true),
            },
            {
                value: negativeSentimentCount,
                percentageValue: negativeSentimentsPercentage,
                subText: this._getSubText(currentLanguage, false),
            },
        ];
    }

    private _computeChartOptions(): ChartOptions<DoughnutChartType> {
        return {
            events: [], // Disable tooltips
            plugins: {
                legend: {
                    display: false,
                },
            },
            cutout: '80%',
            layout: {
                padding: {
                    top: 30,
                    bottom: 30,
                    left: 50,
                    right: 50,
                },
            },
        };
    }

    private _getCenterTextPlugin(): Plugin {
        return {
            id: 'centerText',
            afterDraw: (chart: Chart, _args: Record<string, never>): void => {
                const { ctx } = chart;
                ctx.save();
                const x = chart.getDatasetMeta(0).data[0].x;
                const y = chart.getDatasetMeta(0).data[0].y;

                ctx.font = '600 14px Poppins';
                ctx.fillStyle = malouChartColorText2;
                const textWidth = ctx.measureText(
                    `${this.aggregatedData().sentimentCount} ${this._translateService.instant('chart.reviews_analysis.title')}`
                );
                ctx.fillText(
                    `${this.aggregatedData().sentimentCount} ${this._translateService.instant('chart.reviews_analysis.title')}`,
                    x - textWidth.width / 2,
                    y + 10
                );
                ctx.restore();
            },
        };
    }

    private _getDoughnutLabelLinePlugin(): Plugin {
        return {
            id: 'doughnutLabelLine',
            afterDraw: (chart: Chart, _args: Record<string, never>): void => {
                const { ctx } = chart;
                const centerX = chart.getDatasetMeta(0).data[0].x;
                const centerY = chart.getDatasetMeta(0).data[0].y;
                chart.data.datasets.forEach((dataset, i) => {
                    chart.getDatasetMeta(i).data.forEach((dataPoint, index) => {
                        ctx.save();
                        const { x, y } = dataPoint.tooltipPosition(true);
                        if (dataset.borderColor) {
                            const borderColor = Array.isArray(dataset.borderColor) ? dataset.borderColor[index] : dataset.borderColor;
                            ctx.strokeStyle = borderColor;
                        }

                        const quarter = this._getQuarter(centerX, centerY, x, y);
                        ctx.beginPath();
                        ctx.moveTo(x, y);

                        const gap = 30;
                        const gap_2x = 50;
                        let currentXPos;
                        let currentYPos;

                        switch (quarter) {
                            case Quarter.TOP_RIGHT:
                                currentXPos = x + gap_2x;
                                currentYPos = y + gap;
                                ctx.lineTo(x + gap, y + gap);
                                ctx.lineTo(x + gap_2x, y + gap);
                                break;
                            case Quarter.BOTTOM_RIGHT:
                                currentXPos = x + gap_2x;
                                currentYPos = y - gap;
                                ctx.lineTo(x + gap, y - gap);
                                ctx.lineTo(x + gap_2x, y - gap);
                                break;
                            case Quarter.BOTTOM_LEFT:
                                currentXPos = x - gap_2x;
                                currentYPos = y - gap;
                                ctx.lineTo(x - gap, y - gap);
                                ctx.lineTo(x - gap_2x, y - gap);
                                ctx.textAlign = 'end';
                                break;
                            default:
                            case Quarter.TOP_LEFT:
                                currentXPos = x - gap_2x;
                                currentYPos = y + gap;
                                ctx.lineTo(x - gap, y + gap);
                                ctx.lineTo(x - gap_2x, y + gap);
                                ctx.textAlign = 'end';
                                break;
                        }
                        ctx.stroke();
                        const labels = chart.data.labels as CustomChartLabel[];
                        const label = labels[index];

                        ctx.textBaseline = 'middle';

                        ctx.font = '600 14px Poppins';
                        ctx.fillStyle = malouChartColorText1;
                        ctx.fillText(`${Math.round(label.percentageValue)} %`, currentXPos, currentYPos - 8);

                        ctx.font = 'italic 400 12px Poppins';
                        ctx.fillStyle = malouChartColorText2;
                        ctx.fillText(`${label.subText[0]}`, currentXPos, currentYPos + 8);
                        ctx.fillText(`${label.subText[1]}`, currentXPos, currentYPos + 20);

                        ctx.restore();
                    });
                });
            },
        };
    }

    private _getQuarter(centerX: number, centerY: number, x: number, y: number): Quarter {
        if (x > centerX) {
            if (y > centerY) {
                return Quarter.TOP_RIGHT;
            } else {
                return Quarter.BOTTOM_RIGHT;
            }
        } else {
            if (y > centerY) {
                return Quarter.TOP_LEFT;
            } else {
                return Quarter.BOTTOM_LEFT;
            }
        }
    }

    private _getSubText(lang: MaloupeLocale, isPositiveFeelings: boolean): string[] {
        const feelingTextTranslation = isPositiveFeelings
            ? this._translateService.instant('chart.reviews_analysis.positive')
            : this._translateService.instant('chart.reviews_analysis.negative');
        return lang === MaloupeLocale.EN
            ? [feelingTextTranslation, this._translateService.instant('chart.reviews_analysis.title')]
            : [this._translateService.instant('chart.reviews_analysis.title'), feelingTextTranslation];
    }
}
