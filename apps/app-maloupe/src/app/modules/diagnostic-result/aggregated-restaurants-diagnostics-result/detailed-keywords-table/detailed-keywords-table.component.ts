import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';

import { DiagnosticRating, HeapEventName } from '@malou-io/package-utils';

import { DiagnosticHttpService } from ':core/http-services/diagnostic.http-service';
import { RankingCompetitorsKeywordListComponent } from ':modules/restaurant-diagnostic-keywords/ranking-competitors-keyword-list/ranking-competitors-keyword-list.component';
import { KeywordGooglePositionComponent } from ':shared/components/keyword-google-position/keyword-google-position.component';
import { Icon, TableSortDirection } from ':shared/enums';
import { TrackByFunctionFactory } from ':shared/helpers';
import { DetailedKeywordsSectionData } from ':shared/interfaces';
import { SentenceCasePipe, ToRangePipe } from ':shared/pipes';
import { getKeywordRatingFromPosition } from ':shared/utils';
import { getUserIdentityFromLocalStorage } from ':shared/utils/manage-user-uuid';

enum DetailedKeywordsTableFieldName {
    RESTAURANT_NAME = 'restaurantName',
    KEYWORDS = 'keywords',
    GOOGLE_POSITION = 'google_position',
    COMPETITORS = 'competitors',
}

@Component({
    selector: 'app-detailed-keywords-table',
    imports: [
        MatIconModule,
        MatTableModule,
        TranslateModule,
        MatSortModule,
        KeywordGooglePositionComponent,
        SentenceCasePipe,
        ToRangePipe,
        NgClass,
        NgTemplateOutlet,
    ],
    templateUrl: './detailed-keywords-table.component.html',
    styleUrl: './detailed-keywords-table.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailedKeywordsTableComponent {
    readonly keywordsData = input<DetailedKeywordsSectionData[] | null>(null);
    readonly receiverEmail = input.required<string | null>();

    readonly _dialog = inject(MatDialog);
    private _diagnosticHttpService = inject(DiagnosticHttpService);

    readonly Icon = Icon;
    readonly DiagnosticRating = DiagnosticRating;
    readonly TableFieldName = DetailedKeywordsTableFieldName;
    readonly displayedColumns: string[] = Object.values(DetailedKeywordsTableFieldName);

    readonly defaultSort = { active: DetailedKeywordsTableFieldName.GOOGLE_POSITION, direction: TableSortDirection.Desc };
    readonly dataSource = computed(() => new MatTableDataSource<DetailedKeywordsSectionData>(this.keywordsData() ?? []));

    @ViewChild(MatSort) set matSort(sort: MatSort) {
        const dataSource = this.dataSource();
        if (dataSource) {
            dataSource.sortingDataAccessor = (item, property): string | number => {
                switch (property) {
                    case DetailedKeywordsTableFieldName.RESTAURANT_NAME:
                        return item.restaurantName;
                    case DetailedKeywordsTableFieldName.KEYWORDS:
                        return item.keywords[0].name ?? item.keywords[0].googlePosition;
                    case DetailedKeywordsTableFieldName.GOOGLE_POSITION:
                        return item.keywords[0].googlePosition;
                    default:
                        return '';
                }
            };
            dataSource.sort = sort;
        }
    }

    readonly trackByIdFn = TrackByFunctionFactory.get('diagnosticId');

    readonly shouldShowBadRatingColor = (keyword1Position: number, keyword2Position: number): boolean => {
        const keyword1Rating = getKeywordRatingFromPosition(keyword1Position);
        const keyword2Rating = getKeywordRatingFromPosition(keyword2Position);
        return keyword1Rating === DiagnosticRating.VERY_BAD || keyword2Rating === DiagnosticRating.VERY_BAD;
    };

    openKeywordsPositionModal(maloupeKeyword: DetailedKeywordsSectionData['keywords'][0]): void {
        this._dialog
            .open(RankingCompetitorsKeywordListComponent, {
                data: {
                    keyword: maloupeKeyword.name,
                    restaurantIndex: maloupeKeyword.googlePosition - 1,
                    restaurantsRankingList: maloupeKeyword.competitors,
                },
                height: '520px',
                width: '750px',
                panelClass: 'malou-dialog-panel',
            })
            .afterClosed()
            .subscribe(() => {
                this._diagnosticHttpService
                    .trackDiagnosticAction$({
                        identity: getUserIdentityFromLocalStorage() ?? '',
                        eventName: HeapEventName.MALOUPE_TRACKING_CLICK_ON_CHECK_KEYWORDS_COMPETITORS,
                        receiverEmail: this.receiverEmail() ?? '',
                    })
                    .subscribe();
            });
    }
}
