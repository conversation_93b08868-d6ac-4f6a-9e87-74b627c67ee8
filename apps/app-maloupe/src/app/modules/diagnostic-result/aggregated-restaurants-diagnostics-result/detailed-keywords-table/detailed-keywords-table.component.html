<mat-table
    class="malou-mat-table md:hidden"
    matSort
    matSortDisableClear
    [matSortActive]="defaultSort.active"
    [matSortDirection]="defaultSort.direction"
    [trackBy]="trackByIdFn"
    [dataSource]="dataSource()"
    #table="matTable">
    <ng-container [matColumnDef]="TableFieldName.RESTAURANT_NAME">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
            {{ 'common.location' | translate }}
        </mat-header-cell>
        <mat-cell *matCellDef="let row" class="flex items-start">
            <div class="flex flex-col !pt-5">
                <span class="malou-text-13--semibold">{{ row.restaurantName }}</span>
                <span class="malou-text-10--regular italic leading-4 text-malou-text">{{ row.restaurantAddress }}</span>
            </div>
        </mat-cell>
    </ng-container>

    <ng-container [matColumnDef]="TableFieldName.KEYWORDS">
        <mat-header-cell *matHeaderCellDef class="flex-1-7 justify-start !pl-5" mat-sort-header>
            {{ 'maloupe.aggregated_diagnostic_result.keywords.columns.keywords' | translate }}
        </mat-header-cell>
        <mat-cell *matCellDef="let row" class="flex-1-7 justify-start !pl-2">
            <div class="flex h-full w-full flex-col items-start">
                <div class="malou-text-13--semibold flex h-full w-full items-center border-b border-malou-primary py-4">
                    {{ row.keywords[0].name }}
                </div>
                <div class="malou-text-13--semibold flex h-full w-full items-center py-4">
                    {{ row.keywords[1].name }}
                </div>
            </div>
        </mat-cell>
    </ng-container>

    <ng-container [matColumnDef]="TableFieldName.GOOGLE_POSITION">
        <mat-header-cell *matHeaderCellDef class="flex-2 !pl-6" mat-sort-header>
            {{ 'maloupe.aggregated_diagnostic_result.keywords.columns.google_position' | translate }}
        </mat-header-cell>
        <mat-cell *matCellDef="let row" class="flex-2">
            <div class="flex h-full w-full flex-col">
                <div class="max-h-14 w-full border-b border-malou-primary py-4">
                    <app-keyword-google-position
                        [googlePosition]="row.keywords[0].googlePosition"
                        [shouldShowAsChip]="true"></app-keyword-google-position>
                </div>
                <div class="max-h-14 w-full py-4">
                    <app-keyword-google-position
                        [googlePosition]="row.keywords[1].googlePosition"
                        [shouldShowAsChip]="true"></app-keyword-google-position>
                </div>
            </div>
        </mat-cell>
    </ng-container>

    <ng-container [matColumnDef]="TableFieldName.COMPETITORS">
        <mat-header-cell *matHeaderCellDef class="flex-0.5 justify-end">
            {{ 'maloupe.aggregated_diagnostic_result.keywords.columns.competitors' | translate }}
        </mat-header-cell>
        <mat-cell *matCellDef="let row" class="flex-0.5 justify-end !pr-0 align-middle">
            <div class="flex h-full w-full flex-col justify-center">
                <button
                    class="malou-text-13--bold h-full border-b border-malou-primary py-4 pr-6 text-end italic text-malou-primary"
                    (click)="openKeywordsPositionModal(row.keywords[0])">
                    {{ 'common.view' | translate }}
                </button>
                <button
                    class="malou-text-13--bold h-full py-4 pr-6 text-end italic text-malou-primary"
                    (click)="openKeywordsPositionModal(row.keywords[1])">
                    {{ 'common.view' | translate }}
                </button>
            </div>
        </mat-cell>
    </ng-container>

    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
    <mat-row *matRowDef="let row; columns: displayedColumns; let i = index"> </mat-row>
</mat-table>

<div class="hidden flex-col gap-y-2 md:mt-1 md:flex">
    @for (restaurantKeyword of keywordsData(); track $index) {
        <div class="rounded-[10px] border border-malou-primary p-4">
            <div class="flex flex-col sm:gap-y-1">
                <div class="malou-text-13--bold">{{ restaurantKeyword.restaurantName | sentenceCase }}</div>
                <span class="malou-text-10--regular italic text-malou-text">{{ restaurantKeyword.restaurantAddress }}</span>
                <div class="mt-2 flex flex-col gap-y-2 px-5">
                    @for (_ of 2 | toRange; track $index) {
                        <ng-container
                            [ngTemplateOutlet]="keywordListDataTemplate"
                            [ngTemplateOutletContext]="{ keyword: restaurantKeyword.keywords[$index], index: $index }"></ng-container>
                    }
                </div>
            </div>
        </div>
    }
</div>

<ng-template let-keyword="keyword" let-index="index" #keywordListDataTemplate>
    <div
        class="flex gap-3"
        [ngClass]="{
            'border-b border-malou-primary py-4': index === 0,
        }">
        <div class="grid grid-cols-2 grid-rows-3 items-center gap-x-1 gap-y-1">
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.keywords.columns.keywords' | translate
            }}</span>
            <span class="malou-text-13 text-malou-text-title">
                {{ keyword.name }}
            </span>
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.keywords.columns.google_position' | translate
            }}</span>
            <span class="malou-text-13 text-malou-text-title"
                ><app-keyword-google-position
                    [withSubtitle]="false"
                    [googlePosition]="keyword.googlePosition"
                    [shouldShowAsChip]="true"></app-keyword-google-position
            ></span>
            <span class="malou-text-12--regular text-malou-text">{{
                'maloupe.aggregated_diagnostic_result.keywords.columns.competitors' | translate
            }}</span>
            <button class="malou-text-13--bold text-start italic text-malou-primary" (click)="openKeywordsPositionModal(keyword)">
                {{ 'common.view' | translate }}
            </button>
        </div>
    </div>
</ng-template>
