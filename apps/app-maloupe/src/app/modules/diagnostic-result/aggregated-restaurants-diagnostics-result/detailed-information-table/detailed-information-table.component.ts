import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, ViewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';

import { DiagnosticRating } from '@malou-io/package-utils';

import { RatingChipComponent } from ':modules/rating-chip/rating-chip.component';
import { TableSortDirection } from ':shared/enums';
import { TrackByFunctionFactory } from ':shared/helpers';
import { DetailedInconsistenciesSectionData } from ':shared/interfaces';
import { LogoPathResolverPipe } from ':shared/pipes/logo-path-resolver.pipe';
import { SentenceCasePipe } from ':shared/pipes/sentence-case.pipe';

enum DetailedInformationTableFieldName {
    RESTAURANT_NAME = 'restaurantName',
    INCONSISTENCIES = 'inconsistencies',
    PHOTOS = 'photos',
    ATTRIBUTES = 'attributes',
}

@Component({
    selector: 'app-detailed-information-table',
    imports: [
        MatIconModule,
        MatTableModule,
        TranslateModule,
        RatingChipComponent,
        MatSortModule,
        SentenceCasePipe,
        NgClass,
        LogoPathResolverPipe,
    ],
    templateUrl: './detailed-information-table.component.html',
    styleUrl: './detailed-information-table.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailedInformationTableComponent {
    readonly informationData = input<DetailedInconsistenciesSectionData[] | null>(null);

    readonly DiagnosticRating = DiagnosticRating;
    readonly TableFieldName = DetailedInformationTableFieldName;
    readonly displayedColumns: string[] = Object.values(DetailedInformationTableFieldName);

    readonly defaultSort = { active: DetailedInformationTableFieldName.INCONSISTENCIES, direction: TableSortDirection.Desc };
    readonly dataSource = computed(() => new MatTableDataSource<DetailedInconsistenciesSectionData>(this.informationData() ?? []));

    @ViewChild(MatSort) set matSort(sort: MatSort) {
        const dataSource = this.dataSource();
        if (dataSource) {
            dataSource.sortingDataAccessor = (item, property): string | number => {
                switch (property) {
                    case DetailedInformationTableFieldName.RESTAURANT_NAME:
                        return item.restaurantName;
                    case DetailedInformationTableFieldName.INCONSISTENCIES:
                        return item.inconsistencies?.count ?? 0;
                    case DetailedInformationTableFieldName.PHOTOS:
                        return item.photos.count;
                    case DetailedInformationTableFieldName.ATTRIBUTES:
                        return item.attributes.count;
                    default:
                        return '';
                }
            };
            dataSource.sort = sort;
        }
    }

    readonly trackByIdFn = TrackByFunctionFactory.get('diagnosticId');
}
