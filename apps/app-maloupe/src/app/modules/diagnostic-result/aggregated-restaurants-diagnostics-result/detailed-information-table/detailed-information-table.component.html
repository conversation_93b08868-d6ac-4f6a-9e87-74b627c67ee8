<mat-table
    class="malou-mat-table md:hidden"
    matSort
    matSortDisableClear
    [matSortActive]="defaultSort.active"
    [matSortDirection]="defaultSort.direction"
    [trackBy]="trackByIdFn"
    [dataSource]="dataSource()"
    #table="matTable">
    <ng-container [matColumnDef]="TableFieldName.RESTAURANT_NAME">
        <mat-header-cell *matHeaderCellDef class="flex-2" mat-sort-header>
            {{ 'common.location' | translate }}
        </mat-header-cell>
        <mat-cell *matCellDef="let restaurantInformation" class="flex-2">
            <div class="flex flex-col">
                <span class="malou-text-13--semibold">{{ restaurantInformation.restaurantName }}</span>
                <span class="malou-text-10--regular italic text-malou-text">{{ restaurantInformation.restaurantAddress }}</span>
            </div>
        </mat-cell>
    </ng-container>

    <ng-container [matColumnDef]="TableFieldName.INCONSISTENCIES">
        <mat-header-cell *matHeaderCellDef class="justify-left" mat-sort-header>
            {{ 'maloupe.aggregated_diagnostic_result.inconsistencies.columns.inconsistencies' | translate }}
        </mat-header-cell>
        <mat-cell *matCellDef="let restaurantInformation" class="justify-left ml-4">
            <div
                class="flex flex-col"
                [ngClass]="{
                    'malou-chip--error': restaurantInformation.inconsistencies.rating === DiagnosticRating.BAD,
                }">
                @if (restaurantInformation.inconsistencies.count !== null) {
                    {{ restaurantInformation.inconsistencies.count }}
                } @else {
                    -
                }
            </div>
            @if (
                restaurantInformation.inconsistencies.platforms.platformsToDisplay.length > 0 &&
                restaurantInformation.inconsistencies.count > 0
            ) {
                <div class="ml-5 flex items-center gap-0.5">
                    @for (platformKey of restaurantInformation.inconsistencies.platforms.platformsToDisplay; track $index) {
                        <img class="h-5 w-5 rounded" [alt]="platformKey" [src]="platformKey | logoPathResolver: { folder: 'platforms' }" />
                    }
                    @if (restaurantInformation.inconsistencies.platforms.otherPlatformCount > 0) {
                        <div class="malou-text-12--semibold text-malou-text">
                            +{{ restaurantInformation.inconsistencies.platforms.otherPlatformCount }}
                        </div>
                    }
                </div>
            }
        </mat-cell>
    </ng-container>

    <ng-container [matColumnDef]="TableFieldName.PHOTOS">
        <mat-header-cell *matHeaderCellDef class="justify-center" mat-sort-header>
            {{ 'maloupe.aggregated_diagnostic_result.inconsistencies.columns.photos' | translate }}
        </mat-header-cell>
        <mat-cell *matCellDef="let restaurantInformation" class="justify-center">
            <div
                class="flex flex-col"
                [ngClass]="{
                    'malou-chip--error': restaurantInformation.photos.rating === DiagnosticRating.BAD,
                }">
                {{ restaurantInformation.photos.count }} / {{ restaurantInformation.photos.total }}
            </div>
        </mat-cell>
    </ng-container>

    <ng-container [matColumnDef]="TableFieldName.ATTRIBUTES">
        <mat-header-cell *matHeaderCellDef class="justify-end !pr-0" mat-sort-header>
            {{ 'maloupe.aggregated_diagnostic_result.inconsistencies.columns.attributes' | translate }}
        </mat-header-cell>
        <mat-cell *matCellDef="let restaurantInformation" class="justify-end">
            <div
                class="flex flex-col"
                [ngClass]="{
                    'malou-chip--error': restaurantInformation.attributes.rating === DiagnosticRating.BAD,
                }">
                {{ restaurantInformation.attributes.count }} / {{ restaurantInformation.attributes.total }}
            </div>
        </mat-cell>
    </ng-container>

    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
    <mat-row
        *matRowDef="let restaurantInformation; columns: displayedColumns; let i = index"
        [class.row-error]="restaurantInformation.informationRating === DiagnosticRating.BAD">
    </mat-row>
</mat-table>

<div class="hidden flex-col gap-y-2 md:mt-2 md:flex">
    @for (restaurantInformation of informationData(); track $index) {
        <div class="rounded-[10px] border border-malou-primary p-4">
            <div class="flex flex-col sm:gap-y-1">
                <div class="malou-text-13--bold">{{ restaurantInformation.restaurantName | sentenceCase }}</div>
                <span class="malou-text-10--regular italic text-malou-text">{{ restaurantInformation.restaurantAddress }}</span>
                <div class="mt-2 flex items-center px-5">
                    <div class="grid grid-cols-2 grid-rows-3 items-center gap-x-12 gap-y-1">
                        <span class="malou-text-12--regular text-malou-text">{{
                            'maloupe.aggregated_diagnostic_result.inconsistencies.columns.inconsistencies' | translate
                        }}</span>
                        <div class="flex items-center gap-1">
                            <span
                                class="malou-text-13 max-w-fit"
                                [ngClass]="{
                                    'malou-chip--error': restaurantInformation.inconsistencies.rating === DiagnosticRating.BAD,
                                }"
                                >{{ restaurantInformation.inconsistencies.count ?? '-' }}</span
                            >
                            @if (
                                restaurantInformation.inconsistencies.platforms.platformsToDisplay.length > 0 &&
                                (restaurantInformation.inconsistencies.count ?? 0) > 0
                            ) {
                                <div class="ml-2 flex items-center gap-0.5">
                                    @for (platformKey of restaurantInformation.inconsistencies.platforms.platformsToDisplay; track $index) {
                                        <img
                                            class="h-5 w-5 rounded"
                                            [alt]="platformKey"
                                            [src]="platformKey | logoPathResolver: { folder: 'platforms' }" />
                                    }
                                    @if (restaurantInformation.inconsistencies.platforms.otherPlatformCount > 0) {
                                        <div class="malou-text-12--semibold text-malou-text">
                                            +{{ restaurantInformation.inconsistencies.platforms.otherPlatformCount }}
                                        </div>
                                    }
                                </div>
                            }
                        </div>

                        <span class="malou-text-12--regular text-malou-text">{{
                            'maloupe.aggregated_diagnostic_result.inconsistencies.columns.photos' | translate
                        }}</span>
                        <span class="malou-text-13"
                            ><app-rating-chip
                                [onlyText]="true"
                                [diagnosticRating]="DiagnosticRating.GOOD"
                                [ratingsToShow]="[DiagnosticRating.BAD]"></app-rating-chip
                        ></span>
                        <span class="malou-text-12--regular text-malou-text">{{
                            'maloupe.aggregated_diagnostic_result.inconsistencies.columns.attributes' | translate
                        }}</span>
                        <span
                            class="malou-text-13 max-w-fit"
                            [ngClass]="{
                                'malou-chip--error': restaurantInformation.attributes.rating === DiagnosticRating.BAD,
                            }">
                            {{ restaurantInformation.attributes.count }} / {{ restaurantInformation.attributes.total }}</span
                        >
                    </div>
                </div>
            </div>
        </div>
    }
</div>
