import { Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { HeapEventName } from '@malou-io/package-utils';

import { DiagnosticHttpService } from ':core/http-services/diagnostic.http-service';
import { AggregatedRestaurantsDiagnosticsResultComponent } from ':modules/diagnostic-result/aggregated-restaurants-diagnostics-result/aggregated-restaurants-diagnostics-result.component';
import { RestaurantDiagnosticResultComponent } from ':modules/diagnostic-result/restaurant-diagnostic-result/restaurant-diagnostic-result.component';
import { AggregatedDiagnostics, Diagnostic } from ':shared/models';
import { isValidUuidV4 } from ':shared/utils';
import { getJwtTokenFromLocalStorage } from ':shared/utils/jwt-token-local-storage-functions';
import { getUserIdentityFromLocalStorage } from ':shared/utils/manage-user-uuid';

@Component({
    selector: 'app-diagnostic-result',
    templateUrl: './diagnostic-result.component.html',
    styleUrl: './diagnostic-result.component.scss',
    standalone: true,
    imports: [AggregatedRestaurantsDiagnosticsResultComponent, RestaurantDiagnosticResultComponent],
})
export class DiagnosticResultComponent implements OnInit {
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _diagnosticHttpService = inject(DiagnosticHttpService);

    readonly receiverEmail = signal<string | null>(null);
    readonly isMalouUser = signal<boolean>(false);
    readonly aggregatedDiagnostics = signal<AggregatedDiagnostics | null>(null);
    readonly publicDiagnosticUrl = signal<string | null>(null);

    ngOnInit(): void {
        this._activatedRoute.queryParamMap.subscribe((queryParams) => {
            const receiverEmail = queryParams.get('receiverEmail');
            if (receiverEmail) {
                this.receiverEmail.set(receiverEmail);
            }
        });

        this._activatedRoute.queryParamMap.subscribe((params) => {
            const diagnosticIds = params.getAll('diagnostic_id');
            if (diagnosticIds.length !== 0) {
                const jwtToken = getJwtTokenFromLocalStorage();
                if (jwtToken) {
                    this._getDiagnosticsForMalouUser(diagnosticIds, jwtToken);
                } else {
                    this._getDiagnosticsForNonMalouUser(diagnosticIds);
                }
            }
        });
    }

    private _getDiagnosticsForMalouUser(diagnosticIds: string[], jwtToken: string): void {
        const areValidUuids = diagnosticIds.every((id) => isValidUuidV4(id));
        if (areValidUuids) {
            this._diagnosticHttpService.getDiagnosticsByIdsWhileAuthenticated$(diagnosticIds, jwtToken).subscribe((result) => {
                this.isMalouUser.set(true);
                const diagnostics = result.diagnostics.map((diagnostic) => new Diagnostic(diagnostic));
                this.aggregatedDiagnostics.set(new AggregatedDiagnostics(diagnostics));
                this.publicDiagnosticUrl.set(result.publicDiagnosticUrl);
                this._trackOpenDiagnosticsResult(diagnosticIds);
            });
        } else {
            this._getDiagnosticsForNonMalouUser(diagnosticIds);
        }
    }

    private _getDiagnosticsForNonMalouUser(diagnosticIds: string[]): void {
        this._diagnosticHttpService.getDiagnosticsByEncryptedIds$(diagnosticIds).subscribe((result) => {
            const diagnostics = result.map((diagnostic) => new Diagnostic(diagnostic));
            this.aggregatedDiagnostics.set(new AggregatedDiagnostics(diagnostics));
            this._trackOpenDiagnosticsResult(diagnosticIds);
        });
    }

    private _trackOpenDiagnosticsResult(diagnosticIds: string[]): void {
        this._diagnosticHttpService
            .trackDiagnosticAction$({
                identity: getUserIdentityFromLocalStorage() ?? '',
                eventName: HeapEventName.MALOUPE_TRACKING_CLICK_ON_DIAGNOSTIC_EMAIL_LINK,
                receiverEmail: this.receiverEmail() ?? undefined,
                diagnosticIds: diagnosticIds.join(','),
            })
            .subscribe();
    }
}
