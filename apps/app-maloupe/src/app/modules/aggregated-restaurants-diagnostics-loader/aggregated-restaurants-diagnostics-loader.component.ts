import { NgClass, NgTemplateOutlet } from '@angular/common';
import { Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { firstValueFrom } from 'rxjs';

import { DiagnosticDto } from '@malou-io/package-dto';
import { HeapEventName, MaloupeEventName } from '@malou-io/package-utils';

import { DiagnosticHttpService } from ':core/http-services/diagnostic.http-service';
import {
    LoaderStep,
    LoaderStepStatus,
    StepType,
} from ':modules/aggregated-restaurants-diagnostics-loader/aggregated-restaurants-diagnostics-loader.interface';
import { DiagnosticHeaderComponent } from ':shared/components/diagnostic-header/diagnostic-header.component';
import { MalouSpinnerComponent } from ':shared/components/spinner/malou-spinner.component';
import { SvgIcon } from ':shared/enums';
import { ApplyPurePipe, EnumTranslatePipe, Illustration, IllustrationPathResolverPipe } from ':shared/pipes';
import { getUserIdentityFromLocalStorage, isMalouUserIdentityInLocalStorage } from ':shared/utils/manage-user-uuid';

@Component({
    selector: 'app-aggregated-restaurants-diagnostics-loader',
    templateUrl: './aggregated-restaurants-diagnostics-loader.component.html',
    styleUrls: ['./aggregated-restaurants-diagnostics-loader.component.scss'],
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatIconModule,
        TranslateModule,
        DiagnosticHeaderComponent,
        MalouSpinnerComponent,
        ApplyPurePipe,
        EnumTranslatePipe,
        IllustrationPathResolverPipe,
    ],
})
export class AggregatedRestaurantsDiagnosticsLoaderComponent {
    private readonly _router = inject(Router);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _diagnosticHttpService = inject(DiagnosticHttpService);

    readonly Illustration = Illustration;
    readonly SvgIcon = SvgIcon;
    readonly LoaderStepStatus = LoaderStepStatus;
    readonly GOOGLE_STEP = {
        type: StepType.GOOGLE,
        stepAction: async (diagnosticId: string): Promise<DiagnosticDto> => {
            await firstValueFrom(this._diagnosticHttpService.updateWithKeywords$(diagnosticId));
            return await firstValueFrom(this._diagnosticHttpService.updateWithReviewAnalyses$(diagnosticId));
        },
        illustration: Illustration.OK_HAND,
    };

    readonly PLATFORMS_STEP = {
        type: StepType.OTHER_PLATFORMS,
        stepAction: async (diagnosticId: string): Promise<DiagnosticDto> =>
            await firstValueFrom(this._diagnosticHttpService.getRestaurantInconsistencies$(diagnosticId)),
        illustration: Illustration.COMPUTER,
    };

    readonly COMPETITORS_STEP = {
        type: StepType.COMPETITORS,
        stepAction: async (diagnosticId: string): Promise<DiagnosticDto> => {
            await firstValueFrom(this._diagnosticHttpService.updateWithSimilarRestaurants$(diagnosticId));
            await firstValueFrom(this._diagnosticHttpService.updateWithGoogleDiagnostic$(diagnosticId));
            return await firstValueFrom(this._diagnosticHttpService.updateWithAverageReviewCountForSimilarRestaurants$(diagnosticId));
        },
        illustration: Illustration.BINOCULARS,
    };

    readonly INSTAGRAM_STEP = {
        type: StepType.INSTAGRAM,
        stepAction: async (diagnosticId: string): Promise<DiagnosticDto> =>
            await firstValueFrom(this._diagnosticHttpService.updateWithInstagramRating$(diagnosticId)),
        illustration: Illustration.MAN,
    };

    readonly STEPS: WritableSignal<Record<string, { diagnostic: DiagnosticDto; steps: LoaderStep[] }>> = signal({});
    readonly stepCount = computed(() => Object.keys(this.STEPS()).length);
    readonly currentDiagnosticId: WritableSignal<string | null> = signal(null);
    readonly currentStepIndex = signal(0);
    readonly currentDiagnosticIndex = signal(1);
    readonly diagnosticIds = signal<string[]>([]);
    readonly diagnostics = signal<DiagnosticDto[]>([]);

    readonly currentSteps = computed(() => {
        const currentDiagnosticId = this.currentDiagnosticId();
        if (!currentDiagnosticId) {
            return [];
        }
        return this.STEPS()[currentDiagnosticId]?.steps ?? [];
    });

    readonly currentIllustration = computed(() => {
        const currentStepIndex = this.currentStepIndex();
        const currentDiagnosticId = this.currentDiagnosticId();
        if (!currentDiagnosticId) {
            return this.currentSteps()[0].illustration;
        }
        const { steps } = this.STEPS()[currentDiagnosticId];
        return currentStepIndex < steps.length ? steps[currentStepIndex].illustration : steps[this.STEPS.length - 1].illustration;
    });

    readonly isDiagnosticLoading = computed(() => this.diagnostics().length === 0);
    private readonly _WAIT_BEFORE_REDIRECT = 1000; // for design purpose

    /**
     * Query params for hubspot form
     */
    private _utmSource = '';
    private _utmMedium = '';
    private _utmCampaign = '';
    private _firstName: string | null = null;
    private _lastName: string | null = null;
    private _email: string | null = null;
    private _phoneNumber: string | null = null;
    private _locationCount: string | null = null;
    private _eventName: MaloupeEventName | null = null;

    constructor() {
        this._activatedRoute.paramMap.subscribe((params) => {
            const diagnosticIdsParams = params.get('diagnostic_ids');
            if (diagnosticIdsParams) {
                const diagnosticIds = JSON.parse(diagnosticIdsParams);
                if (diagnosticIds.length !== 0) {
                    this._diagnosticHttpService.getDiagnosticsByIds$(diagnosticIds).subscribe(({ diagnostics }) => {
                        this.diagnostics.set([...diagnostics]);
                        this.diagnosticIds.set([...diagnostics.map((diagnostic) => diagnostic.id)]);
                        this._setDiagnosticsSteps();
                        this._startDiagnosticComputation();
                    });
                    this._diagnosticHttpService
                        .trackDiagnosticAction$({
                            identity: getUserIdentityFromLocalStorage() ?? '',
                            eventName: HeapEventName.MALOUPE_TRACKING_GET_DIAGNOSTIC_ACTION,
                            diagnosticIds: diagnosticIds.join(','),
                        })
                        .subscribe();
                }
            }
        });
        this._setQueryParams();
    }

    getStepStatus(stepIndex: number, currentStepIndex: number): LoaderStepStatus {
        if (stepIndex < currentStepIndex) {
            return LoaderStepStatus.DONE;
        }
        if (stepIndex === currentStepIndex) {
            return LoaderStepStatus.CURRENT;
        }
        return LoaderStepStatus.TO_BE_DONE;
    }

    private _setQueryParams(): void {
        this._activatedRoute.queryParamMap.subscribe((queryParams) => {
            this._utmSource = queryParams.get('utm_source') ?? '';
            this._utmMedium = queryParams.get('utm_medium') ?? '';
            this._utmCampaign = queryParams.get('utm_campaign') ?? '';
            this._firstName = queryParams.get('first_name') ?? null;
            this._lastName = queryParams.get('last_name') ?? null;
            this._email = queryParams.get('email') ?? null;
            this._phoneNumber = queryParams.get('phone_number') ?? null;
            this._locationCount = queryParams.get('location_count') ?? '';
            this._eventName = queryParams.get('event_name') as MaloupeEventName;
        });
    }

    private async _startDiagnosticComputation(): Promise<void> {
        for (const diagnosticId of this.diagnosticIds()) {
            this.currentDiagnosticId.set(diagnosticId);
            const { steps } = this.STEPS()[diagnosticId];
            for (const step of steps) {
                if (!step.isDisabled) {
                    const diagnostic = await step.stepAction(diagnosticId);
                    if (diagnostic) {
                        this.STEPS.update((stepsMap) => {
                            stepsMap[diagnosticId].diagnostic = diagnostic;
                            return stepsMap;
                        });
                    }
                }
                if (this.currentStepIndex() < steps.length - 1) {
                    this._moveToNextStep();
                }
            }
            if (this.currentDiagnosticIndex() < this.diagnosticIds().length) {
                this.currentDiagnosticIndex.update((currentDiagnosticIndex) => currentDiagnosticIndex + 1);
                this.currentStepIndex.set(0);
            }
        }
        setTimeout(() => {
            const diagnosticIds = this.diagnostics().map((diagnostic) => diagnostic.id);
            if (diagnosticIds.length !== 0) {
                this._openDiagnosticForm(diagnosticIds);
            }
        }, this._WAIT_BEFORE_REDIRECT);
    }

    private _moveToNextStep(): void {
        this.currentStepIndex.update((currentStepIndex) => currentStepIndex + 1);
    }

    private _openDiagnosticForm(diagnosticIds: string[]): void {
        if (this._eventName) {
            this._diagnosticHttpService.updateWithEventName$(diagnosticIds[0], this._eventName).subscribe({
                next: () => {
                    this._navigateToDiagnosticForm(diagnosticIds);
                },
            });
        } else {
            this._navigateToDiagnosticForm(diagnosticIds);
        }
    }

    private _navigateToDiagnosticForm(diagnosticIds: string[]): void {
        if (isMalouUserIdentityInLocalStorage()) {
            this._router.navigate(['diagnostic'], {
                queryParams: {
                    utm_source: this._utmSource,
                    utm_medium: this._utmMedium,
                    utm_campaign: this._utmCampaign,
                    diagnostic_id: diagnosticIds,
                },
            });
            return;
        }
        const userInfoParams = this._areAllInformationValid()
            ? {
                  first_name: this._firstName,
                  last_name: this._lastName,
                  email: this._email,
                  phone_number: this._phoneNumber,
                  location_count: this._locationCount,
              }
            : {};
        this._router.navigate(['form'], {
            queryParams: {
                utm_source: this._utmSource,
                utm_medium: this._utmMedium,
                utm_campaign: this._utmCampaign,
                diagnostic_id: diagnosticIds,
                ...userInfoParams,
            },
        });
    }

    private _setDiagnosticsSteps(): void {
        this.diagnostics().forEach((diagnostic) => {
            const steps: LoaderStep[] = [this.GOOGLE_STEP, this.PLATFORMS_STEP, this.COMPETITORS_STEP];
            const stepsToSet = !!diagnostic.instagramPage?.name ? steps.concat(this.INSTAGRAM_STEP) : steps;
            this.STEPS.update((stepsMap) => {
                stepsMap[diagnostic.id] = {
                    diagnostic,
                    steps: stepsToSet,
                };
                return stepsMap;
            });
        });
    }

    private _areAllInformationValid(): boolean {
        return !!this._firstName && !!this._lastName && !!this._email && !!this._phoneNumber && !!this._locationCount;
    }
}
