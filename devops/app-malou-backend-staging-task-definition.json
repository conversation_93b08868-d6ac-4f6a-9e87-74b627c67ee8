{"taskRoleArn": "arn:aws:iam::515192135070:role/ECSTaskRole", "executionRoleArn": "arn:aws:iam::515192135070:role/ecsTaskExecutionRole", "containerDefinitions": [{"logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/app-malou-backend-staging", "awslogs-region": "eu-west-3", "awslogs-stream-prefix": "ecs"}}, "portMappings": [{"hostPort": 3000, "protocol": "tcp", "containerPort": 3000}], "environment": [{"name": "GOOGLE_REVIEWS_SUBSCRIPTION_NAME", "value": "projects/malou-product/subscriptions/gmb_reviews-sub-staging"}, {"name": "START_PUBSUB_SUBSCRIPTION", "value": "true"}, {"name": "BN_PROJECT_ID", "value": "malou-product"}, {"name": "BN_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "PUSHER_APP_ID", "value": "*******"}, {"name": "PUSHER_KEY", "value": "733a67ba5efb479776f1"}, {"name": "PUSHER_CLUSTER", "value": "eu"}, {"name": "PASSWORD_CRYPT_KEY", "value": "dragNdr0p"}, {"name": "BRICKS_CACHING_TTL", "value": "604800"}, {"name": "AWS_BUCKET", "value": "malou-staging"}, {"name": "BASE_URL", "value": "https://staging.omni.malou.io"}, {"name": "FB_APP_ID", "value": "1377266962404541"}, {"name": "FB_REDIRECT_URI", "value": "https://staging.api.malou.io/api/v1/facebook/oauth/callback/"}, {"name": "TIKTOK_CLIENT_ID", "value": "sbaw8gxqn83tt38e09"}, {"name": "TIKTOK_REDIRECT_URI", "value": "https://staging.api.malou.io/api/v1/tiktok/oauth/callback"}, {"name": "FOURSQUARE_CLIENT_ID", "value": "************************************************"}, {"name": "FOURSQUARE_REDIRECT_URI", "value": ""}, {"name": "GMB_CLIENT_ID", "value": "712726183109-p8dhjne3fo6j3vn8228hpo2tjs7u83dv.apps.googleusercontent.com"}, {"name": "GMB_REDIRECT_URIS", "value": "https://staging.api.malou.io/api/v1/google/oauth2callback"}, {"name": "NODE_ENV", "value": "staging"}, {"name": "YELP_CLIENT_ID", "value": "IEIf37zltcqlYia_9yfjCQ"}, {"name": "AWS_KEY", "value": "AKIAXP46YDWPGGPZDDA3"}, {"name": "MAILGUN_DOMAIN", "value": "mail.app.malou.io"}, {"name": "MAILGUN_HOST", "value": "api.eu.mailgun.net"}, {"name": "PLATFORMS_SCRAPPER_URL", "value": "https://8457xgi890.execute-api.eu-west-3.amazonaws.com/staging/platforms-scrapper"}, {"name": "PYTHON_CRAWLER_URL", "value": "https://8457xgi890.execute-api.eu-west-3.amazonaws.com/development/python-crawler"}, {"name": "KEYWORDS_GENERATOR_URL", "value": "https://dzzqxto6n1.execute-api.eu-west-3.amazonaws.com/staging/keywordsGenerator"}, {"name": "FB_API_VERSION", "value": "v20.0"}, {"name": "FIXED_IP_CALLER_URL", "value": "https://l86x12pjii.execute-api.eu-west-3.amazonaws.com/development/crawl/node/us-static"}, {"name": "HASHTAGS_GENERATOR_URL", "value": "https://dzzqxto6n1.execute-api.eu-west-3.amazonaws.com/staging/hashtagsGenerator"}, {"name": "START_SQS_CONSUMER", "value": "true"}, {"name": "SEND_EMAIL", "value": "true"}, {"name": "SCRAP_PAGES_JAUNES", "value": "true"}, {"name": "FETCH_RANKINGS", "value": "true"}, {"name": "ADMIN_APP_EMAIL", "value": "<EMAIL>"}, {"name": "ELASTICACHE_URI", "value": "ec-redis-staging-2.hnqdvp.ng.0001.euw3.cache.amazonaws.com"}, {"name": "ELASTICACHE_PORT", "value": "6379"}, {"name": "UBEREATS_REDIRECT_URI", "value": "https://staging.api.malou.io/api/v1/ubereats/oauth/callback"}, {"name": "UBEREATS_CLIENT_ID", "value": "wQsVVx8aEPW7uHi1Zql8Z1uPU_345FWO"}, {"name": "REVIEWS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_collect_reviews_staging"}, {"name": "PUBLISH_POST_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_publish_post_staging"}, {"name": "SCRAPPER_API_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_scrapper_api_staging"}, {"name": "REVIEW_BOOSTER_SNS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_review_booster_sns_staging"}, {"name": "PUPPETEER_SERVICE_ARN", "value": "arn:aws:lambda:eu-west-3:515192135070:function:puppeteerServiceDevelopment"}, {"name": "BASE_API_URL", "value": "https://staging.api.malou.io/api/v1"}, {"name": "BASE_API_MALOUPE_URL", "value": "https://staging.api.malou.io/api/maloupe"}, {"name": "BM_PARTNER_NAME", "value": "<PERSON><PERSON>"}, {"name": "BM_PARTNER_EMAIL_ADDRESS", "value": "<EMAIL>"}, {"name": "BM_BRAND_CONTACT_NAME", "value": "<PERSON><PERSON>"}, {"name": "BM_BRAND_CONTACT_EMAIL_ADDRESS", "value": "<EMAIL>"}, {"name": "BM_BRAND_WEBSITE_URL", "value": "https://malou.io/"}, {"name": "BM_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "MEDIA_CONVERT_ENDPOINT", "value": "https://xpxxifqxa.mediaconvert.eu-west-3.amazonaws.com"}, {"name": "AWS_MEDIA_CONVERT_ROLE", "value": "arn:aws:iam::515192135070:role/service-role/MediaConvert_Default_Role"}, {"name": "MEDIA_CONVERT_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_convert_media_staging"}, {"name": "PLATFORMS_SCRAPPER_FUNCTION_NAME", "value": "platformsScrapper"}, {"name": "KEYWORDS_GENERATOR_FUNCTION_NAME", "value": "keywordsGenerator"}, {"name": "FIREBASE_PROJECT_ID", "value": "malou-1555310857472"}, {"name": "FIREBASE_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "TRANSLATOR_FUNCTION_NAME", "value": "serverlessTranslator"}, {"name": "CLOUDINARY_CLOUD_NAME", "value": "dtjj53vxe"}, {"name": "CLOUDINARY_API_KEY", "value": "989924112896137"}, {"name": "THUMBNAIL_GENERATOR_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_thumbnail_queue_staging"}, {"name": "SLACK_ALERTS_WEBHOOK_URL", "value": "https://hooks.slack.com/triggers/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"}, {"name": "SLACK_APP_ALERTS_WEBHOOK_URL", "value": "*******************************************************************************"}, {"name": "KEYWORDS_SCORE_FUNCTION_NAME", "value": "keywordsScoreStaging"}, {"name": "EXPERIMENTATION_APP_URL", "value": "https://experimentation.malou.io:3300"}, {"name": "EXPERIMENTATION_APP_CLIENT_KEY", "value": "sdk-1Opl3RBF1NxyS4a"}, {"name": "REVIEWS_SEMANTIC_ANALYSIS_OVERVIEW_FUNCTION_NAME", "value": "reviewsSemanticAnalysisOverviewStaging"}, {"name": "AI_TEXT_GENERATION_SERVICE_FUNCTION_NAME", "value": "aiTextGenerationStaging"}, {"name": "KEYWORDS_GENERATOR_FUNCTION_NAME_V2", "value": "keywordsGeneratorV2Staging"}, {"name": "AI_HASHTAG_GEN_SERVICE_FUNCTION_NAME", "value": "serverlessHashtagGeneratorStaging"}, {"name": "YEXT_API_BASE_URL", "value": "https://sbx-api.yextapis.com/v2"}, {"name": "YEXT_API_VERSION", "value": "20240424"}, {"name": "YEXT_KNOWLEDGE_ENGINE_STARTER_SKU", "value": "DEV-00010000"}, {"name": "YEXT_US_KNOWLEDGE_ENGINE_STARTER_SKU", "value": "DEV-00010000"}, {"name": "YEXT_US_MENU_LISTING_SKU", "value": "KE-00001820"}, {"name": "TEXT_TRANSLATOR_FUNCTION_NAME", "value": "serverlessTextTranslatorStaging"}, {"name": "SIMILAR_RESTAURANTS_FUNCTION_NAME", "value": "similarRestaurantsIdentificationStaging"}, {"name": "RESY_API_KEY", "value": "VbWk7s3L4KiK5fzlO7JD3Q5EYolJI7n5"}, {"name": "CREATE_MESSAGE_QUEUES_MONTHLY_SAVE_ROI_INSIGHTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_message_queues_monthly_save_roi_insights_staging"}, {"name": "MONTHLY_ROI_SAVE_INSIGHTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_save_roi_insights_staging"}, {"name": "CREATE_MESSAGES_MONTHLY_UPDATE_SIMILAR_RESTAURANTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_messages_monthly_update_similar_restaurants_staging"}, {"name": "MONTHLY_UPDATE_SIMILAR_RESTAURANTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_update_similar_restaurants_staging"}, {"name": "OPEN_TELEMETRY_COLLECTOR_HOST", "value": "localhost"}, {"name": "OTEL_NODE_RESOURCE_DETECTORS", "value": "env,host"}, {"name": "DIAGNOSTIC_KEYWORDS_GENERATOR_FUNCTION_NAME", "value": "serverlessKeywordDiagnosisStaging"}, {"name": "CREATE_NEW_REVIEWS_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_send_new_reviews_notification_staging"}, {"name": "CREATE_POST_ERROR_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_post_error_notification_staging"}, {"name": "CREATE_COMMENTS_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_comments_notification_staging"}, {"name": "CREATE_MENTIONS_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_mentions_notification_staging"}, {"name": "CREATE_MESSAGE_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_message_notification_staging"}, {"name": "AI_MEDIA_DESCRIPTION_GENERATION_SERVICE_FUNCTION_NAME", "value": "serverlessAiImageAnalysisStaging"}, {"name": "CREATE_PLATFORM_DISCONNECTED_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_platform_disconnected_notification_staging"}, {"name": "AI_TEXT_DUPLICATION_FUNCTION_NAME", "value": "serverlessAiPostDuplicationStaging"}, {"name": "APPLE_BUSINESS_CONNECT_API_URL", "value": "https://businessconnect.apple.com"}, {"name": "APPLE_BUSINESS_CONNECT_COMPANY_ID", "value": "1501513140663465984"}, {"name": "APPLE_BUSINESS_CONNECT_CLIENT_ID", "value": "14d6723e-fed3-b800-1500-88f521ef2000"}, {"name": "CREATE_INFO_UPDATE_ERROR_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_info_update_error_notification_staging"}, {"name": "UPDATE_REVIEW_RELEVANT_BRICKS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_update_review_relevant_bricks_staging"}, {"name": "AI_REVIEWS_FUNCTION_NAME", "value": "serverlessAiReviewsStaging"}, {"name": "AI_KEYWORDS_BREAKDOWN_SERVICE_FUNCTION_NAME", "value": "serverlessKeywordsBreakdownStaging"}, {"name": "FETCH_REVIEW_SEMANTIC_ANALYSIS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_fetch_review_semantic_analysis_staging"}, {"name": "FETCH_REVIEW_SEMANTIC_ANALYSIS_FIFO_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_fetch_review_semantic_analysis_staging.fifo"}, {"name": "NEW_GMAPS_API_ROLLOUT", "value": "0"}, {"name": "AI_SEMANTIC_ANALYSIS_FUNCTION_NAME", "value": "serverlessAiSemanticAnalysisStaging"}, {"name": "MONTHLY_SAVE_KEYWORD_SEARCH_IMPRESSIONS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_save_keyword_search_impressions_staging"}], "secrets": [{"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/mapstr_bearer_token-NQsM5h", "name": "MAPSTR_BEARER_TOKEN"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/bn_private_key-ddIVxX", "name": "BN_PRIVATE_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app-malou-api/pusher_secret-MSe9lW", "name": "PUSHER_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/platforms_scrapper_authorization-zX8Zme", "name": "PLATFORMS_SCRAPPER_AUTHORIZATION"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/node_crawler_api_key-4f48j2", "name": "NODE_CRAWLER_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/keywords_generator_authorization-LJgrm3", "name": "KEYWORDS_GENERATOR_AUTHORIZATION"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/aws_key-FiDiDr", "name": "AWS_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/fb_client_secret-kJfVam", "name": "FB_CLIENT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app-malou-api/tiktok_client_secret-XSSyEb", "name": "TIKTOK_CLIENT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app-malou-api/foursquare_client_secret-NxJswl", "name": "FOURSQUARE_CLIENT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app-malou-api/gmb_client_secret-mjGRBM", "name": "GMB_CLIENT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app-malou-api/mongodb_uri-XX9Y8f", "name": "MONGODB_URI"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/passport_secret-xcdX5y", "name": "PASSPORT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app_malou_api/gmaps_apikey-HFTfJG", "name": "GMAPS_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/yelp_api_key-zsF3AD", "name": "YELP_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/encrypt_secret-n7UJhm", "name": "ENCRYPT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/jobs_key-uZF0KH", "name": "JOBS_AUTHORIZATION"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/mailgun_api_key-fYk79G", "name": "MAILGUN_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/facebook_webhook_token-kCDLHN", "name": "FB_WEBHOOK_TOKEN"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/malou_api_key-pXJzzz", "name": "MALOU_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/hashtags_generator_api_key-wmSrC5", "name": "HASHTAGS_GENERATOR_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/hashtags_generator_api_key-wmSrC5", "name": "KEYWORDS_GENERATOR_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/fb_app_token-QmVFwm", "name": "FB_APP_TOKEN"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/apple_business_connect_client_secret-fSTCfp", "name": "APPLE_BUSINESS_CONNECT_CLIENT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/puppeteer_service_authorization-1JR6Ko", "name": "PUPPETEER_SERVICE_AUTHORIZATION"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/ubereats_client_secret-Q1Egxo", "name": "UBEREATS_CLIENT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/bm_private_key-RC3kOq", "name": "BM_PRIVATE_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/github_app_client_secret-r9TX7r", "name": "GITHUB_APP_CLIENT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/github_app_private_key-eqMAcS", "name": "GITHUB_APP_PRIVATE_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app-malou-api/mongodb_agenda_uri-93BYnE", "name": "MONGODB_AGENDA_URI"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/openai_api_key-fdAp5K", "name": "OPENAI_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/firebase_private_key-VY8mSS", "name": "FIREBASE_PRIVATE_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/translator_authorization-Xc16jf", "name": "TRANSLATOR_AUTHORIZATION"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/cloudinary_api_secret-8INqaM", "name": "CLOUDINARY_API_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/front_chat_user_verification_secret-Wsn6Cd", "name": "FRONT_CHAT_USER_VERIFICATION_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app-malou-api/yext_api_key-js35Dz", "name": "YEXT_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/keyword_tool_api_key-aAY5JG", "name": "KEYWORD_TOOL_API_KEY"}, {"valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/maloupe/google_api_key-C6KqVL", "name": "MALOUPE_GMAPS_API_KEY"}, {"name": "BRIGHT_DATA_RESIDENTIAL_PROXY_PASSWORD", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/bright_data_residential_proxy_password-dVJMvZ"}, {"name": "BRIGHT_DATA_ISP_PROXY_PASSWORD", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/bright_data_isp_proxy_password-HSTEKW"}, {"name": "FRONT_CHAT_USER_VERIFICATION_SECRET_EN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/front_chat_user_verification_secret_en-2VcuC6"}, {"name": "SLACK_TECH_BOT_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/slack_tech_bot_token-hDCH1C"}, {"name": "SLACK_TECH_BOT_SIGNING_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/slack_tech_bot_signing_secret-WyNGQ4"}], "memoryReservation": 1024, "image": "515192135070.dkr.ecr.eu-west-3.amazonaws.com/malou/app-malou-api:staging", "command": ["node", "dist/src/server"], "name": "app-malou-api", "dependsOn": [{"containerName": "open-telemetry-collector", "condition": "START"}]}, {"name": "open-telemetry-collector", "image": "amazon/aws-otel-collector", "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/open-telemetry-collector-staging", "awslogs-region": "eu-west-3", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "OTEL_RESOURCE_ATTRIBUTES", "value": "service.name=api,environment=staging,deployment.environment=staging"}], "secrets": [{"name": "AOT_CONFIG_CONTENT", "valueFrom": "otel-collector-config"}]}], "memory": "2048", "family": "app-malou-backend-staging", "requiresCompatibilities": ["FARGATE"], "networkMode": "awsvpc", "cpu": "512"}