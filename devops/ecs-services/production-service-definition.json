{"cluster": "malou-production", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-0770b4819648861b9", "subnet-08c1ff0883c81a824"], "securityGroups": ["sg-0ee57d383b4645082"], "assignPublicIp": "ENABLED"}}, "launchType": "FARGATE", "enableECSManagedTags": false, "loadBalancers": [{"containerName": "app-malou-api", "targetGroupArn": "arn:aws:elasticloadbalancing:eu-west-3:515192135070:targetgroup/tg-malou-production-alb-api/5ed896920f1fcd64", "containerPort": 3000}], "desiredCount": 3, "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 100}, "healthCheckGracePeriodSeconds": 0, "schedulingStrategy": "REPLICA", "placementConstraints": [], "serviceName": "app-malou-backend-service-production", "serviceRegistries": [], "platformVersion": "LATEST", "placementStrategy": [], "taskDefinition": "arn:aws:ecs:eu-west-3:515192135070:task-definition/app-malou-backend-production:177"}