{"cluster": "malou-dev", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-bfd7bfc4", "subnet-0bce9f62"], "securityGroups": ["sg-0822f362"], "assignPublicIp": "ENABLED"}}, "launchType": "FARGATE", "enableECSManagedTags": false, "loadBalancers": [{"containerName": "app-malou-api", "targetGroupArn": "arn:aws:elasticloadbalancing:eu-west-3:515192135070:targetgroup/tg-malou-dev-alb-3/9426bfff25622c24", "containerPort": 3000}], "desiredCount": 1, "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 100}, "healthCheckGracePeriodSeconds": 0, "schedulingStrategy": "REPLICA", "placementConstraints": [], "serviceName": "app-malou-backend-service-development", "serviceRegistries": [], "platformVersion": "LATEST", "placementStrategy": [], "taskDefinition": "arn:aws:ecs:eu-west-3:515192135070:task-definition/app-malou-backend:1022"}