{"cluster": "malou-staging", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-08fe7d810d23a24f0", "subnet-0c700c2a1a1ae65e7"], "securityGroups": ["sg-06b920f62ca382048"], "assignPublicIp": "ENABLED"}}, "launchType": "FARGATE", "enableECSManagedTags": false, "loadBalancers": [{"containerName": "app-malou-api", "targetGroupArn": "arn:aws:elasticloadbalancing:eu-west-3:515192135070:targetgroup/tg-malou-staging-backend-api/29a592280b42e024", "containerPort": 3000}], "desiredCount": 2, "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 100}, "healthCheckGracePeriodSeconds": 0, "schedulingStrategy": "REPLICA", "placementConstraints": [], "serviceName": "app-malou-backend-service-staging-2", "serviceRegistries": [], "platformVersion": "LATEST", "placementStrategy": [], "taskDefinition": "arn:aws:ecs:eu-west-3:515192135070:task-definition/app-malou-backend-staging:311"}