#!/bin/bash

# Check if jq is installed
if ! command -v jq &> /dev/null
then
    echo "Error: jq is not installed. Please install jq and try again."
    exit 1
fi

PS3="Enter the destination app: "
options=("api" "worker" "all")
select app in "${options[@]}"; do
    case $app in
        "api")
            # Select files that do not include 'worker' in their names
            files=$(ls app-malou-backend-*-task-definition.json | grep -v "worker")
            break
            ;;
        "worker")
            # Select files that include 'worker' in their names
            files=$(ls app-malou-backend-*-task-definition.json | grep "worker")
            break
            ;;
        "all")
            # Select all files
            files=$(ls app-malou-backend-*-task-definition.json)
            break
            ;;
        *)
            echo "Invalid option. Please select a valid app."
            ;;
    esac
done

echo
read -p "Variable name: " var_name
read -p "Base variable value: " base_var_value

PS3="Is this a variable or a secret? "
options=("variable" "secret")
select var_type in "${options[@]}"; do
    case $var_type in
        "variable")
            var_key="environment"
            break
            ;;
        "secret")
            var_key="secrets"
            break
            ;;
        *)
            echo "Invalid option. Please select either 'variable' or 'secret'."
            ;;
    esac
done

echo "Adding the $var_type to the selected task definition files..."

# Loop through the selected files and append the variable to the appropriate array
for file in $files; do
    echo "Modifying $file..."
    
    # Determine the environment from the file name (development, staging, production)
    if [[ "$file" == *"development"* ]]; then
        env="development"
    elif [[ "$file" == *"staging"* ]]; then
        env="staging"
    elif [[ "$file" == *"production"* ]]; then
        env="production"
    else
        echo "Unknown environment in file: $file. Skipping..."
        continue
    fi

    # Replace the part of the base value corresponding to the environment
    new_var_value=$(echo "$base_var_value" | sed "s/development/$env/" | sed "s/staging/$env/" | sed "s/production/$env/")

    echo "Inserting $var_type with value: $new_var_value"

    # Use jq to add the variable or secret to the appropriate array at the end
    if [ "$var_type" == "variable" ]; then
        jq --arg name "$var_name" --arg value "$new_var_value" \
        '.containerDefinitions[0].environment += [{"name": $name, "value": $value}]' "$file" > tmp.json && mv tmp.json "$file"
    else
        jq --arg name "$var_name" --arg value "$new_var_value" \
        '.containerDefinitions[0].secrets += [{"name": $name, "valueFrom": $value}]' "$file" > tmp.json && mv tmp.json "$file"
    fi
done

echo "$var_type added successfully!"
