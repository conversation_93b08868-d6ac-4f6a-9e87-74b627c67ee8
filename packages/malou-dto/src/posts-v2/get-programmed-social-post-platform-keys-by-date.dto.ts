import { z } from 'zod';

import { PlatformKey } from '@malou-io/package-utils';

import { restaurantIdParamsTransformValidator } from '../common';

export const getProgrammedSocialPostPlatformKeysByDateParamsValidator = restaurantIdParamsTransformValidator;
export type GetProgrammedSocialPostPlatformKeysByDateParamsDto = z.infer<typeof getProgrammedSocialPostPlatformKeysByDateParamsValidator>;

export const getProgrammedSocialPostPlatformKeysByDateQueryValidator = z.object({
    timezone: z.string().optional(),
});
export type GetProgrammedSocialPostPlatformKeysByDateQueryDto = z.infer<typeof getProgrammedSocialPostPlatformKeysByDateQueryValidator>;

export type ProgrammedSocialPostPlatformKeysByDateDto = {
    date: string;
    platformKeys: PlatformKey[];
}[];
