interface CalendarEventName {
    en?: string;
    fr?: string;
    es?: string;
    it?: string;
}

interface CalendarEventExample {
    fr?: string;
    en?: string;
}

export interface CalendarEventDto {
    id: string;
    startDate: Date;
    emoji?: string;
    key: string;
    country?: string;
    name: CalendarEventName;
    byDefault: boolean;
    example?: CalendarEventExample;
    ideas?: CalendarEventExample;
    isBankHoliday?: boolean;
    shouldSuggestSpecialHourUpdate?: boolean;
    shouldSuggestToPost?: {
        active: boolean;
        concernedRestaurantCategories: string[];
    };
    createdAt: Date;
    updatedAt: Date;
}
