import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    PlatformKey,
    PlatformPresenceStatus,
    ReviewAnalysisSentiment,
    ReviewAnalysisTag,
    ReviewType,
    SemanticAnalysisFetchStatus,
} from '@malou-io/package-utils';

import { TranslationsDto } from '../keywords';
import type { ScanWithNfcDto } from '../scan';
import { SegmentAnalysisParentTopicDto } from '../segment-analysis-parent-topics/segment-analysis-parent-topics.response.dto';
import { PaginationDto } from '../shared';
import { CommentReviewDto, SocialAttachmentReviewDto } from './review.dto';

export interface AiReviewRelevantBrickDto {
    category: string;
    text: string;
    translationsId?: string;
    translations?: TranslationsDto;
}

export interface IntelligentSubjectDto {
    subject: string;
    isDetected: boolean;
    sentiment?: ReviewAnalysisSentiment;
}

export interface ReviewResponseDto {
    _id: string;
    aiRelatedBricksCount?: number;
    aiRelevantBricks?: AiReviewRelevantBrickDto[];
    archived: boolean;
    businessSocialLink?: string;
    campaignId?: string;
    clientId?: string;
    comments: CommentReviewDto[];
    eaterTotalOrders?: number;
    id: string;
    intelligentSubjects?: IntelligentSubjectDto[];
    key: PlatformKey;
    keywordsLang?: string;
    lang?: string;
    menuItemReviews?: any[];
    order?: {
        appVariant?: string;
        currencyCode?: CurrencyCode;
        deliveredAt?: Date;
        orderTotal?: number;
        workflowId?: string;
    };
    platformId?: string;
    platformPresenceStatus?: PlatformPresenceStatus;
    rating?: number;
    ratingTags?: string[];
    restaurantId: string;
    reviewer?: {
        displayName: string;
        email?: string;
        profilePhotoUrl?: string;
        socialId?: string;
        socialUrl?: string;
    };
    scan?: ScanWithNfcDto;
    scanId?: string;
    semanticAnalysis?: ReviewAnalysisDto;
    semanticAnalysisFetchStatus: SemanticAnalysisFetchStatus | null;
    semanticAnalysisSegments?: SegmentAnalysisDto[];
    socialAttachments: SocialAttachmentReviewDto[];
    socialCreatedAt: string;
    socialId?: string;
    socialLink?: string;
    socialRating?: number;
    socialTranslatedText?: string;
    socialUpdatedAt?: string;
    text?: string;
    title?: string;
    type?: ReviewType;
    wasAnsweredAutomatically: boolean;
}

export interface ReviewAnalysisDto {
    _id: string;
    platformKey: PlatformKey;
    reviewSocialId: string;
    createdAt: string;
    failCount: number;
    providerKey: string;
    segmentAnalyses?: PreviousSegmentAnalysisDto[];
    status: string;
    updatedAt: string;
    openaiProcessingTimeMs?: string;
    providerId?: string;
    rawProviderResponse?: string;
    socialCreatedAt?: string;
    failedReason?: string;
}

// TODO: Remove when feature toggle 'release-new-semantic-analysis' is removed
export interface PreviousSegmentAnalysisDto {
    tag: ReviewAnalysisTag;
    segment: string;
    sentiment: ReviewAnalysisSentiment;
    probability: number;
    originalSegment: string;
}

export interface SegmentAnalysisDto {
    id: string;
    platformKey: PlatformKey;
    reviewSocialId: string;
    reviewSocialCreatedAt?: Date;
    platformSocialId: string;
    category: ReviewAnalysisTag;
    segment: string;
    aiFoundSegment: string;
    isRatingTagOrMenuItem?: boolean;
    sentiment: ReviewAnalysisSentiment;
    topic: string;
    segmentAnalysisParentTopic?: SegmentAnalysisParentTopicDto;
}

export type ReviewWithTranslationsResponseDto = ReviewResponseDto & { translations?: TranslationsDto };

export type GetRestaurantsReviewsResponseDto = { reviews: ReviewResponseDto[]; pagination: PaginationDto };

export type SemanticAnalysisInsightsReviewDto = {
    id: string;
    key: PlatformKey;
    lang?: string;
    menuItemReviews?: any[];
    rating?: number;
    ratingTags?: string[];
    restaurantId: string;
    reviewer: {
        displayName?: string;
        profilePhotoUrl?: string;
    };
    semanticAnalysisSegments?: SegmentAnalysisDto[];
    sentiment: ReviewAnalysisSentiment;
    socialCreatedAt: string;
    socialId: string;
    text?: string;
    translations?: TranslationsDto;
};

// ------------------------------------------------------------------------------------------

export type LightReviewWithSegmentAnalysesDto = {
    id: string;
    restaurantId: string;
    rating: number;
    key: PlatformKey;
    socialId: string;
    text: string;
    semanticAnalysisSegments: SegmentAnalysisDto[];
    translations?: TranslationsDto;
};

// ------------------------------------------------------------------------------------------
export type GetRestaurantsReviewsV2ResponseDto = {
    reviews: ReviewWithTranslationsResponseDto[];
    pagination: {
        pageSize: number;
        skip: number;
    };
};
// ------------------------------------------------------------------------------------------
export type GetReviewCountResponseDto = { count: number };
// ------------------------------------------------------------------------------------------
export type GetRestaurantsUnansweredCountResponseDto = { count: number };
// ------------------------------------------------------------------------------------------
export type GetReviewPageResponseDto = number;
// ------------------------------------------------------------------------------------------
export type SynchronizeReviewsResponseDto = undefined;
// ------------------------------------------------------------------------------------------
export type GetTotalReviewCountResponseDto = { count: number };
// ------------------------------------------------------------------------------------------
export interface GetRestaurantReviewsEvolutionDto {
    results: {
        dataPerDay: { key: PlatformKey; day: number; year: number; total: number }[];
        dataPerWeek: { key: PlatformKey; week: number; year: number; total: number }[];
        dataPerMonth: { key: PlatformKey; month: number; year: number; total: number }[];
    };
    startDate: string;
    endDate: string;
}

export interface GetRestaurantsReviewsAverageRatingDto {
    results: {
        restaurant: {
            _id: string;
            name: string;
            internalName?: string;
        } | null;
        averageRating: number;
        averageRatingPerPlatform: {
            platformKey: PlatformKey;
            averageRating: number;
        }[];
    }[];
    startDate: string;
    endDate: string;
}
