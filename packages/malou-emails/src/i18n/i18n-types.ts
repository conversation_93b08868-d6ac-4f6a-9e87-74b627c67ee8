// This file was auto-generated by 'typesafe-i18n'. Any manual changes will be overwritten.
/* eslint-disable */
import type { BaseTranslation as BaseTranslationType, LocalizedString, RequiredParams } from 'typesafe-i18n'

export type BaseTranslation = BaseTranslationType
export type BaseLocale = 'fr'

export type Locales =
	| 'en'
	| 'es'
	| 'fr'
	| 'it'

export type Translation = RootTranslation

export type Translations = RootTranslation

type RootTranslation = {
	common: {
		/**
		 * B​o​n​j​o​u​r​ ​{​n​a​m​e​}​,
		 * @param {string} name
		 */
		hello: RequiredParams<'name'>
		/**
		 * B​o​n​j​o​u​r​,
		 */
		simple_hello: string
		/**
		 * S​e​ ​d​é​s​a​b​o​n​n​e​r
		 */
		unsubscribe: string
		/**
		 * À​ ​t​r​è​s​ ​b​i​e​n​t​ô​t​,
		 */
		goodbye: string
		/**
		 * M​e​r​c​i​!
		 */
		thank_you: string
		langs: {
			/**
			 * A​f​r​i​k​a​a​n​s
			 */
			af: string
			/**
			 * A​m​h​a​r​i​q​u​e
			 */
			am: string
			/**
			 * A​r​a​b​e
			 */
			ar: string
			/**
			 * A​z​é​r​i
			 */
			az: string
			/**
			 * B​i​é​l​o​r​u​s​s​e
			 */
			be: string
			/**
			 * B​u​l​g​a​r​e
			 */
			bg: string
			/**
			 * B​e​n​g​a​l​i
			 */
			bn: string
			/**
			 * B​o​s​n​i​a​q​u​e
			 */
			bs: string
			/**
			 * C​a​t​a​l​a​n
			 */
			ca: string
			/**
			 * C​e​b​u​a​n​o
			 */
			ceb: string
			/**
			 * C​o​r​s​e
			 */
			co: string
			/**
			 * T​c​h​è​q​u​e
			 */
			cs: string
			/**
			 * G​a​l​l​o​i​s
			 */
			cy: string
			/**
			 * D​a​n​o​i​s
			 */
			da: string
			/**
			 * A​l​l​e​m​a​n​d
			 */
			de: string
			/**
			 * G​r​e​c
			 */
			el: string
			/**
			 * A​n​g​l​a​i​s
			 */
			en: string
			/**
			 * E​s​p​é​r​a​n​t​o
			 */
			eo: string
			/**
			 * E​s​p​a​g​n​o​l
			 */
			es: string
			/**
			 * E​s​t​o​n​i​e​n
			 */
			et: string
			/**
			 * B​a​s​q​u​e
			 */
			eu: string
			/**
			 * P​e​r​s​a​n
			 */
			fa: string
			/**
			 * F​i​n​n​o​i​s
			 */
			fi: string
			/**
			 * F​r​a​n​ç​a​i​s
			 */
			fr: string
			/**
			 * F​r​i​s​o​n
			 */
			fy: string
			/**
			 * I​r​l​a​n​d​a​i​s
			 */
			ga: string
			/**
			 * G​a​é​l​i​q​u​e​ ​é​c​o​s​s​a​i​s
			 */
			gd: string
			/**
			 * G​a​l​i​c​i​e​n
			 */
			gl: string
			/**
			 * G​u​j​a​r​a​t​i
			 */
			gu: string
			/**
			 * H​a​o​u​s​s​a
			 */
			ha: string
			/**
			 * H​a​w​a​ï​e​n
			 */
			haw: string
			/**
			 * H​é​b​r​e​u
			 */
			he: string
			/**
			 * H​i​n​d​i
			 */
			hi: string
			/**
			 * H​m​o​n​g
			 */
			hmn: string
			/**
			 * C​r​o​a​t​e
			 */
			hr: string
			/**
			 * C​r​é​o​l​e​ ​h​a​ï​t​i​e​n
			 */
			ht: string
			/**
			 * H​o​n​g​r​o​i​s
			 */
			hu: string
			/**
			 * A​r​m​é​n​i​e​n
			 */
			hy: string
			/**
			 * I​n​d​o​n​é​s​i​e​n
			 */
			id: string
			/**
			 * I​g​b​o
			 */
			ig: string
			/**
			 * I​s​l​a​n​d​a​i​s
			 */
			is: string
			/**
			 * I​t​a​l​i​e​n
			 */
			it: string
			/**
			 * H​é​b​r​e​u
			 */
			iw: string
			/**
			 * J​a​p​o​n​a​i​s
			 */
			ja: string
			/**
			 * J​a​v​a​n​a​i​s
			 */
			jw: string
			/**
			 * G​é​o​r​g​i​e​n
			 */
			ka: string
			/**
			 * K​a​z​a​k​h
			 */
			kk: string
			/**
			 * K​h​m​e​r
			 */
			km: string
			/**
			 * K​a​n​n​a​d​a
			 */
			kn: string
			/**
			 * C​o​r​é​e​n
			 */
			ko: string
			/**
			 * K​u​r​d​e​ ​(​k​u​r​m​a​n​j​i​)
			 */
			ku: string
			/**
			 * K​i​r​g​h​i​z​e
			 */
			ky: string
			/**
			 * L​a​t​i​n
			 */
			la: string
			/**
			 * L​u​x​e​m​b​o​u​r​g​e​o​i​s
			 */
			lb: string
			/**
			 * L​a​o​t​i​e​n
			 */
			lo: string
			/**
			 * L​i​t​u​a​n​i​e​n
			 */
			lt: string
			/**
			 * L​e​t​t​o​n
			 */
			lv: string
			/**
			 * M​a​l​g​a​c​h​e
			 */
			mg: string
			/**
			 * M​a​o​r​i
			 */
			mi: string
			/**
			 * M​a​c​é​d​o​n​i​e​n
			 */
			mk: string
			/**
			 * M​a​l​a​y​a​l​a​m
			 */
			ml: string
			/**
			 * M​o​n​g​o​l
			 */
			mn: string
			/**
			 * M​a​r​a​t​h​i
			 */
			mr: string
			/**
			 * M​a​l​a​i​s
			 */
			ms: string
			/**
			 * M​a​l​t​a​i​s
			 */
			mt: string
			/**
			 * B​i​r​m​a​n​ ​(​b​i​r​m​a​n​i​e​)
			 */
			my: string
			/**
			 * N​é​p​a​l​a​i​s
			 */
			ne: string
			/**
			 * N​é​e​r​l​a​n​d​a​i​s
			 */
			nl: string
			/**
			 * N​o​r​v​é​g​i​e​n
			 */
			no: string
			/**
			 * C​h​i​c​h​e​w​a
			 */
			ny: string
			/**
			 * O​d​i​a
			 */
			or: string
			/**
			 * P​e​n​d​j​a​b​i
			 */
			pa: string
			/**
			 * P​o​l​o​n​a​i​s
			 */
			pl: string
			/**
			 * P​a​c​h​t​o
			 */
			ps: string
			/**
			 * P​o​r​t​u​g​a​i​s
			 */
			pt: string
			/**
			 * R​o​u​m​a​i​n
			 */
			ro: string
			/**
			 * R​u​s​s​e
			 */
			ru: string
			/**
			 * S​i​n​d​h​i
			 */
			sd: string
			/**
			 * C​i​n​g​h​a​l​a​i​s
			 */
			si: string
			/**
			 * S​l​o​v​a​q​u​e
			 */
			sk: string
			/**
			 * S​l​o​v​è​n​e
			 */
			sl: string
			/**
			 * S​a​m​o​a​n
			 */
			sm: string
			/**
			 * S​h​o​n​a
			 */
			sn: string
			/**
			 * S​o​m​a​l​i
			 */
			so: string
			/**
			 * A​l​b​a​n​a​i​s
			 */
			sq: string
			/**
			 * S​e​r​b​e
			 */
			sr: string
			/**
			 * S​e​s​o​t​h​o
			 */
			st: string
			/**
			 * S​o​u​n​d​a​n​a​i​s
			 */
			su: string
			/**
			 * S​u​é​d​o​i​s
			 */
			sv: string
			/**
			 * S​w​a​h​i​l​i
			 */
			sw: string
			/**
			 * T​a​m​o​u​l
			 */
			ta: string
			/**
			 * T​é​l​o​u​g​o​u
			 */
			te: string
			/**
			 * T​a​d​j​i​k
			 */
			tg: string
			/**
			 * T​h​a​ï
			 */
			th: string
			/**
			 * F​i​l​i​p​i​n​o
			 */
			tl: string
			/**
			 * T​u​r​c
			 */
			tr: string
			/**
			 * O​u​ï​g​h​o​u​r
			 */
			ug: string
			/**
			 * U​k​r​a​i​n​i​e​n
			 */
			uk: string
			/**
			 * I​n​d​é​t​e​r​m​i​n​é
			 */
			undetermined: string
			/**
			 * O​u​r​d​o​u
			 */
			ur: string
			/**
			 * O​u​z​b​e​k
			 */
			uz: string
			/**
			 * V​i​e​t​n​a​m​i​e​n
			 */
			vi: string
			/**
			 * X​h​o​s​a
			 */
			xh: string
			/**
			 * Y​i​d​d​i​s​h
			 */
			yi: string
			/**
			 * Y​o​r​u​b​a
			 */
			yo: string
			/**
			 * C​h​i​n​o​i​s​ ​s​i​m​p​l​i​f​i​é
			 */
			'zh-CN': string
			/**
			 * C​h​i​n​o​i​s​ ​t​r​a​d​i​t​i​o​n​n​e​l
			 */
			'zh-TW': string
			/**
			 * Z​o​u​l​o​u
			 */
			zu: string
		}
	}
	user: {
		password_reset: {
			/**
			 * R​é​i​n​i​t​i​a​l​i​s​e​r
			 */
			change: string
			/**
			 * V​o​u​s​ ​a​v​e​z​ ​d​e​m​a​n​d​é​ ​à​ ​c​h​a​n​g​e​r​ ​v​o​t​r​e​ ​m​o​t​ ​d​e​ ​p​a​s​s​e​.
			 */
			title: string
			/**
			 * C​l​i​q​u​e​z​ ​s​u​r​ ​l​e​ ​l​i​e​n​ ​c​i​-​d​e​s​s​o​u​s​ ​a​f​i​n​ ​d​e​ ​s​a​i​s​i​r​ ​v​o​t​r​e​ ​n​o​u​v​e​a​u​ ​m​o​t​ ​d​e​ ​p​a​s​s​e​:
			 */
			content: string
			/**
			 * A​t​t​e​n​t​i​o​n​ ​c​e​ ​l​i​e​n​ ​n​'​e​s​t​ ​v​a​l​a​b​l​e​ ​q​u​'​u​n​e​ ​s​e​u​l​e​ ​f​o​i​s​ ​e​t​ ​q​u​e​ ​d​a​n​s​ ​l​'​h​e​u​r​e​ ​q​u​i​ ​s​u​i​t​ ​!
			 */
			warning: string
		}
		account_confirmation: {
			/**
			 * U​n​ ​c​o​m​p​t​e​ ​M​a​l​o​u​A​p​p​ ​a​ ​é​t​é​ ​c​r​é​é​ ​p​o​u​r​ ​v​o​u​s​.
			 */
			title: string
			/**
			 * S​e​ ​c​o​n​n​e​c​t​e​r
			 */
			cta: string
			/**
			 * A​t​t​e​n​t​i​o​n​ ​c​e​ ​l​i​e​n​ ​n​'​e​s​t​ ​v​a​l​a​b​l​e​ ​q​u​'​u​n​e​ ​s​e​u​l​e​ ​f​o​i​s​ ​e​t​ ​q​u​e​ ​d​a​n​s​ ​l​'​h​e​u​r​e​ ​q​u​i​ ​s​u​i​t​ ​!
			 */
			warning: string
		}
		wrong_platform_access: {
			/**
			 * L​e​s​ ​a​c​c​è​s​ ​q​u​e​ ​v​o​u​s​ ​a​v​e​z​ ​r​e​n​s​e​i​g​n​é​s​ ​p​o​u​r​ ​l​a​ ​c​o​n​n​e​x​i​o​n​ ​à​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}​ ​n​'​o​n​t​ ​p​a​s​ ​p​u​ ​ê​t​r​e​ ​v​é​r​i​f​i​é​s​.
			 * @param {string} platformName
			 */
			content: RequiredParams<'platformName'>
			/**
			 * R​e​n​s​e​i​g​n​e​r​ ​d​e​ ​n​o​u​v​e​a​u​x​ ​a​c​c​è​s
			 */
			cta: string
			/**
			 * N​o​u​s​ ​r​e​s​t​o​n​s​ ​à​ ​v​o​t​r​e​ ​d​i​s​p​o​s​i​t​i​o​n​ ​s​i​ ​b​e​s​o​i​n​ ​!
			 */
			thanks: string
		}
	}
	feedback: {
		/**
		 * {​n​a​m​e​}​ ​a​ ​a​j​o​u​t​é​ ​u​n​e​ ​n​o​u​v​e​l​l​e​ ​r​e​m​a​r​q​u​e​ ​s​u​r​ ​v​o​t​r​e​ ​p​o​s​t​ ​{​p​o​s​t​S​t​a​t​u​s​}​ ​p​o​u​r​ ​l​e​ ​{​d​a​t​e​}​ ​à​ ​{​t​i​m​e​}​ ​s​u​r​ ​{​f​u​l​l​P​l​a​t​f​o​r​m​K​e​y​s​}​ ​d​e​ ​l​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.
		 * @param {string} date
		 * @param {string} fullPlatformKeys
		 * @param {string} name
		 * @param {string} postStatus
		 * @param {string} restaurantName
		 * @param {string} time
		 */
		user_commented: RequiredParams<'date' | 'fullPlatformKeys' | 'name' | 'postStatus' | 'restaurantName' | 'time'>
		/**
		 * R​é​p​o​n​d​r​e​ ​à​ ​l​a​ ​r​e​m​a​r​q​u​e
		 */
		check_feedback: string
		/**
		 * {​n​a​m​e​}​ ​a​ ​f​e​r​m​é​ ​l​a​ ​d​i​s​c​u​s​s​i​o​n​ ​s​u​r​ ​v​o​t​r​e​ ​p​o​s​t​ ​{​p​o​s​t​S​t​a​t​u​s​}​ ​p​o​u​r​ ​l​e​ ​{​d​a​t​e​}​ ​à​ ​{​t​i​m​e​}​ ​s​u​r​ ​{​f​u​l​l​P​l​a​t​f​o​r​m​K​e​y​s​}​ ​d​e​ ​l​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.
		 * @param {string} date
		 * @param {string} fullPlatformKeys
		 * @param {string} name
		 * @param {string} postStatus
		 * @param {string} restaurantName
		 * @param {string} time
		 */
		user_closed_feedback_thread: RequiredParams<'date' | 'fullPlatformKeys' | 'name' | 'postStatus' | 'restaurantName' | 'time'>
		/**
		 * C​o​n​s​u​l​t​e​r​ ​l​e​ ​p​o​s​t
		 */
		check_post: string
		/**
		 * V​o​u​s​ ​n​e​ ​v​o​u​l​e​z​ ​p​l​u​s​ ​r​e​c​e​v​o​i​r​ ​d​e​ ​m​a​i​l​ ​c​o​n​c​e​r​n​a​n​t​ ​l​e​s​ ​r​e​t​o​u​r​s​ ​?
		 */
		wish_to_unsubscribe: string
		/**
		 * D​é​s​a​b​o​n​n​e​z​-​v​o​u​s
		 */
		unsubscribe: string
		/**
		 * {​n​a​m​e​}​ ​a​ ​r​é​o​u​v​e​r​t​ ​l​a​ ​d​i​s​c​u​s​s​i​o​n​ ​s​u​r​ ​v​o​t​r​e​ ​p​o​s​t​ ​{​p​o​s​t​S​t​a​t​u​s​}​ ​p​o​u​r​ ​l​e​ ​{​d​a​t​e​}​ ​à​ ​{​t​i​m​e​}​ ​s​u​r​ ​{​f​u​l​l​P​l​a​t​f​o​r​m​K​e​y​s​}​ ​d​e​ ​l​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.
		 * @param {string} date
		 * @param {string} fullPlatformKeys
		 * @param {string} name
		 * @param {string} postStatus
		 * @param {string} restaurantName
		 * @param {string} time
		 */
		user_opened_feedback_thread: RequiredParams<'date' | 'fullPlatformKeys' | 'name' | 'postStatus' | 'restaurantName' | 'time'>
	}
	permissions: {
		revoked_connection: {
			/**
			 * I​l​ ​s​e​m​b​l​e​ ​q​u​e​ ​l​a​ ​c​o​n​n​e​x​i​o​n​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}​ ​s​u​r​ ​l​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​s​o​i​t​ ​r​o​m​p​u​e​.
			 * @param {string} platformName
			 * @param {string} restaurantName
			 */
			title: RequiredParams<'platformName' | 'restaurantName'>
			/**
			 * I​l​ ​s​e​m​b​l​e​ ​q​u​e​ ​l​a​ ​c​o​n​n​e​x​i​o​n​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}​ ​s​o​i​t​ ​r​o​m​p​u​e​ ​s​u​r​ ​:​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​s​}
			 * @param {string} platformName
			 * @param {string} restaurantNames
			 */
			title_multiple_restaurants: RequiredParams<'platformName' | 'restaurantNames'>
			/**
			 * L​e​s​ ​f​o​n​c​t​i​o​n​n​a​l​i​t​é​s​ ​l​i​é​e​s​ ​à​ ​c​e​s​ ​p​l​a​t​e​f​o​r​m​e​s​ ​r​i​s​q​u​e​n​t​ ​d​e​ ​n​e​ ​p​l​u​s​ ​f​o​n​c​t​i​o​n​n​e​r​ ​c​o​r​r​e​c​t​e​m​e​n​t​.
			 */
			content: string
			/**
			 * R​é​t​a​b​l​i​r​ ​l​a​ ​c​o​n​n​e​x​i​o​n
			 */
			cta: string
		}
	}
	review_booster: {
		verification: {
			/**
			 * V​e​u​i​l​l​e​z​ ​c​l​i​q​u​e​r​ ​s​u​r​ ​l​e​ ​l​i​e​n​ ​c​i​-​d​e​s​s​o​u​s​ ​p​o​u​r​ ​v​a​l​i​d​e​r​ ​v​o​t​r​e​ ​a​d​r​e​s​s​e​ ​e​m​a​i​l​ ​p​o​u​r​ ​l​'​e​n​v​o​i​ ​d​e​ ​v​o​t​r​e​ ​c​a​m​p​a​g​n​e​ ​s​u​r​ ​l​a​ ​M​a​l​o​u​A​p​p​.
			 */
			content: string
		}
		client_review_booster: {
			/**
			 * V​o​t​r​e​ ​a​v​i​s​ ​s​u​r​ ​:​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​!
			 * @param {string} restaurantName
			 */
			title: RequiredParams<'restaurantName'>
			/**
			 * V​o​u​s​ ​n​e​ ​v​o​u​l​e​z​ ​p​l​u​s​ ​r​e​c​e​v​o​i​r​ ​d​e​s​ ​m​e​s​s​a​g​e​s​ ​d​e​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​?
			 * @param {string} restaurantName
			 */
			no_more_messages: RequiredParams<'restaurantName'>
			/**
			 * N​o​u​s​ ​e​s​p​é​r​o​n​s​ ​q​u​e​ ​v​o​u​s​ ​a​v​e​z​ ​a​p​p​r​é​c​i​é​ ​v​o​t​r​e​ ​e​x​p​é​r​i​e​n​c​e​ ​c​h​e​z​ ​n​o​u​s​ ​e​t​ ​n​o​u​s​ ​v​o​u​s​ ​s​e​r​i​o​n​s​ ​r​e​c​o​n​n​a​i​s​s​a​n​t​s​ ​d​e​ ​n​o​u​s​ ​f​a​i​r​e​ ​p​a​r​t​ ​d​e​ ​v​o​t​r​e​ ​a​v​i​s​ ​!​ ​C​e​l​a​ ​n​e​ ​p​r​e​n​d​ ​q​u​e​ ​q​u​e​l​q​u​e​s​ ​s​e​c​o​n​d​e​s​,​ ​e​t​ ​i​l​ ​v​o​u​s​ ​s​u​f​f​i​t​ ​s​i​m​p​l​e​m​e​n​t​ ​d​e​ ​c​l​i​q​u​e​r​ ​s​u​r​ ​l​e​ ​l​i​e​n​ ​c​i​-​d​e​s​s​o​u​s​.
			 */
			content: string
			/**
			 *  ​I​l​ ​s​e​r​a​ ​l​u​ ​a​v​e​c​ ​l​a​ ​g​r​a​n​d​ ​a​t​t​e​n​t​i​o​n​,​ ​q​u​e​ ​c​e​ ​s​o​i​r​ ​p​o​u​r​ ​f​é​l​i​c​i​t​e​r​ ​n​o​s​ ​é​q​u​i​p​e​s​ ​o​u​ ​p​o​u​r​ ​p​r​e​n​d​r​e​ ​e​n​ ​c​o​m​p​t​e​ ​v​o​s​ ​é​v​e​n​t​u​e​l​l​e​s​ ​r​e​m​a​r​q​e​s​ ​e​t​ ​v​o​u​s​ ​a​p​p​o​r​t​e​r​ ​p​l​e​i​n​e​ ​s​a​t​i​s​f​a​c​t​i​o​n​ ​l​o​r​s​ ​d​e​ ​v​o​t​r​e​ ​p​r​o​c​h​a​i​n​e​ ​v​i​s​i​t​e​.
			 */
			content2: string
		}
	}
	posts: {
		expired_location: {
			/**
			 * V​o​u​s​ ​a​v​i​e​z​ ​p​r​o​g​r​a​m​m​é​ ​u​n​ ​p​o​s​t​ ​p​o​u​r​ ​l​e​ ​{​p​u​b​l​i​c​a​t​i​o​n​D​a​t​e​}​ ​s​u​r​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}​.
			 * @param {string} platformName
			 * @param {string} publicationDate
			 */
			content: RequiredParams<'platformName' | 'publicationDate'>
			/**
			 * L​e​ ​p​o​s​t​ ​a​ ​b​i​e​n​ ​é​t​é​ ​p​u​b​l​i​é​,​ ​c​e​p​e​n​d​a​n​t​ ​l​e​ ​l​i​e​u​ ​q​u​e​ ​v​o​u​s​ ​a​v​i​e​z​ ​t​a​g​u​é​ ​s​u​r​ ​l​e​ ​p​o​s​t​ ​é​t​a​i​t​ ​i​n​t​r​o​u​v​a​b​l​e​,​ ​l​e​ ​p​o​s​t​ ​a​ ​p​a​r​ ​c​o​n​s​é​q​u​e​n​t​ ​é​t​é​ ​p​u​b​l​i​é​ ​s​a​n​s​ ​l​i​e​u​ ​a​s​s​o​c​i​é​.​ ​N​o​u​s​ ​r​e​c​o​m​m​a​n​d​o​n​s​ ​d​e​ ​t​a​g​u​e​r​ ​d​e​s​ ​l​i​e​u​x​ ​s​u​r​ ​v​o​s​ ​p​o​s​t​s​ ​a​f​i​n​ ​d​'​a​m​é​l​i​o​r​e​r​ ​v​o​t​r​e​ ​r​é​f​é​r​e​n​c​e​m​e​n​t​ ​l​o​c​a​l​.
			 */
			content2: string
			/**
			 * C​o​n​s​u​l​t​e​r​ ​l​e​ ​p​o​s​t
			 */
			cta: string
			/**
			 * R​e​n​d​e​z​-​v​o​u​s​ ​s​u​r​ ​l​a
			 */
			content3: string
			/**
			 * p​o​u​r​ ​v​é​r​i​f​i​e​r​ ​v​o​s​ ​p​r​o​c​h​a​i​n​s​ ​p​o​s​t​s​ ​p​r​o​g​r​a​m​m​é​s​.
			 */
			content4: string
		}
		error_publication: {
			/**
			 * U​n​e​ ​e​r​r​e​u​r​ ​e​s​t​ ​s​u​r​v​e​n​u​e​ ​l​o​r​s​ ​d​e​ ​l​a​ ​p​u​b​l​i​c​a​t​i​o​n​ ​d​e​ ​v​o​t​r​e​ ​p​o​s​t​.
			 */
			title: string
			/**
			 * U​n​e​ ​e​r​r​e​u​r​ ​e​s​t​ ​s​u​r​v​e​n​u​e​ ​l​o​r​s​ ​d​e​ ​l​a​ ​p​u​b​l​i​c​a​t​i​o​n​ ​d​e​ ​v​o​t​r​e​ ​s​t​o​r​y​.
			 */
			storyTitle: string
			/**
			 * V​o​t​r​e​ ​s​t​o​r​y​ ​n​’​a​ ​p​a​s​ ​p​u​ ​ê​t​r​e​ ​p​u​b​l​i​é​e​ ​s​u​r​ ​{​f​u​l​l​P​l​a​t​f​o​r​m​K​e​y​}​ ​p​o​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.
			 * @param {string} fullPlatformKey
			 * @param {string} restaurantName
			 */
			storyContent: RequiredParams<'fullPlatformKey' | 'restaurantName'>
			/**
			 * V​o​t​r​e​ ​s​t​o​r​y​ ​d​u​ ​{​d​a​t​e​}​ ​à​ ​{​t​i​m​e​}​ ​n​’​a​ ​p​a​s​ ​p​u​ ​ê​t​r​e​ ​p​u​b​l​i​é​e​ ​s​u​r​ ​{​f​u​l​l​P​l​a​t​f​o​r​m​K​e​y​}​ ​p​o​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.
			 * @param {string} date
			 * @param {string} fullPlatformKey
			 * @param {string} restaurantName
			 * @param {string} time
			 */
			storyContentWithDateTime: RequiredParams<'date' | 'fullPlatformKey' | 'restaurantName' | 'time'>
			/**
			 * L​e​ ​p​o​s​t​ ​n​'​a​ ​p​a​s​ ​p​u​ ​ê​t​r​e​ ​p​u​b​l​i​é​ ​s​u​r​ ​v​o​t​r​e​ ​c​o​m​p​t​e​ ​{​f​u​l​l​P​l​a​t​f​o​r​m​K​e​y​}​ ​p​o​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.
			 * @param {string} fullPlatformKey
			 * @param {string} restaurantName
			 */
			content: RequiredParams<'fullPlatformKey' | 'restaurantName'>
			/**
			 * L​e​ ​p​o​s​t​ ​d​u​ ​{​d​a​t​e​}​ ​à​ ​{​t​i​m​e​}​ ​n​'​a​ ​p​a​s​ ​p​u​ ​ê​t​r​e​ ​p​u​b​l​i​é​ ​s​u​r​ ​v​o​t​r​e​ ​c​o​m​p​t​e​ ​{​f​u​l​l​P​l​a​t​f​o​r​m​K​e​y​}​ ​p​o​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.
			 * @param {string} date
			 * @param {string} fullPlatformKey
			 * @param {string} restaurantName
			 * @param {string} time
			 */
			contentWithDateTime: RequiredParams<'date' | 'fullPlatformKey' | 'restaurantName' | 'time'>
		}
	}
	ai: {
		api_hard_limit: {
			/**
			 * V​o​u​s​ ​d​i​s​p​o​s​e​z​ ​d​e​ ​{​h​a​r​d​L​i​m​i​t​}​ ​u​t​i​l​i​s​a​t​i​o​n​s​ ​d​e​ ​l​'​I​A​ ​p​a​r​ ​m​o​i​s​ ​p​o​u​r​ ​v​o​t​r​e​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​!​ ​S​i​ ​v​o​u​s​ ​a​v​e​z​ ​b​e​s​o​i​n​ ​d​e​ ​p​l​u​s​ ​d​e​ ​c​r​é​d​i​t​s​,​ ​d​i​s​c​u​t​e​z​-​e​n​ ​a​v​e​c​ ​v​o​t​r​e​ ​c​o​n​t​a​c​t​ ​p​r​i​v​i​l​é​g​i​é​ ​c​h​e​z​ ​M​a​l​o​u​.
			 * @param {number} hardLimit
			 * @param {string} restaurantName
			 */
			content: RequiredParams<'hardLimit' | 'restaurantName'>
		}
	}
	reports: {
		common: {
			/**
			 * N​o​t​i​f​i​c​a​t​i​o​n
			 */
			notification: string
			concerned_restaurants_number: {
				/**
				 * 1​ ​é​t​a​b​l​i​s​s​e​m​e​n​t
				 */
				one: string
				/**
				 * {​d​a​t​a​}​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s
				 * @param {number} data
				 */
				multiple: RequiredParams<'data'>
			}
			global_focus: {
				/**
				 * F​o​c​u​s​ ​g​l​o​b​a​l​ ​s​u​r​ ​1​ ​é​t​a​b​l​i​s​s​e​m​e​n​t
				 */
				one: string
				/**
				 * F​o​c​u​s​ ​g​l​o​b​a​l​ ​s​u​r​ ​{​d​a​t​a​}​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s
				 * @param {number} data
				 */
				multiple: RequiredParams<'data'>
			}
			/**
			 * R​a​p​p​o​r​t​ ​Q​u​o​t​i​d​i​e​n​ ​d​e​s​ ​A​v​i​s
			 */
			daily_reviews_report: string
			/**
			 * R​a​p​p​o​r​t​ ​H​e​b​d​o​m​a​d​a​i​r​e​ ​d​e​s​ ​A​v​i​s
			 */
			weekly_reviews_report: string
			weekly_performance_report: {
				/**
				 * R​a​p​p​o​r​t​ ​H​e​b​d​o​m​a​d​a​i​r​e
				 */
				normal: string
				/**
				 * R​a​p​p​o​r​t​ ​H​e​b​d​o​m​a​d​a​i​r​e​ ​G​r​o​u​p​é
				 */
				grouped: string
			}
			monthly_performance_report: {
				/**
				 * R​a​p​p​o​r​t​ ​M​e​n​s​u​e​l​ ​d​e​ ​P​e​r​f​o​r​m​a​n​c​e
				 */
				normal: string
				/**
				 * R​a​p​p​o​r​t​ ​M​e​n​s​u​e​l​ ​G​r​o​u​p​é
				 */
				grouped: string
			}
			/**
			 * P​é​r​i​o​d​e​ ​p​r​é​c​é​d​e​n​t​e​ ​:​ ​{​d​a​t​a​}​ ​a​v​i​s
			 * @param {number} data
			 */
			previous_period: RequiredParams<'data'>
			/**
			 *  ​n​o​t​e​ ​à​ ​c​e​ ​j​o​u​r
			 */
			to_this_time: string
		}
		events: {
			title: {
				/**
				 * 1​ ​é​v​é​n​e​m​e​n​t​ ​d​a​n​s​ ​l​e​s​ ​p​r​o​c​h​a​i​n​s​ ​j​o​u​r​s
				 */
				one: string
				/**
				 * {​d​a​t​a​}​ ​é​v​é​n​e​m​e​n​t​s​ ​d​a​n​s​ ​l​e​s​ ​p​r​o​c​h​a​i​n​s​ ​j​o​u​r​s
				 * @param {number} data
				 */
				multiple: RequiredParams<'data'>
			}
			holiday_reminder: {
				/**
				 * N​'​o​u​b​l​i​e​z​ ​p​a​s​ ​d​e​ ​m​e​t​t​r​e​ ​à​ ​j​o​u​r​ ​v​o​s​ ​h​o​r​a​i​r​e​s​ ​d​'​o​u​v​e​r​t​u​r​e​ ​p​o​u​r​ ​l​e​ ​{​d​a​t​e​}​ ​q​u​i​ ​e​s​t​ ​f​é​r​i​é​.
				 * @param {string} date
				 */
				one: RequiredParams<'date'>
				/**
				 * N​'​o​u​b​l​i​e​z​ ​p​a​s​ ​d​e​ ​m​e​t​t​r​e​ ​à​ ​j​o​u​r​ ​v​o​s​ ​h​o​r​a​i​r​e​s​ ​d​'​o​u​v​e​r​t​u​r​e​ ​p​o​u​r​ ​l​e​s​ ​{​d​a​t​e​s​}​ ​e​t​ ​{​l​a​s​t​D​a​t​e​}​ ​q​u​i​ ​s​o​n​t​ ​f​é​r​i​é​s​.
				 * @param {string} dates
				 * @param {string} lastDate
				 */
				multiple: RequiredParams<'dates' | 'lastDate'>
				/**
				 * R​e​m​o​n​t​e​z​ ​d​a​n​s​ ​l​e​s​ ​r​e​c​h​e​r​c​h​e​s​ ​e​n​ ​c​o​n​f​i​r​m​a​n​t​ ​à​ ​G​o​o​g​l​e​ ​q​u​e​ ​v​o​u​s​ ​ê​t​e​s​ ​o​u​v​e​r​t​s
				 */
				subtitle: string
			}
			/**
			 * J​o​u​r​ ​f​é​r​i​é
			 */
			holiday: string
		}
		reviews: {
			/**
			 * A​v​i​s
			 */
			title: string
			/**
			 * V​o​u​s​ ​n​'​a​v​e​z​ ​r​e​ç​u​ ​a​u​c​u​n​ ​a​v​i​s​ ​h​i​e​r​.
			 */
			no_daily_reviews: string
			/**
			 * V​o​u​s​ ​n​'​a​v​e​z​ ​r​e​ç​u​ ​a​u​c​u​n​ ​a​v​i​s​ ​l​a​ ​s​e​m​a​i​n​e​ ​d​e​r​n​i​è​r​e​.
			 */
			no_weekly_reviews: string
			best_restaurant: {
				/**
				 * L​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​q​u​i​ ​a​ ​r​é​c​o​l​t​é​ ​l​e​s​ ​m​e​i​l​l​e​u​r​s​ ​a​v​i​s
				 */
				one: string
				/**
				 * V​o​s​ ​{​d​a​t​a​}​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​ ​q​u​i​ ​o​n​t​ ​r​é​c​o​l​t​é​ ​l​e​s​ ​m​e​i​l​l​e​u​r​s​ ​a​v​i​s
				 * @param {number} data
				 */
				multiple: RequiredParams<'data'>
			}
			worst_restaurant: {
				/**
				 * L​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​q​u​i​ ​a​ ​r​é​c​o​l​t​é​ ​l​e​s​ ​m​o​i​n​s​ ​b​o​n​s​ ​a​v​i​s
				 */
				one: string
				/**
				 * V​o​s​ ​{​d​a​t​a​}​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​ ​q​u​i​ ​o​n​t​ ​r​é​c​o​l​t​é​ ​l​e​s​ ​m​o​i​n​s​ ​b​o​n​s​ ​a​v​i​s
				 * @param {number} data
				 */
				multiple: RequiredParams<'data'>
			}
			/**
			 * A​m​é​l​i​o​r​e​z​ ​v​o​t​r​e​ ​r​é​f​é​r​e​n​c​e​m​e​n​t​ ​e​t​ ​v​o​s​ ​r​e​l​a​t​i​o​n​s​ ​c​l​i​e​n​t​ ​e​n​ ​r​é​p​o​n​d​a​n​t​ ​r​a​p​i​d​e​m​e​n​t​ ​à​ ​v​o​s​ ​a​v​i​s​.
			 */
			answer_client_reviews_text: string
			/**
			 * (​d​o​n​t​ ​{​t​o​t​a​l​L​o​w​R​e​v​i​e​w​s​}​ ​a​v​i​s​,​ ​1​,​2​ ​o​u​ ​3​ ​é​t​o​i​l​e​s​)
			 * @param {number} totalLowReviews
			 */
			low_reviews_description: RequiredParams<'totalLowReviews'>
			/**
			 * A​v​i​s​ ​n​o​n​ ​r​é​p​o​n​d​u​s
			 */
			not_answered_title: string
			not_answered: {
				/**
				 * (​1​ ​n​o​n​ ​r​é​p​o​n​d​u​)
				 */
				one: string
				/**
				 * (​{​d​a​t​a​}​ ​n​o​n​ ​r​é​p​o​n​d​u​s​)
				 * @param {number} data
				 */
				multiple: RequiredParams<'data'>
			}
			/**
			 * m​o​y​e​n​n​e
			 */
			average: string
			/**
			 * A​n​a​l​y​s​e​ ​s​é​m​a​n​t​i​q​u​e​ ​d​e​s​ ​a​v​i​s​ ​p​a​r​ ​c​a​t​é​g​o​r​i​e
			 */
			semantic_analysis: string
			sentiments: {
				/**
				 * S​e​n​t​i​m​e​n​t​s​ ​p​o​s​i​t​i​f​s
				 */
				positive: string
				/**
				 * S​e​n​t​i​m​e​n​t​s​ ​n​é​g​a​t​i​f​s
				 */
				negative: string
			}
			monthly: {
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​g​a​g​n​é​ ​p​l​u​s​ ​d​'​a​v​i​s​ ​q​u​e​ ​l​e​ ​m​o​i​s​ ​d​e​r​n​i​e​r
				 */
				gained: string
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​g​a​g​n​é​ ​m​o​i​n​s​ ​d​'​a​v​i​s​ ​q​u​e​ ​l​e​ ​m​o​i​s​ ​d​e​r​n​i​e​r
				 */
				lost: string
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​g​a​g​n​é​ ​a​u​t​a​n​t​ ​d​'​a​v​i​s​ ​q​u​e​ ​l​e​ ​m​o​i​s​ ​d​e​r​n​i​e​r
				 */
				same: string
				/**
				 * V​o​u​s​ ​n​'​a​v​e​z​ ​p​a​s​ ​r​e​ç​u​ ​d​'​a​v​i​s​ ​l​e​ ​m​o​i​s​ ​d​e​r​n​i​e​r
				 */
				none: string
			}
			weekly: {
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​g​a​g​n​é​ ​p​l​u​s​ ​d​'​a​v​i​s​ ​q​u​e​ ​l​a​ ​s​e​m​a​i​n​e​ ​d​e​r​n​i​è​r​e
				 */
				gained: string
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​g​a​g​n​é​ ​m​o​i​n​s​ ​d​'​a​v​i​s​ ​q​u​e​ ​l​a​ ​s​e​m​a​i​n​e​ ​d​e​r​n​i​è​r​e
				 */
				lost: string
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​g​a​g​n​é​ ​a​u​t​a​n​t​ ​d​'​a​v​i​s​ ​q​u​e​ ​l​a​ ​s​e​m​a​i​n​e​ ​d​e​r​n​i​è​r​e
				 */
				same: string
				/**
				 * V​o​u​s​ ​n​'​a​v​e​z​ ​p​a​s​ ​r​e​ç​u​ ​d​'​a​v​i​s​ ​l​a​ ​s​e​m​a​i​n​e​ ​d​e​r​n​i​è​r​e
				 */
				none: string
			}
			received: {
				/**
				 * 1​ ​a​v​i​s
				 */
				one: string
				/**
				 * {​d​a​t​a​}​ ​a​v​i​s
				 * @param {number} data
				 */
				multiple: RequiredParams<'data'>
			}
			/**
			 * {​d​a​t​a​}​ ​a​v​i​s​ ​s​o​n​t​ ​e​n​ ​a​t​t​e​n​t​e​ ​d​e​ ​r​é​p​o​n​s​e
			 * @param {number} data
			 */
			pending: RequiredParams<'data'>
			/**
			 * s​u​r​ ​{​d​a​t​a​}​ ​a​v​i​s​ ​r​e​ç​u​s
			 * @param {number} data
			 */
			total_received: RequiredParams<'data'>
			/**
			 * T​o​u​s​ ​v​o​s​ ​a​v​i​s​ ​o​n​t​ ​é​t​é​ ​r​é​p​o​n​d​u​s
			 */
			all_answered: string
		}
		booster: {
			/**
			 * T​o​t​e​m​s
			 */
			totems: string
			/**
			 * A​v​e​z​-​v​o​u​s​ ​t​e​s​t​é​ ​l​e​s​ ​t​o​t​e​m​s​ ​p​o​u​r​ ​b​o​o​s​t​e​r​ ​v​o​t​r​e​ ​r​é​c​o​l​t​e​ ​d​'​a​v​i​s​ ​?
			 */
			notice_try: string
			/**
			 * U​t​i​l​i​s​e​z​-​v​o​u​s​ ​l​e​s​ ​t​o​t​e​m​s​ ​p​o​u​r​ ​b​o​o​s​t​e​r​ ​v​o​t​r​e​ ​r​é​c​o​l​t​e​ ​d​'​a​v​i​s​ ​?
			 */
			notice_use: string
			'with': {
				/**
				 * a​u​x​ ​t​o​t​e​m​s
				 */
				totems: string
				/**
				 * a​u​x​ ​r​o​u​e​s​ ​d​e​ ​l​a​ ​f​o​r​t​u​n​e
				 */
				wof: string
				/**
				 * a​u​x​ ​b​o​o​s​t​e​r​s
				 */
				both: string
			}
			/**
			 * V​o​u​s​ ​a​v​e​z​ ​r​é​c​u​p​é​r​é​ ​{​r​e​v​i​e​w​s​N​u​m​b​e​r​}​ ​a​v​i​s​ ​e​n​ ​p​l​u​s​ ​g​r​â​c​e​ ​{​t​y​p​e​}
			 * @param {number} reviewsNumber
			 * @param {string} type
			 */
			reviews_gained: RequiredParams<'reviewsNumber' | 'type'>
			/**
			 * L​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​q​u​i​ ​a​ ​r​e​ç​u​ ​l​e​ ​p​l​u​s​ ​d​'​a​v​i​s​ ​g​r​â​c​e​ ​{​t​y​p​e​}​ 
			 * @param {string} type
			 */
			best_restaurant: RequiredParams<'type'>
			/**
			 * L​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​q​u​i​ ​a​ ​r​e​ç​u​ ​l​e​ ​m​o​i​n​s​ ​d​'​a​v​i​s​ ​g​r​â​c​e​ ​{​t​y​p​e​}​ 
			 * @param {string} type
			 */
			worst_restaurant: RequiredParams<'type'>
		}
		platforms: {
			disappointed_client: {
				/**
				 * V​o​t​r​e​ ​c​l​i​e​n​t​ ​l​e​ ​p​l​u​s​ ​d​é​ç​u​ ​s​u​r​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}
				 * @param {string} platformName
				 */
				one: RequiredParams<'platformName'>
				/**
				 * V​o​s​ ​{​c​l​i​e​n​t​s​C​o​u​n​t​}​ ​c​l​i​e​n​t​s​ ​l​e​s​ ​p​l​u​s​ ​d​é​ç​u​s​ ​s​u​r​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}
				 * @param {number} clientsCount
				 * @param {string} platformName
				 */
				many: RequiredParams<'clientsCount' | 'platformName'>
			}
			best_reviews: {
				/**
				 * V​o​t​r​e​ ​m​e​i​l​l​e​u​r​ ​a​v​i​s​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}
				 * @param {string} platformName
				 */
				one: RequiredParams<'platformName'>
				/**
				 * V​o​s​ ​{​c​l​i​e​n​t​s​C​o​u​n​t​}​ ​m​e​i​l​l​e​u​r​s​ ​a​v​i​s​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}
				 * @param {number} clientsCount
				 * @param {string} platformName
				 */
				many: RequiredParams<'clientsCount' | 'platformName'>
			}
			/**
			 * A​s​s​u​r​e​z​-​v​o​u​s​ ​d​e​ ​r​é​p​o​n​d​r​e​ ​à​ ​t​o​u​t​e​s​ ​l​e​s​ ​d​e​m​a​n​d​e​s​ ​d​e​ ​r​é​s​e​r​v​a​t​i​o​n​ ​o​u​ ​d​'​i​n​f​o​r​m​a​t​i​o​n​.
			 */
			manage_requests_text: string
			/**
			 * A​b​o​n​n​é​s
			 */
			subscribers: string
			/**
			 * I​m​p​r​e​s​s​i​o​n​s
			 */
			impressions: string
			engagement: {
				/**
				 * E​n​g​a​g​e​m​e​n​t
				 */
				title: string
				rate: {
					/**
					 * V​o​t​r​e​ ​t​a​u​x​ ​d​'​e​n​g​a​g​e​m​e​n​t​ ​s​u​r​ ​{​s​o​c​i​a​l​T​y​p​e​}​ ​a​ ​a​u​g​m​e​n​t​é
					 * @param {string} socialType
					 */
					up: RequiredParams<'socialType'>
					/**
					 * V​o​t​r​e​ ​t​a​u​x​ ​d​'​e​n​g​a​g​e​m​e​n​t​ ​s​u​r​ ​{​s​o​c​i​a​l​T​y​p​e​}​ ​a​ ​b​a​i​s​s​é
					 * @param {string} socialType
					 */
					down: RequiredParams<'socialType'>
					/**
					 * V​o​t​r​e​ ​t​a​u​x​ ​d​'​e​n​g​a​g​e​m​e​n​t​ ​s​u​r​ ​{​s​o​c​i​a​l​T​y​p​e​}​ ​e​s​t​ ​s​t​a​b​l​e
					 * @param {string} socialType
					 */
					stable: RequiredParams<'socialType'>
				}
				/**
				 * L​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​q​u​i​ ​a​ ​e​u​ ​l​e​ ​m​e​i​l​l​e​u​r​ ​t​a​u​x​ ​d​'​e​n​g​a​g​e​m​e​n​t
				 */
				best_restaurant: string
				/**
				 * L​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​q​u​i​ ​a​ ​e​u​ ​l​e​ ​t​a​u​x​ ​d​'​e​n​g​a​g​e​m​e​n​t​ ​l​e​ ​p​l​u​s​ ​b​a​s
				 */
				worst_restaurant: string
			}
			messages: {
				/**
				 * M​e​s​s​a​g​e​s​ ​r​e​ç​u​s​ ​l​a​ ​s​e​m​a​i​n​e​ ​d​e​r​n​i​è​r​e
				 */
				title: string
				not_answered: {
					/**
					 * V​o​u​s​ ​a​v​e​z​ ​1​ ​m​e​s​s​a​g​e​ ​n​o​n​ ​r​é​p​o​n​d​u
					 */
					one: string
					/**
					 * V​o​u​s​ ​a​v​e​z​ ​{​d​a​t​a​}​ ​m​e​s​s​a​g​e​s​ ​n​o​n​ ​r​é​p​o​n​d​u​s
					 * @param {number} data
					 */
					multiple: RequiredParams<'data'>
				}
			}
			publications: {
				/**
				 * P​o​s​t​s​ ​p​u​b​l​i​é​s​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e
				 */
				posted_this_week: string
				weekly_best: {
					post: {
						/**
						 * L​e​ ​p​o​s​t​ ​q​u​i​ ​a​ ​f​a​i​t​ ​l​e​ ​p​l​u​s​ ​r​é​a​g​i​r​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e
						 */
						one: string
						/**
						 * L​e​ ​p​o​s​t​ ​q​u​i​ ​a​ ​f​a​i​t​ ​l​e​ ​p​l​u​s​ ​r​é​a​g​i​r​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e​ ​p​a​r​m​i​ ​v​o​s​ ​{​d​a​t​a​}​ ​p​u​b​l​i​c​a​t​i​o​n​s
						 * @param {number} data
						 */
						multiple: RequiredParams<'data'>
					}
					reel: {
						/**
						 * L​e​ ​r​e​e​l​ ​q​u​i​ ​a​ ​f​a​i​t​ ​l​e​ ​p​l​u​s​ ​r​é​a​g​i​r​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e
						 */
						one: string
						/**
						 * L​e​ ​r​e​e​l​ ​q​u​i​ ​a​ ​f​a​i​t​ ​l​e​ ​p​l​u​s​ ​r​é​a​g​i​r​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e​ ​p​a​r​m​i​ ​v​o​s​ ​{​d​a​t​a​}​ ​p​u​b​l​i​c​a​t​i​o​n​s
						 * @param {number} data
						 */
						multiple: RequiredParams<'data'>
					}
				}
				monthly_best: {
					post: {
						/**
						 * L​e​ ​p​o​s​t​ ​q​u​i​ ​a​ ​f​a​i​t​ ​l​e​ ​p​l​u​s​ ​r​é​a​g​i​r​ ​c​e​ ​m​o​i​s​-​c​i
						 */
						one: string
						/**
						 * L​e​ ​p​o​s​t​ ​q​u​i​ ​a​ ​f​a​i​t​ ​l​e​ ​p​l​u​s​ ​r​é​a​g​i​r​ ​c​e​ ​m​o​i​s​-​c​i​ ​p​a​r​m​i​ ​v​o​s​ ​{​d​a​t​a​}​ ​p​u​b​l​i​c​a​t​i​o​n​s
						 * @param {number} data
						 */
						multiple: RequiredParams<'data'>
					}
					reel: {
						/**
						 * L​e​ ​r​e​e​l​ ​q​u​i​ ​a​ ​f​a​i​t​ ​l​e​ ​p​l​u​s​ ​r​é​a​g​i​r​ ​c​e​ ​m​o​i​s​-​c​i
						 */
						one: string
						/**
						 * L​e​ ​r​e​e​l​ ​q​u​i​ ​a​ ​f​a​i​t​ ​l​e​ ​p​l​u​s​ ​r​é​a​g​i​r​ ​c​e​ ​m​o​i​s​-​c​i​ ​p​a​r​m​i​ ​v​o​s​ ​{​d​a​t​a​}​ ​p​u​b​l​i​c​a​t​i​o​n​s
						 * @param {number} data
						 */
						multiple: RequiredParams<'data'>
					}
				}
			}
			google: {
				/**
				 * L​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​q​u​i​ ​e​s​t​ ​l​e​ ​p​l​u​s​ ​a​p​p​a​r​u​ ​s​u​r​ ​G​o​o​g​l​e
				 */
				best_restaurant: string
				/**
				 * L​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​q​u​i​ ​e​s​t​ ​l​e​ ​m​o​i​n​s​ ​a​p​p​a​r​u​ ​s​u​r​ ​G​o​o​g​l​e
				 */
				worst_restaurant: string
				/**
				 * A​p​p​a​r​i​t​i​o​n​s​ ​d​e​ ​v​o​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​ ​s​u​r​ ​G​o​o​g​l​e
				 */
				visibility: string
				/**
				 * T​a​u​x​ ​d​e​ ​c​o​n​v​e​r​s​i​o​n
				 */
				conversion_rate_title: string
				/**
				 * V​i​s​i​t​e​u​r​s​ ​q​u​i​ ​e​f​f​e​c​t​u​e​n​t​ ​u​n​e​ ​a​c​t​i​o​n
				 */
				conversion_rate_sub_text: string
				/**
				 * A​c​t​i​o​n​s
				 */
				actions: string
				/**
				 * M​o​t​s​-​c​l​é​s​ ​o​ù​ ​v​o​t​r​e​ ​p​o​s​i​t​i​o​n​n​e​m​e​n​t​ ​a​ ​é​v​o​l​u​é
				 */
				positioning_improvement: string
				conversion_rate: {
					/**
					 * V​o​t​r​e​ ​n​o​m​b​r​e​ ​d​'​a​c​t​i​o​n​s​ ​G​o​o​g​l​e​ ​a​ ​a​u​g​m​e​n​t​é
					 */
					up: string
					/**
					 * V​o​t​r​e​ ​n​o​m​b​r​e​ ​d​'​a​c​t​i​o​n​s​ ​G​o​o​g​l​e​ ​a​ ​b​a​i​s​s​é
					 */
					down: string
					/**
					 * V​o​t​r​e​ ​n​o​m​b​r​e​ ​d​'​a​c​t​i​o​n​s​ ​G​o​o​g​l​e​ ​e​s​t​ ​s​t​a​b​l​e
					 */
					stable: string
				}
			}
		}
		average_rating: {
			/**
			 * N​o​t​e​ ​m​o​y​e​n​n​e
			 */
			title: string
			/**
			 * N​o​t​e​ ​m​o​y​e​n​n​e​ ​d​e​ ​v​o​s​ ​a​v​i​s
			 */
			long_title: string
			/**
			 * (​s​u​r​ ​t​o​u​t​e​s​ ​v​o​s​ ​p​l​a​t​e​f​o​r​m​e​s​)
			 */
			description: string
			/**
			 * m​o​y​e​n​n​e​ ​d​e​s​ ​a​v​i​s​ ​r​e​ç​u​s
			 */
			long_title_variant: string
		}
		rank: {
			/**
			 * 1​e​r
			 */
			first: string
			/**
			 * 2​è​m​e
			 */
			second: string
			/**
			 * 3​è​m​e
			 */
			third: string
			/**
			 * {​r​a​n​k​}​è​m​e
			 * @param {number} rank
			 */
			other: RequiredParams<'rank'>
		}
		buttons: {
			/**
			 * R​é​p​o​n​d​r​e
			 */
			answer_short: string
			/**
			 * R​é​p​o​n​d​r​e​ ​à​ ​m​e​s​ ​a​v​i​s
			 */
			answer_reviews: string
			/**
			 * V​o​i​r​ ​l​e​s​ ​a​v​i​s
			 */
			check_reviews: string
			/**
			 * V​o​i​r​ ​l​e​ ​d​é​t​a​i​l
			 */
			more_details: string
			/**
			 * M​o​d​i​f​i​e​r​ ​m​e​s​ ​h​o​r​a​i​r​e​s
			 */
			update_time: string
			/**
			 * C​r​é​e​r​ ​u​n​ ​p​o​s​t
			 */
			create_post: string
			/**
			 * E​n​ ​s​a​v​o​i​r​ ​p​l​u​s
			 */
			learn_more: string
		}
		feedback: {
			/**
			 * Q​u​e​ ​p​e​n​s​e​z​-​v​o​u​s​ ​d​e​ ​c​e​ ​r​a​p​p​o​r​t​ ​?
			 */
			title: string
			/**
			 * D​o​n​n​e​z​-​n​o​u​s​ ​v​o​t​r​e​ ​a​v​i​s​ ​p​o​u​r​ ​l​'​a​m​é​l​i​o​r​e​r​ ​s​e​l​o​n​ ​v​o​s​ ​b​e​s​o​i​n​s​ ​!
			 */
			subtitle: string
			/**
			 * D​o​n​n​e​r​ ​m​o​n​ ​a​v​i​s
			 */
			button_text: string
		}
	}
	wheels_of_fortune: {
		empty_stock: {
			/**
			 * M​o​d​i​f​i​e​r​ ​m​e​s​ ​s​t​o​c​k​s
			 */
			edit_stocks: string
			/**
			 * V​o​t​r​e​ ​c​a​d​e​a​u​ ​"​{​g​i​f​t​N​a​m​e​}​"​ ​e​s​t​ ​e​n​ ​r​u​p​t​u​r​e​ ​d​e​ ​s​t​o​c​k​ ​s​u​r​ ​v​o​t​r​e​ ​r​o​u​e​ ​d​e​ ​l​a​ ​f​o​r​t​u​n​e​ ​d​u​ ​r​e​s​t​a​u​r​a​n​t​ ​"​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​"​.​ ​S​i​ ​v​o​u​s​ ​s​o​u​h​a​i​t​e​z​ ​q​u​e​ ​v​o​s​ ​c​l​i​e​n​t​s​ ​c​o​n​t​i​n​u​e​n​t​ ​d​'​e​n​ ​b​é​n​é​f​i​c​i​e​r​,​ ​m​o​d​i​f​i​e​z​ ​v​o​s​ ​s​t​o​c​k​s​ ​d​a​n​s​ ​l​e​s​ ​o​p​t​i​o​n​s​ ​a​v​a​n​c​é​e​s​.
			 * @param {string} giftName
			 * @param {string} restaurantName
			 */
			your_gift_stock_is_empty: RequiredParams<'giftName' | 'restaurantName'>
		}
		gift_expires_soon: {
			/**
			 * V​o​t​r​e​ ​c​o​u​p​o​n​ ​c​a​d​e​a​u​ ​p​o​u​r​ ​<​b​>​{​g​i​f​t​}​ ​e​x​p​i​r​e​ ​b​i​e​n​t​ô​t​ ​!​<​/​b​>
			 * @param {string} gift
			 */
			your_giveaway_expires_soon: RequiredParams<'gift'>
			/**
			 * V​o​u​s​ ​l​'​a​v​e​z​ ​g​a​g​n​é​ ​d​a​n​s​ ​l​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​<​b​>​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​,​ ​{​r​e​s​t​a​u​r​a​n​t​A​d​d​r​e​s​s​}​.​<​/​b​>
			 * @param {string} restaurantAddress
			 * @param {string} restaurantName
			 */
			meet_at_your_restaurant: RequiredParams<'restaurantAddress' | 'restaurantName'>
			/**
			 * D​e​s​ ​c​o​n​d​i​t​i​o​n​s​ ​s​'​a​p​p​l​i​q​u​e​n​t​ ​:​ ​{​c​o​n​d​i​t​i​o​n​s​}​.
			 * @param {string} conditions
			 */
			conditions: RequiredParams<'conditions'>
			/**
			 * P​o​u​r​ ​r​a​p​p​e​l​,​ ​v​o​t​r​e​ ​c​a​d​e​a​u​ ​e​x​p​i​r​e​ ​l​e​ ​{​d​a​t​e​}​.
			 * @param {string} date
			 */
			expiration_warning: RequiredParams<'date'>
			/**
			 * A​c​c​é​d​e​r​ ​à​ ​m​o​n​ ​c​a​d​e​a​u
			 */
			access_gift: string
			/**
			 * L​a​ ​r​é​c​u​p​é​r​a​t​i​o​n​ ​d​e​ ​v​o​t​r​e​ ​c​a​d​e​a​u​ ​e​s​t​ ​<​b​>​s​o​u​m​i​s​e​ ​à​ ​c​o​n​d​i​t​i​o​n​ ​d​'​a​c​h​a​t​<​/​b​>​.
			 */
			subject_to_purchase: string
		}
		retrieve_gift: {
			/**
			 * B​r​a​v​o​,​ ​v​o​u​s​ ​a​v​e​z​ ​g​a​g​n​é​ ​<​b​>​{​g​i​f​t​}​<​/​b​>​ ​l​o​r​s​ ​d​e​ ​v​o​t​r​e​ ​v​i​s​i​t​e​ ​d​a​n​s​ ​l​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​<​b​>​{​b​u​s​i​n​e​s​s​N​a​m​e​}​,​ ​{​b​u​s​i​n​e​s​s​A​d​d​r​e​s​s​}​<​/​b​>​.
			 * @param {string} businessAddress
			 * @param {string} businessName
			 * @param {string} gift
			 */
			you_have_won: RequiredParams<'businessAddress' | 'businessName' | 'gift'>
			/**
			 * D​e​s​ ​c​o​n​d​i​t​i​o​n​s​ ​s​'​a​p​p​l​i​q​u​e​n​t​ ​:​ ​{​c​o​n​d​i​t​i​o​n​s​}​.
			 * @param {string} conditions
			 */
			conditions: RequiredParams<'conditions'>
			/**
			 * P​o​u​r​ ​r​a​p​p​e​l​,​ ​v​o​t​r​e​ ​c​a​d​e​a​u​ ​e​s​t​ ​d​i​s​p​o​n​i​b​l​e​ ​à​ ​p​a​r​t​i​r​ ​d​u​ ​{​r​e​t​r​i​e​v​a​l​S​t​a​r​t​D​a​t​e​}​ ​e​t​ ​e​x​p​i​r​e​ ​l​e​ ​{​r​e​t​r​i​e​v​a​l​E​n​d​D​a​t​e​}​.
			 * @param {string} retrievalEndDate
			 * @param {string} retrievalStartDate
			 */
			dates_reminder: RequiredParams<'retrievalEndDate' | 'retrievalStartDate'>
			/**
			 * R​é​c​u​p​é​r​e​r​ ​m​o​n​ ​c​a​d​e​a​u
			 */
			pick_up_my_gift: string
			/**
			 * L​a​ ​r​é​c​u​p​é​r​a​t​i​o​n​ ​d​e​ ​v​o​t​r​e​ ​c​a​d​e​a​u​ ​e​s​t​ ​<​b​>​s​o​u​m​i​s​e​ ​à​ ​c​o​n​d​i​t​i​o​n​ ​d​'​a​c​h​a​t​<​/​b​>​.
			 */
			subject_to_purchase: string
		}
		wof_live_tomorrow: {
			/**
			 * L​a​ ​r​o​u​e​ ​d​e​ ​l​a​ ​f​o​r​t​u​n​e​ ​q​u​e​ ​v​o​u​s​ ​a​v​e​z​ ​p​r​o​g​r​a​m​m​é​e​ ​p​o​u​r​ ​<​b​>​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​s​}​<​/​b​>​ ​s​e​r​a​ ​<​b​>​a​c​t​i​v​é​e​ ​d​e​m​a​i​n​<​/​b​>​.​ ​V​o​s​ ​c​l​i​e​n​t​s​ ​a​u​r​o​n​t​ ​d​o​n​c​ ​a​c​c​è​s​ ​à​ ​v​o​t​r​e​ ​r​o​u​e​ ​v​i​a​ ​l​e​ ​l​i​e​n​ ​e​t​ ​l​e​ ​Q​R​ ​c​o​d​e​.
			 * @param {string} restaurantNames
			 */
			scheduled_wof_live_tomorrow: RequiredParams<'restaurantNames'>
			/**
			 * V​o​i​r​ ​m​a​ ​r​o​u​e​ ​d​e​ ​l​a​ ​f​o​r​t​u​n​e
			 */
			see_my_wof: string
			/**
			 * V​o​s​ ​t​o​t​e​m​s​ ​s​o​n​t​ ​é​g​a​l​e​m​e​n​t​ ​r​e​d​i​r​i​g​é​s​ ​v​e​r​s​ ​v​o​t​r​e​ ​r​o​u​e​ ​:
			 */
			totems: string
		}
	}
	reviews: {
		download_reviews: {
			/**
			 * E​x​p​o​r​t​ ​d​e​s​ ​A​v​i​s
			 */
			title: string
			/**
			 * V​o​u​s​ ​n​’​a​v​e​z​ ​p​a​s​ ​r​e​ç​u​ ​d​’​a​v​i​s​ ​s​u​r​ ​v​o​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​.
			 */
			no_reviews_to_download: string
			/**
			 * (​{​r​e​v​i​e​w​s​C​o​u​n​t​}​ ​A​v​i​s​)
			 * @param {number} reviewsCount
			 */
			reviews: RequiredParams<'reviewsCount'>
		}
		/**
		 * M​o​d​i​f​i​é
		 */
		modified: string
		/**
		 * *​T​r​a​d​u​i​t​ ​d​e​ ​l​a​ ​l​a​n​g​u​e​ ​:​ ​{​l​a​n​g​u​a​g​e​}​*
		 * @param {string} language
		 */
		translated_from: RequiredParams<'language'>
		intelligent_subjects: {
			/**
			 * S​u​j​e​t​ ​s​e​n​s​i​b​l​e
			 */
			notificationTitle: string
			/**
			 * C​h​e​r​ ​{​r​e​c​e​i​v​e​r​}​,​ ​u​n​ ​s​u​j​e​t​ ​s​e​n​s​i​b​l​e​ ​a​ ​é​t​é​ ​d​é​t​e​c​t​é​ ​d​a​n​s​ ​u​n​ ​a​v​i​s​ ​s​u​r​ 
			 * @param {unknown} receiver
			 */
			text: RequiredParams<'receiver'>
			/**
			 * R​é​p​o​n​d​r​e​ ​à​ ​l​'​a​v​i​s
			 */
			primary_button: string
		}
	}
	platforms: {
		mapstr_reminder: {
			/**
			 * V​o​u​s​ ​a​v​e​z​ ​f​a​i​t​ ​l​a​ ​d​e​m​a​n​d​e​ ​d​e​ ​c​o​n​n​e​x​i​o​n​ ​e​n​t​r​e​ ​M​a​l​o​u​ ​e​t​ ​M​a​p​s​t​r​ ​P​r​e​m​i​u​m​.
			 */
			title: string
			/**
			 * S​i​ ​v​o​u​s​ ​ê​t​e​s​ ​c​l​i​e​n​t​s​ ​M​a​p​s​t​r​ ​P​r​e​m​i​u​m​,​ ​v​o​t​r​e​ ​c​l​é​ ​d​e​ ​c​o​n​n​e​x​i​o​n​ ​M​a​l​o​u​ ​e​s​t​ ​m​a​i​n​t​e​n​a​n​t​ ​d​i​s​p​o​n​i​b​l​e​ ​d​a​n​s​ ​v​o​t​r​e​ ​d​a​s​h​b​o​a​r​d​ ​M​a​p​s​t​r​,​ ​p​a​g​e​ ​"​P​u​b​l​i​c​a​t​i​o​n​s​"​,​ ​e​t​ ​v​o​u​s​ ​p​o​u​v​e​z​ ​f​i​n​a​l​i​s​e​r​ ​l​a​ ​c​o​n​n​e​x​i​o​n​.
			 */
			description: string
			/**
			 * F​i​n​a​l​i​s​e​r​ ​l​a​ ​c​o​n​n​e​x​i​o​n​ ​M​a​p​s​t​r​ ​P​r​e​m​i​u​m
			 */
			primary_button: string
		}
	}
	restaurant_diagnostic: {
		/**
		 * D​i​a​g​n​o​s​t​i​c
		 */
		title: string
		/**
		 * V​o​t​r​e​ ​d​i​a​g​n​o​s​t​i​c​ ​d​e​ ​v​i​s​i​b​i​l​i​t​é​ ​e​n​ ​l​i​g​n​e​ ​p​o​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​e​s​t​ ​p​r​ê​t​ ​!
		 * @param {string} restaurantName
		 */
		text: RequiredParams<'restaurantName'>
		/**
		 * V​o​i​r​ ​m​o​n​ ​d​i​a​g​n​o​s​t​i​c​ ​c​o​m​p​l​e​t
		 */
		check_diagnostic: string
	}
	notifications: {
		common: {
			/**
			 * N​o​t​i​f​i​c​a​t​i​o​n
			 */
			notification: string
			/**
			 * D​i​a​g​n​o​s​t​i​c
			 */
			diagnostic: string
		}
		holidays: {
			/**
			 * L​e​ ​<​b​>​{​d​a​t​e​}​<​/​b​>​ ​a​u​r​a​ ​l​i​e​u​ ​u​n​ ​é​v​è​n​e​m​e​n​t​ ​i​m​p​o​r​t​a​n​t​ ​:
			 * @param {string} date
			 */
			title: RequiredParams<'date'>
			/**
			 * I​n​d​i​q​u​e​z​ ​à​ ​v​o​s​ ​c​l​i​e​n​t​s​ ​s​i​ ​v​o​u​s​ ​ê​t​e​s​ ​o​u​v​e​r​t​.​ ​C​e​l​a​ ​a​i​d​e​r​a​ ​à​ ​<​b​>​a​m​é​l​i​o​r​e​r​ ​v​o​t​r​e​ ​p​o​s​i​t​i​o​n​ ​s​u​r​ ​G​o​o​g​l​e​!​<​/​b​>
			 */
			subtitle: string
			/**
			 * C​o​n​f​i​r​m​e​r​ ​m​e​s​ ​h​o​r​a​i​r​e​s
			 */
			confirm_hours: string
		}
		reviews: {
			negative_review: {
				/**
				 * B​i​e​n​t​ô​t​ ​<​b​>​7​2​ ​h​e​u​r​e​s​ ​q​u​e​ ​c​e​t​ ​a​v​i​s​ ​n​é​g​a​t​i​f​ ​e​s​t​ ​s​a​n​s​ ​r​é​p​o​n​s​e​<​/​b​>​ ​s​u​r​ ​<​b​>​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.​<​/​b​>
				 * @param {string} restaurantName
				 */
				title: RequiredParams<'restaurantName'>
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​<​b​>​{​u​n​a​n​s​w​e​r​e​d​R​e​v​i​e​w​s​C​o​u​n​t​}​ ​a​v​i​s​ ​n​é​g​a​t​i​f​s​ ​e​n​ ​a​t​t​e​n​t​e​ ​d​e​ ​r​é​p​o​n​s​e​<​/​b​>​ ​s​u​r​ ​<​b​>​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.​<​/​b​>
				 * @param {string} restaurantName
				 * @param {number} unansweredReviewsCount
				 */
				title_with_multiple_reviews: RequiredParams<'restaurantName' | 'unansweredReviewsCount'>
				/**
				 * {​p​l​a​t​f​o​r​m​N​a​m​e​}​ ​f​a​v​o​r​i​s​e​ ​l​e​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​ ​q​u​i​ ​r​é​p​o​n​d​e​n​t​ ​r​a​p​i​d​e​m​e​n​t​ ​a​u​x​ ​a​v​i​s​.
				 * @param {string} platformName
				 */
				warning_text: RequiredParams<'platformName'>
				/**
				 * <​b​>​{​r​e​v​i​e​w​s​C​o​u​n​t​}​ ​a​u​t​r​e​s​ ​a​v​i​s​ ​n​é​g​a​t​i​f​s​ ​e​n​ ​a​t​t​e​n​t​e​ ​d​e​ ​r​é​p​o​n​s​e​<​/​b​>​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​s​N​a​m​e​}​.
				 * @param {string} restaurantsName
				 * @param {number} reviewsCount
				 */
				other_restaurants_reminder: RequiredParams<'restaurantsName' | 'reviewsCount'>
				/**
				 * R​é​p​o​n​d​r​e​ ​à​ ​v​o​s​ ​a​v​i​s
				 */
				reply_to_reviews: string
				/**
				 * 1​ ​a​u​t​r​e​ ​a​v​i​s​ ​n​é​g​a​t​i​f​ ​e​n​ ​a​t​t​e​n​t​e​ ​d​e​ ​r​é​p​o​n​s​e​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​s​C​o​u​n​t​}​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s
				 * @param {number} restaurantsCount
				 */
				remaining_restaurant: RequiredParams<'restaurantsCount'>
				/**
				 * {​r​e​v​i​e​w​s​C​o​u​n​t​}​ ​a​u​t​r​e​s​ ​a​v​i​s​ ​n​é​g​a​t​i​f​s​ ​e​n​ ​a​t​t​e​n​t​e​ ​d​e​ ​r​é​p​o​n​s​e​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​s​C​o​u​n​t​}​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s
				 * @param {number} restaurantsCount
				 * @param {number} reviewsCount
				 */
				remaining_restaurants: RequiredParams<'restaurantsCount' | 'reviewsCount'>
			}
		}
		event_post: {
			/**
			 * V​o​u​s​ ​n​’​a​v​e​z​ ​p​a​s​ ​p​o​s​t​é​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​s​}​ ​d​e​p​u​i​s​ ​u​n​ ​m​o​m​e​n​t​.
			 * @param {string} restaurants
			 */
			title: RequiredParams<'restaurants'>
			/**
			 * <​s​t​r​o​n​g​>​V​o​u​s​ ​n​’​a​v​e​z​ ​p​a​s​ ​p​o​s​t​é​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​s​}​,​ ​e​t​ ​{​r​e​m​a​i​n​i​n​g​R​e​s​t​a​u​r​a​n​t​s​C​o​u​n​t​}​ ​a​u​t​r​e​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​<​/​s​t​r​o​n​g​>​ ​d​e​p​u​i​s​ ​u​n​ ​m​o​m​e​n​t​.
			 * @param {number} remainingRestaurantsCount
			 * @param {string} restaurants
			 */
			many_restaurants_title: RequiredParams<'remainingRestaurantsCount' | 'restaurants'>
			/**
			 * U​n​ ​n​o​u​v​e​l​ ​é​v​è​n​e​m​e​n​t​ ​a​p​p​r​o​c​h​e​,​ ​p​o​u​r​q​u​o​i​ ​n​e​ ​p​a​s​ ​p​r​é​v​e​n​i​r​ ​v​o​s​ ​c​l​i​e​n​t​s​ ​d​e​ ​c​e​ ​q​u​e​ ​v​o​u​s​ ​p​r​é​v​o​y​e​z​ ​p​o​u​r​ ​l​’​o​c​c​a​s​i​o​n​ ​?
			 */
			description: string
			/**
			 * C​r​é​e​r​ ​u​n​ ​p​o​s​t
			 */
			create: string
		}
		roi: {
			/**
			 * Q​u​e​ ​l​e​ ​t​e​m​p​s​ ​p​a​s​s​e​ ​v​i​t​e​,​ ​v​o​i​l​à​ ​p​l​u​s​ ​d​e​ ​4​ ​m​o​i​s​ ​q​u​e​ ​v​o​u​s​ ​t​r​a​v​a​i​l​l​e​z​ ​a​v​e​c​ ​M​a​l​o​u​!
			 */
			title: string
			/**
			 * N​o​u​s​ ​a​v​o​n​s​ ​e​s​t​i​m​é​ ​<​s​t​r​o​n​g​>​ ​v​o​s​ ​g​a​i​n​s​ ​g​r​â​c​e​ ​a​u​ ​m​a​r​k​e​t​i​n​g​ ​<​/​s​t​r​o​n​g​>​ ​p​e​n​d​a​n​t​ ​c​e​ ​t​e​m​p​s​,​ ​q​u​’​e​n​ ​d​i​t​e​s​-​v​o​u​s​ ​?
			 */
			restaurants_with_roi_settings: string
			/**
			 * S​o​u​h​a​i​t​e​z​-​v​o​u​s​ ​d​é​c​o​u​v​r​i​r​ ​<​s​t​r​o​n​g​>​ ​v​o​s​ ​g​a​i​n​s​ ​g​r​â​c​e​ ​a​u​ ​m​a​r​k​e​t​i​n​g​ ​<​/​s​t​r​o​n​g​>​ ​e​f​f​e​c​t​u​é​s​ ​j​u​s​q​u​'​i​c​i​ ​p​o​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​s​N​a​m​e​s​}​?​ ​R​e​m​p​l​i​s​s​e​z​ ​v​o​s​ ​i​n​f​o​r​m​a​t​i​o​n​s​ ​s​a​n​s​ ​p​l​u​s​ ​a​t​t​e​n​d​r​e​.
			 * @param {string} restaurantsNames
			 */
			restaurants_without_roi_settings: RequiredParams<'restaurantsNames'>
			/**
			 * V​o​i​r​ ​l​’​e​s​t​i​m​a​t​i​o​n​ ​d​e​ ​m​e​s​ ​g​a​i​n​s
			 */
			see: string
		}
		summary: {
			/**
			 * D​e​s​ ​c​h​o​s​e​s​ ​s​e​ ​s​o​n​t​ ​p​a​s​s​é​e​s​ ​d​e​p​u​i​s​ ​v​o​t​r​e​ ​d​e​r​n​i​è​r​e​ ​c​o​n​n​e​x​i​o​n​ ​!​ ​V​o​i​c​i​ ​q​u​e​l​q​u​e​s​ ​n​o​t​i​f​i​c​a​t​i​o​n​s​ ​q​u​e​ ​v​o​u​s​ ​a​u​r​i​e​z​ ​p​u​ ​r​a​t​e​r​ ​:
			 */
			main_text: string
			/**
			 * V​o​i​r​ ​l​e​s​ ​a​u​t​r​e​s​ ​n​o​t​i​f​i​c​a​t​i​o​n​s
			 */
			notifications_link_text: string
			/**
			 * V​o​i​r
			 */
			see: string
			special_hour: {
				/**
				 * I​n​d​i​q​u​e​z​ ​à​ ​v​o​s​ ​c​l​i​e​n​t​s​ ​s​i​ ​v​o​u​s​ ​ê​t​e​s​ ​<​s​t​r​o​n​g​>​ ​o​u​v​e​r​t​ ​l​e​ ​{​d​a​t​e​}​ ​<​/​s​t​r​o​n​g​>​ ​p​o​u​r​ ​t​o​u​s​ ​v​o​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​ ​<​s​t​r​o​n​g​>​ ​(​{​e​v​e​n​t​N​a​m​e​}​)​.​ ​<​/​s​t​r​o​n​g​>
				 * @param {string} date
				 * @param {string} eventName
				 */
				text: RequiredParams<'date' | 'eventName'>
			}
			post_suggestion: {
				/**
				 * U​n​ ​é​v​è​n​e​m​e​n​t​ ​a​p​p​r​o​c​h​e​ ​:​ ​{​n​a​m​e​}​ ​l​e​ ​{​d​a​t​e​}​.
				 * @param {string} date
				 * @param {string} name
				 */
				text: RequiredParams<'date' | 'name'>
			}
			reviews: {
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​C​o​u​n​t​}​ ​d​e​ ​v​o​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​ ​o​n​t​ ​r​e​ç​u​ ​d​e​s​ ​a​v​i​s
				 * @param {number} restaurantCount
				 */
				title: RequiredParams<'restaurantCount'>
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​r​e​ç​u​ ​{​c​o​u​n​t​}​ ​{​{​n​o​u​v​e​l​|​n​o​u​v​e​a​u​x​}​}​ ​a​v​i​s
				 * @param {number} count
				 */
				text: RequiredParams<'count'>
			}
			comments: {
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​C​o​u​n​t​}​ ​d​e​ ​v​o​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​ ​o​n​t​ ​r​e​ç​u​ ​d​e​s​ ​c​o​m​m​e​n​t​a​i​r​e​s​ ​s​u​r​ ​l​e​s​ ​r​é​s​e​a​u​x​ ​s​o​c​i​a​u​x
				 * @param {number} restaurantCount
				 */
				multiple_restaurant_title: RequiredParams<'restaurantCount'>
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​a​ ​r​e​ç​u​ ​{​c​o​m​m​e​n​t​C​o​u​n​t​}​ ​c​o​m​m​e​n​t​a​i​r​e​s​ ​s​u​r​ ​l​e​s​ ​r​é​s​e​a​u​x​ ​s​o​c​i​a​u​x
				 * @param {number} commentCount
				 * @param {string} restaurantName
				 */
				multiple_comments_title: RequiredParams<'commentCount' | 'restaurantName'>
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​a​ ​r​e​ç​u​ ​u​n​ ​c​o​m​m​e​n​t​a​i​r​e​ ​s​u​r​ ​l​e​s​ ​r​é​s​e​a​u​x​ ​s​o​c​i​a​u​x
				 * @param {string} restaurantName
				 */
				single_comment_title: RequiredParams<'restaurantName'>
				/**
				 * @​{​a​u​t​h​o​r​D​i​s​p​l​a​y​N​a​m​e​}​ ​:​ ​{​t​e​x​t​}
				 * @param {string} authorDisplayName
				 * @param {string} text
				 */
				single_comment_text: RequiredParams<'authorDisplayName' | 'text'>
			}
			mentions: {
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​C​o​u​n​t​}​ ​d​e​ ​v​o​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​ ​o​n​t​ ​é​t​é​ ​m​e​n​t​i​o​n​n​é​s
				 * @param {number} restaurantCount
				 */
				multiple_restaurant_title: RequiredParams<'restaurantCount'>
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​a​ ​é​t​é​ ​m​e​n​t​i​o​n​n​é
				 * @param {string} restaurantName
				 */
				single_mention_title: RequiredParams<'restaurantName'>
				/**
				 * @​{​a​u​t​h​o​r​D​i​s​p​l​a​y​N​a​m​e​}​ ​:​ ​{​t​e​x​t​}
				 * @param {string} authorDisplayName
				 * @param {string} text
				 */
				mention_text_with_author: RequiredParams<'authorDisplayName' | 'text'>
			}
			messages: {
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​C​o​u​n​t​}​ ​d​e​ ​v​o​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s​ ​o​n​t​ ​r​e​ç​u​ ​d​e​s​ ​m​e​s​s​a​g​e​s
				 * @param {number} restaurantCount
				 */
				multiple_restaurant_title: RequiredParams<'restaurantCount'>
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​a​ ​r​e​ç​u​ ​{​m​e​s​s​a​g​e​C​o​u​n​t​}​ ​m​e​s​s​a​g​e​s
				 * @param {number} messageCount
				 * @param {string} restaurantName
				 */
				multiple_message_title: RequiredParams<'messageCount' | 'restaurantName'>
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​a​ ​r​e​ç​u​ ​u​n​ ​m​e​s​s​s​a​g​e
				 * @param {string} restaurantName
				 */
				single_message_title: RequiredParams<'restaurantName'>
				/**
				 * {​s​e​n​d​e​r​N​a​m​e​}​ ​:​ ​{​t​e​x​t​}
				 * @param {string} senderName
				 * @param {string} text
				 */
				single_message_text: RequiredParams<'senderName' | 'text'>
			}
			post_error: {
				/**
				 * U​n​e​ ​e​r​r​e​u​r​ ​e​s​t​ ​s​u​r​v​e​n​u​e
				 */
				error_occured: string
				/**
				 * V​o​t​r​e​ ​p​o​s​t​ ​n​'​a​ ​p​a​s​ ​p​u​ ​ê​t​r​e​ ​p​u​b​l​i​é​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
				 * @param {string} restaurantName
				 */
				text: RequiredParams<'restaurantName'>
			}
			info_error: {
				/**
				 * E​r​r​e​u​r​ ​l​o​r​s​ ​d​e​ ​l​a​ ​m​i​s​e​ ​à​ ​j​o​u​r​ ​d​e​ ​v​o​s​ ​i​n​f​o​r​m​a​t​i​o​n​s​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
				 * @param {string} restaurantName
				 */
				text: RequiredParams<'restaurantName'>
			}
		}
	}
}

export type TranslationFunctions = {
	common: {
		/**
		 * Bonjour {name},
		 */
		hello: (arg: { name: string }) => LocalizedString
		/**
		 * Bonjour,
		 */
		simple_hello: () => LocalizedString
		/**
		 * Se désabonner
		 */
		unsubscribe: () => LocalizedString
		/**
		 * À très bientôt,
		 */
		goodbye: () => LocalizedString
		/**
		 * Merci!
		 */
		thank_you: () => LocalizedString
		langs: {
			/**
			 * Afrikaans
			 */
			af: () => LocalizedString
			/**
			 * Amharique
			 */
			am: () => LocalizedString
			/**
			 * Arabe
			 */
			ar: () => LocalizedString
			/**
			 * Azéri
			 */
			az: () => LocalizedString
			/**
			 * Biélorusse
			 */
			be: () => LocalizedString
			/**
			 * Bulgare
			 */
			bg: () => LocalizedString
			/**
			 * Bengali
			 */
			bn: () => LocalizedString
			/**
			 * Bosniaque
			 */
			bs: () => LocalizedString
			/**
			 * Catalan
			 */
			ca: () => LocalizedString
			/**
			 * Cebuano
			 */
			ceb: () => LocalizedString
			/**
			 * Corse
			 */
			co: () => LocalizedString
			/**
			 * Tchèque
			 */
			cs: () => LocalizedString
			/**
			 * Gallois
			 */
			cy: () => LocalizedString
			/**
			 * Danois
			 */
			da: () => LocalizedString
			/**
			 * Allemand
			 */
			de: () => LocalizedString
			/**
			 * Grec
			 */
			el: () => LocalizedString
			/**
			 * Anglais
			 */
			en: () => LocalizedString
			/**
			 * Espéranto
			 */
			eo: () => LocalizedString
			/**
			 * Espagnol
			 */
			es: () => LocalizedString
			/**
			 * Estonien
			 */
			et: () => LocalizedString
			/**
			 * Basque
			 */
			eu: () => LocalizedString
			/**
			 * Persan
			 */
			fa: () => LocalizedString
			/**
			 * Finnois
			 */
			fi: () => LocalizedString
			/**
			 * Français
			 */
			fr: () => LocalizedString
			/**
			 * Frison
			 */
			fy: () => LocalizedString
			/**
			 * Irlandais
			 */
			ga: () => LocalizedString
			/**
			 * Gaélique écossais
			 */
			gd: () => LocalizedString
			/**
			 * Galicien
			 */
			gl: () => LocalizedString
			/**
			 * Gujarati
			 */
			gu: () => LocalizedString
			/**
			 * Haoussa
			 */
			ha: () => LocalizedString
			/**
			 * Hawaïen
			 */
			haw: () => LocalizedString
			/**
			 * Hébreu
			 */
			he: () => LocalizedString
			/**
			 * Hindi
			 */
			hi: () => LocalizedString
			/**
			 * Hmong
			 */
			hmn: () => LocalizedString
			/**
			 * Croate
			 */
			hr: () => LocalizedString
			/**
			 * Créole haïtien
			 */
			ht: () => LocalizedString
			/**
			 * Hongrois
			 */
			hu: () => LocalizedString
			/**
			 * Arménien
			 */
			hy: () => LocalizedString
			/**
			 * Indonésien
			 */
			id: () => LocalizedString
			/**
			 * Igbo
			 */
			ig: () => LocalizedString
			/**
			 * Islandais
			 */
			is: () => LocalizedString
			/**
			 * Italien
			 */
			it: () => LocalizedString
			/**
			 * Hébreu
			 */
			iw: () => LocalizedString
			/**
			 * Japonais
			 */
			ja: () => LocalizedString
			/**
			 * Javanais
			 */
			jw: () => LocalizedString
			/**
			 * Géorgien
			 */
			ka: () => LocalizedString
			/**
			 * Kazakh
			 */
			kk: () => LocalizedString
			/**
			 * Khmer
			 */
			km: () => LocalizedString
			/**
			 * Kannada
			 */
			kn: () => LocalizedString
			/**
			 * Coréen
			 */
			ko: () => LocalizedString
			/**
			 * Kurde (kurmanji)
			 */
			ku: () => LocalizedString
			/**
			 * Kirghize
			 */
			ky: () => LocalizedString
			/**
			 * Latin
			 */
			la: () => LocalizedString
			/**
			 * Luxembourgeois
			 */
			lb: () => LocalizedString
			/**
			 * Laotien
			 */
			lo: () => LocalizedString
			/**
			 * Lituanien
			 */
			lt: () => LocalizedString
			/**
			 * Letton
			 */
			lv: () => LocalizedString
			/**
			 * Malgache
			 */
			mg: () => LocalizedString
			/**
			 * Maori
			 */
			mi: () => LocalizedString
			/**
			 * Macédonien
			 */
			mk: () => LocalizedString
			/**
			 * Malayalam
			 */
			ml: () => LocalizedString
			/**
			 * Mongol
			 */
			mn: () => LocalizedString
			/**
			 * Marathi
			 */
			mr: () => LocalizedString
			/**
			 * Malais
			 */
			ms: () => LocalizedString
			/**
			 * Maltais
			 */
			mt: () => LocalizedString
			/**
			 * Birman (birmanie)
			 */
			my: () => LocalizedString
			/**
			 * Népalais
			 */
			ne: () => LocalizedString
			/**
			 * Néerlandais
			 */
			nl: () => LocalizedString
			/**
			 * Norvégien
			 */
			no: () => LocalizedString
			/**
			 * Chichewa
			 */
			ny: () => LocalizedString
			/**
			 * Odia
			 */
			or: () => LocalizedString
			/**
			 * Pendjabi
			 */
			pa: () => LocalizedString
			/**
			 * Polonais
			 */
			pl: () => LocalizedString
			/**
			 * Pachto
			 */
			ps: () => LocalizedString
			/**
			 * Portugais
			 */
			pt: () => LocalizedString
			/**
			 * Roumain
			 */
			ro: () => LocalizedString
			/**
			 * Russe
			 */
			ru: () => LocalizedString
			/**
			 * Sindhi
			 */
			sd: () => LocalizedString
			/**
			 * Cinghalais
			 */
			si: () => LocalizedString
			/**
			 * Slovaque
			 */
			sk: () => LocalizedString
			/**
			 * Slovène
			 */
			sl: () => LocalizedString
			/**
			 * Samoan
			 */
			sm: () => LocalizedString
			/**
			 * Shona
			 */
			sn: () => LocalizedString
			/**
			 * Somali
			 */
			so: () => LocalizedString
			/**
			 * Albanais
			 */
			sq: () => LocalizedString
			/**
			 * Serbe
			 */
			sr: () => LocalizedString
			/**
			 * Sesotho
			 */
			st: () => LocalizedString
			/**
			 * Soundanais
			 */
			su: () => LocalizedString
			/**
			 * Suédois
			 */
			sv: () => LocalizedString
			/**
			 * Swahili
			 */
			sw: () => LocalizedString
			/**
			 * Tamoul
			 */
			ta: () => LocalizedString
			/**
			 * Télougou
			 */
			te: () => LocalizedString
			/**
			 * Tadjik
			 */
			tg: () => LocalizedString
			/**
			 * Thaï
			 */
			th: () => LocalizedString
			/**
			 * Filipino
			 */
			tl: () => LocalizedString
			/**
			 * Turc
			 */
			tr: () => LocalizedString
			/**
			 * Ouïghour
			 */
			ug: () => LocalizedString
			/**
			 * Ukrainien
			 */
			uk: () => LocalizedString
			/**
			 * Indéterminé
			 */
			undetermined: () => LocalizedString
			/**
			 * Ourdou
			 */
			ur: () => LocalizedString
			/**
			 * Ouzbek
			 */
			uz: () => LocalizedString
			/**
			 * Vietnamien
			 */
			vi: () => LocalizedString
			/**
			 * Xhosa
			 */
			xh: () => LocalizedString
			/**
			 * Yiddish
			 */
			yi: () => LocalizedString
			/**
			 * Yoruba
			 */
			yo: () => LocalizedString
			/**
			 * Chinois simplifié
			 */
			'zh-CN': () => LocalizedString
			/**
			 * Chinois traditionnel
			 */
			'zh-TW': () => LocalizedString
			/**
			 * Zoulou
			 */
			zu: () => LocalizedString
		}
	}
	user: {
		password_reset: {
			/**
			 * Réinitialiser
			 */
			change: () => LocalizedString
			/**
			 * Vous avez demandé à changer votre mot de passe.
			 */
			title: () => LocalizedString
			/**
			 * Cliquez sur le lien ci-dessous afin de saisir votre nouveau mot de passe:
			 */
			content: () => LocalizedString
			/**
			 * Attention ce lien n'est valable qu'une seule fois et que dans l'heure qui suit !
			 */
			warning: () => LocalizedString
		}
		account_confirmation: {
			/**
			 * Un compte MalouApp a été créé pour vous.
			 */
			title: () => LocalizedString
			/**
			 * Se connecter
			 */
			cta: () => LocalizedString
			/**
			 * Attention ce lien n'est valable qu'une seule fois et que dans l'heure qui suit !
			 */
			warning: () => LocalizedString
		}
		wrong_platform_access: {
			/**
			 * Les accès que vous avez renseignés pour la connexion à {platformName} n'ont pas pu être vérifiés.
			 */
			content: (arg: { platformName: string }) => LocalizedString
			/**
			 * Renseigner de nouveaux accès
			 */
			cta: () => LocalizedString
			/**
			 * Nous restons à votre disposition si besoin !
			 */
			thanks: () => LocalizedString
		}
	}
	feedback: {
		/**
		 * {name} a ajouté une nouvelle remarque sur votre post {postStatus} pour le {date} à {time} sur {fullPlatformKeys} de l'établissement {restaurantName}.
		 */
		user_commented: (arg: { date: string, fullPlatformKeys: string, name: string, postStatus: string, restaurantName: string, time: string }) => LocalizedString
		/**
		 * Répondre à la remarque
		 */
		check_feedback: () => LocalizedString
		/**
		 * {name} a fermé la discussion sur votre post {postStatus} pour le {date} à {time} sur {fullPlatformKeys} de l'établissement {restaurantName}.
		 */
		user_closed_feedback_thread: (arg: { date: string, fullPlatformKeys: string, name: string, postStatus: string, restaurantName: string, time: string }) => LocalizedString
		/**
		 * Consulter le post
		 */
		check_post: () => LocalizedString
		/**
		 * Vous ne voulez plus recevoir de mail concernant les retours ?
		 */
		wish_to_unsubscribe: () => LocalizedString
		/**
		 * Désabonnez-vous
		 */
		unsubscribe: () => LocalizedString
		/**
		 * {name} a réouvert la discussion sur votre post {postStatus} pour le {date} à {time} sur {fullPlatformKeys} de l'établissement {restaurantName}.
		 */
		user_opened_feedback_thread: (arg: { date: string, fullPlatformKeys: string, name: string, postStatus: string, restaurantName: string, time: string }) => LocalizedString
	}
	permissions: {
		revoked_connection: {
			/**
			 * Il semble que la connexion {platformName} sur l'établissement {restaurantName} soit rompue.
			 */
			title: (arg: { platformName: string, restaurantName: string }) => LocalizedString
			/**
			 * Il semble que la connexion {platformName} soit rompue sur : {restaurantNames}
			 */
			title_multiple_restaurants: (arg: { platformName: string, restaurantNames: string }) => LocalizedString
			/**
			 * Les fonctionnalités liées à ces plateformes risquent de ne plus fonctionner correctement.
			 */
			content: () => LocalizedString
			/**
			 * Rétablir la connexion
			 */
			cta: () => LocalizedString
		}
	}
	review_booster: {
		verification: {
			/**
			 * Veuillez cliquer sur le lien ci-dessous pour valider votre adresse email pour l'envoi de votre campagne sur la MalouApp.
			 */
			content: () => LocalizedString
		}
		client_review_booster: {
			/**
			 * Votre avis sur : {restaurantName} !
			 */
			title: (arg: { restaurantName: string }) => LocalizedString
			/**
			 * Vous ne voulez plus recevoir des messages de {restaurantName} ?
			 */
			no_more_messages: (arg: { restaurantName: string }) => LocalizedString
			/**
			 * Nous espérons que vous avez apprécié votre expérience chez nous et nous vous serions reconnaissants de nous faire part de votre avis ! Cela ne prend que quelques secondes, et il vous suffit simplement de cliquer sur le lien ci-dessous.
			 */
			content: () => LocalizedString
			/**
			 *  Il sera lu avec la grand attention, que ce soir pour féliciter nos équipes ou pour prendre en compte vos éventuelles remarqes et vous apporter pleine satisfaction lors de votre prochaine visite.
			 */
			content2: () => LocalizedString
		}
	}
	posts: {
		expired_location: {
			/**
			 * Vous aviez programmé un post pour le {publicationDate} sur {platformName}.
			 */
			content: (arg: { platformName: string, publicationDate: string }) => LocalizedString
			/**
			 * Le post a bien été publié, cependant le lieu que vous aviez tagué sur le post était introuvable, le post a par conséquent été publié sans lieu associé. Nous recommandons de taguer des lieux sur vos posts afin d'améliorer votre référencement local.
			 */
			content2: () => LocalizedString
			/**
			 * Consulter le post
			 */
			cta: () => LocalizedString
			/**
			 * Rendez-vous sur la
			 */
			content3: () => LocalizedString
			/**
			 * pour vérifier vos prochains posts programmés.
			 */
			content4: () => LocalizedString
		}
		error_publication: {
			/**
			 * Une erreur est survenue lors de la publication de votre post.
			 */
			title: () => LocalizedString
			/**
			 * Une erreur est survenue lors de la publication de votre story.
			 */
			storyTitle: () => LocalizedString
			/**
			 * Votre story n’a pas pu être publiée sur {fullPlatformKey} pour {restaurantName}.
			 */
			storyContent: (arg: { fullPlatformKey: string, restaurantName: string }) => LocalizedString
			/**
			 * Votre story du {date} à {time} n’a pas pu être publiée sur {fullPlatformKey} pour {restaurantName}.
			 */
			storyContentWithDateTime: (arg: { date: string, fullPlatformKey: string, restaurantName: string, time: string }) => LocalizedString
			/**
			 * Le post n'a pas pu être publié sur votre compte {fullPlatformKey} pour {restaurantName}.
			 */
			content: (arg: { fullPlatformKey: string, restaurantName: string }) => LocalizedString
			/**
			 * Le post du {date} à {time} n'a pas pu être publié sur votre compte {fullPlatformKey} pour {restaurantName}.
			 */
			contentWithDateTime: (arg: { date: string, fullPlatformKey: string, restaurantName: string, time: string }) => LocalizedString
		}
	}
	ai: {
		api_hard_limit: {
			/**
			 * Vous disposez de {hardLimit} utilisations de l'IA par mois pour votre établissement {restaurantName} ! Si vous avez besoin de plus de crédits, discutez-en avec votre contact privilégié chez Malou.
			 */
			content: (arg: { hardLimit: number, restaurantName: string }) => LocalizedString
		}
	}
	reports: {
		common: {
			/**
			 * Notification
			 */
			notification: () => LocalizedString
			concerned_restaurants_number: {
				/**
				 * 1 établissement
				 */
				one: () => LocalizedString
				/**
				 * {data} établissements
				 */
				multiple: (arg: { data: number }) => LocalizedString
			}
			global_focus: {
				/**
				 * Focus global sur 1 établissement
				 */
				one: () => LocalizedString
				/**
				 * Focus global sur {data} établissements
				 */
				multiple: (arg: { data: number }) => LocalizedString
			}
			/**
			 * Rapport Quotidien des Avis
			 */
			daily_reviews_report: () => LocalizedString
			/**
			 * Rapport Hebdomadaire des Avis
			 */
			weekly_reviews_report: () => LocalizedString
			weekly_performance_report: {
				/**
				 * Rapport Hebdomadaire
				 */
				normal: () => LocalizedString
				/**
				 * Rapport Hebdomadaire Groupé
				 */
				grouped: () => LocalizedString
			}
			monthly_performance_report: {
				/**
				 * Rapport Mensuel de Performance
				 */
				normal: () => LocalizedString
				/**
				 * Rapport Mensuel Groupé
				 */
				grouped: () => LocalizedString
			}
			/**
			 * Période précédente : {data} avis
			 */
			previous_period: (arg: { data: number }) => LocalizedString
			/**
			 *  note à ce jour
			 */
			to_this_time: () => LocalizedString
		}
		events: {
			title: {
				/**
				 * 1 événement dans les prochains jours
				 */
				one: () => LocalizedString
				/**
				 * {data} événements dans les prochains jours
				 */
				multiple: (arg: { data: number }) => LocalizedString
			}
			holiday_reminder: {
				/**
				 * N'oubliez pas de mettre à jour vos horaires d'ouverture pour le {date} qui est férié.
				 */
				one: (arg: { date: string }) => LocalizedString
				/**
				 * N'oubliez pas de mettre à jour vos horaires d'ouverture pour les {dates} et {lastDate} qui sont fériés.
				 */
				multiple: (arg: { dates: string, lastDate: string }) => LocalizedString
				/**
				 * Remontez dans les recherches en confirmant à Google que vous êtes ouverts
				 */
				subtitle: () => LocalizedString
			}
			/**
			 * Jour férié
			 */
			holiday: () => LocalizedString
		}
		reviews: {
			/**
			 * Avis
			 */
			title: () => LocalizedString
			/**
			 * Vous n'avez reçu aucun avis hier.
			 */
			no_daily_reviews: () => LocalizedString
			/**
			 * Vous n'avez reçu aucun avis la semaine dernière.
			 */
			no_weekly_reviews: () => LocalizedString
			best_restaurant: {
				/**
				 * L'établissement qui a récolté les meilleurs avis
				 */
				one: () => LocalizedString
				/**
				 * Vos {data} établissements qui ont récolté les meilleurs avis
				 */
				multiple: (arg: { data: number }) => LocalizedString
			}
			worst_restaurant: {
				/**
				 * L'établissement qui a récolté les moins bons avis
				 */
				one: () => LocalizedString
				/**
				 * Vos {data} établissements qui ont récolté les moins bons avis
				 */
				multiple: (arg: { data: number }) => LocalizedString
			}
			/**
			 * Améliorez votre référencement et vos relations client en répondant rapidement à vos avis.
			 */
			answer_client_reviews_text: () => LocalizedString
			/**
			 * (dont {totalLowReviews} avis, 1,2 ou 3 étoiles)
			 */
			low_reviews_description: (arg: { totalLowReviews: number }) => LocalizedString
			/**
			 * Avis non répondus
			 */
			not_answered_title: () => LocalizedString
			not_answered: {
				/**
				 * (1 non répondu)
				 */
				one: () => LocalizedString
				/**
				 * ({data} non répondus)
				 */
				multiple: (arg: { data: number }) => LocalizedString
			}
			/**
			 * moyenne
			 */
			average: () => LocalizedString
			/**
			 * Analyse sémantique des avis par catégorie
			 */
			semantic_analysis: () => LocalizedString
			sentiments: {
				/**
				 * Sentiments positifs
				 */
				positive: () => LocalizedString
				/**
				 * Sentiments négatifs
				 */
				negative: () => LocalizedString
			}
			monthly: {
				/**
				 * Vous avez gagné plus d'avis que le mois dernier
				 */
				gained: () => LocalizedString
				/**
				 * Vous avez gagné moins d'avis que le mois dernier
				 */
				lost: () => LocalizedString
				/**
				 * Vous avez gagné autant d'avis que le mois dernier
				 */
				same: () => LocalizedString
				/**
				 * Vous n'avez pas reçu d'avis le mois dernier
				 */
				none: () => LocalizedString
			}
			weekly: {
				/**
				 * Vous avez gagné plus d'avis que la semaine dernière
				 */
				gained: () => LocalizedString
				/**
				 * Vous avez gagné moins d'avis que la semaine dernière
				 */
				lost: () => LocalizedString
				/**
				 * Vous avez gagné autant d'avis que la semaine dernière
				 */
				same: () => LocalizedString
				/**
				 * Vous n'avez pas reçu d'avis la semaine dernière
				 */
				none: () => LocalizedString
			}
			received: {
				/**
				 * 1 avis
				 */
				one: () => LocalizedString
				/**
				 * {data} avis
				 */
				multiple: (arg: { data: number }) => LocalizedString
			}
			/**
			 * {data} avis sont en attente de réponse
			 */
			pending: (arg: { data: number }) => LocalizedString
			/**
			 * sur {data} avis reçus
			 */
			total_received: (arg: { data: number }) => LocalizedString
			/**
			 * Tous vos avis ont été répondus
			 */
			all_answered: () => LocalizedString
		}
		booster: {
			/**
			 * Totems
			 */
			totems: () => LocalizedString
			/**
			 * Avez-vous testé les totems pour booster votre récolte d'avis ?
			 */
			notice_try: () => LocalizedString
			/**
			 * Utilisez-vous les totems pour booster votre récolte d'avis ?
			 */
			notice_use: () => LocalizedString
			'with': {
				/**
				 * aux totems
				 */
				totems: () => LocalizedString
				/**
				 * aux roues de la fortune
				 */
				wof: () => LocalizedString
				/**
				 * aux boosters
				 */
				both: () => LocalizedString
			}
			/**
			 * Vous avez récupéré {reviewsNumber} avis en plus grâce {type}
			 */
			reviews_gained: (arg: { reviewsNumber: number, type: string }) => LocalizedString
			/**
			 * L'établissement qui a reçu le plus d'avis grâce {type} 
			 */
			best_restaurant: (arg: { type: string }) => LocalizedString
			/**
			 * L'établissement qui a reçu le moins d'avis grâce {type} 
			 */
			worst_restaurant: (arg: { type: string }) => LocalizedString
		}
		platforms: {
			disappointed_client: {
				/**
				 * Votre client le plus déçu sur {platformName}
				 */
				one: (arg: { platformName: string }) => LocalizedString
				/**
				 * Vos {clientsCount} clients les plus déçus sur {platformName}
				 */
				many: (arg: { clientsCount: number, platformName: string }) => LocalizedString
			}
			best_reviews: {
				/**
				 * Votre meilleur avis {platformName}
				 */
				one: (arg: { platformName: string }) => LocalizedString
				/**
				 * Vos {clientsCount} meilleurs avis {platformName}
				 */
				many: (arg: { clientsCount: number, platformName: string }) => LocalizedString
			}
			/**
			 * Assurez-vous de répondre à toutes les demandes de réservation ou d'information.
			 */
			manage_requests_text: () => LocalizedString
			/**
			 * Abonnés
			 */
			subscribers: () => LocalizedString
			/**
			 * Impressions
			 */
			impressions: () => LocalizedString
			engagement: {
				/**
				 * Engagement
				 */
				title: () => LocalizedString
				rate: {
					/**
					 * Votre taux d'engagement sur {socialType} a augmenté
					 */
					up: (arg: { socialType: string }) => LocalizedString
					/**
					 * Votre taux d'engagement sur {socialType} a baissé
					 */
					down: (arg: { socialType: string }) => LocalizedString
					/**
					 * Votre taux d'engagement sur {socialType} est stable
					 */
					stable: (arg: { socialType: string }) => LocalizedString
				}
				/**
				 * L'établissement qui a eu le meilleur taux d'engagement
				 */
				best_restaurant: () => LocalizedString
				/**
				 * L'établissement qui a eu le taux d'engagement le plus bas
				 */
				worst_restaurant: () => LocalizedString
			}
			messages: {
				/**
				 * Messages reçus la semaine dernière
				 */
				title: () => LocalizedString
				not_answered: {
					/**
					 * Vous avez 1 message non répondu
					 */
					one: () => LocalizedString
					/**
					 * Vous avez {data} messages non répondus
					 */
					multiple: (arg: { data: number }) => LocalizedString
				}
			}
			publications: {
				/**
				 * Posts publiés cette semaine
				 */
				posted_this_week: () => LocalizedString
				weekly_best: {
					post: {
						/**
						 * Le post qui a fait le plus réagir cette semaine
						 */
						one: () => LocalizedString
						/**
						 * Le post qui a fait le plus réagir cette semaine parmi vos {data} publications
						 */
						multiple: (arg: { data: number }) => LocalizedString
					}
					reel: {
						/**
						 * Le reel qui a fait le plus réagir cette semaine
						 */
						one: () => LocalizedString
						/**
						 * Le reel qui a fait le plus réagir cette semaine parmi vos {data} publications
						 */
						multiple: (arg: { data: number }) => LocalizedString
					}
				}
				monthly_best: {
					post: {
						/**
						 * Le post qui a fait le plus réagir ce mois-ci
						 */
						one: () => LocalizedString
						/**
						 * Le post qui a fait le plus réagir ce mois-ci parmi vos {data} publications
						 */
						multiple: (arg: { data: number }) => LocalizedString
					}
					reel: {
						/**
						 * Le reel qui a fait le plus réagir ce mois-ci
						 */
						one: () => LocalizedString
						/**
						 * Le reel qui a fait le plus réagir ce mois-ci parmi vos {data} publications
						 */
						multiple: (arg: { data: number }) => LocalizedString
					}
				}
			}
			google: {
				/**
				 * L'établissement qui est le plus apparu sur Google
				 */
				best_restaurant: () => LocalizedString
				/**
				 * L'établissement qui est le moins apparu sur Google
				 */
				worst_restaurant: () => LocalizedString
				/**
				 * Apparitions de vos établissements sur Google
				 */
				visibility: () => LocalizedString
				/**
				 * Taux de conversion
				 */
				conversion_rate_title: () => LocalizedString
				/**
				 * Visiteurs qui effectuent une action
				 */
				conversion_rate_sub_text: () => LocalizedString
				/**
				 * Actions
				 */
				actions: () => LocalizedString
				/**
				 * Mots-clés où votre positionnement a évolué
				 */
				positioning_improvement: () => LocalizedString
				conversion_rate: {
					/**
					 * Votre nombre d'actions Google a augmenté
					 */
					up: () => LocalizedString
					/**
					 * Votre nombre d'actions Google a baissé
					 */
					down: () => LocalizedString
					/**
					 * Votre nombre d'actions Google est stable
					 */
					stable: () => LocalizedString
				}
			}
		}
		average_rating: {
			/**
			 * Note moyenne
			 */
			title: () => LocalizedString
			/**
			 * Note moyenne de vos avis
			 */
			long_title: () => LocalizedString
			/**
			 * (sur toutes vos plateformes)
			 */
			description: () => LocalizedString
			/**
			 * moyenne des avis reçus
			 */
			long_title_variant: () => LocalizedString
		}
		rank: {
			/**
			 * 1er
			 */
			first: () => LocalizedString
			/**
			 * 2ème
			 */
			second: () => LocalizedString
			/**
			 * 3ème
			 */
			third: () => LocalizedString
			/**
			 * {rank}ème
			 */
			other: (arg: { rank: number }) => LocalizedString
		}
		buttons: {
			/**
			 * Répondre
			 */
			answer_short: () => LocalizedString
			/**
			 * Répondre à mes avis
			 */
			answer_reviews: () => LocalizedString
			/**
			 * Voir les avis
			 */
			check_reviews: () => LocalizedString
			/**
			 * Voir le détail
			 */
			more_details: () => LocalizedString
			/**
			 * Modifier mes horaires
			 */
			update_time: () => LocalizedString
			/**
			 * Créer un post
			 */
			create_post: () => LocalizedString
			/**
			 * En savoir plus
			 */
			learn_more: () => LocalizedString
		}
		feedback: {
			/**
			 * Que pensez-vous de ce rapport ?
			 */
			title: () => LocalizedString
			/**
			 * Donnez-nous votre avis pour l'améliorer selon vos besoins !
			 */
			subtitle: () => LocalizedString
			/**
			 * Donner mon avis
			 */
			button_text: () => LocalizedString
		}
	}
	wheels_of_fortune: {
		empty_stock: {
			/**
			 * Modifier mes stocks
			 */
			edit_stocks: () => LocalizedString
			/**
			 * Votre cadeau "{giftName}" est en rupture de stock sur votre roue de la fortune du restaurant "{restaurantName}". Si vous souhaitez que vos clients continuent d'en bénéficier, modifiez vos stocks dans les options avancées.
			 */
			your_gift_stock_is_empty: (arg: { giftName: string, restaurantName: string }) => LocalizedString
		}
		gift_expires_soon: {
			/**
			 * Votre coupon cadeau pour <b>{gift} expire bientôt !</b>
			 */
			your_giveaway_expires_soon: (arg: { gift: string }) => LocalizedString
			/**
			 * Vous l'avez gagné dans l'établissement <b>{restaurantName}, {restaurantAddress}.</b>
			 */
			meet_at_your_restaurant: (arg: { restaurantAddress: string, restaurantName: string }) => LocalizedString
			/**
			 * Des conditions s'appliquent : {conditions}.
			 */
			conditions: (arg: { conditions: string }) => LocalizedString
			/**
			 * Pour rappel, votre cadeau expire le {date}.
			 */
			expiration_warning: (arg: { date: string }) => LocalizedString
			/**
			 * Accéder à mon cadeau
			 */
			access_gift: () => LocalizedString
			/**
			 * La récupération de votre cadeau est <b>soumise à condition d'achat</b>.
			 */
			subject_to_purchase: () => LocalizedString
		}
		retrieve_gift: {
			/**
			 * Bravo, vous avez gagné <b>{gift}</b> lors de votre visite dans l'établissement <b>{businessName}, {businessAddress}</b>.
			 */
			you_have_won: (arg: { businessAddress: string, businessName: string, gift: string }) => LocalizedString
			/**
			 * Des conditions s'appliquent : {conditions}.
			 */
			conditions: (arg: { conditions: string }) => LocalizedString
			/**
			 * Pour rappel, votre cadeau est disponible à partir du {retrievalStartDate} et expire le {retrievalEndDate}.
			 */
			dates_reminder: (arg: { retrievalEndDate: string, retrievalStartDate: string }) => LocalizedString
			/**
			 * Récupérer mon cadeau
			 */
			pick_up_my_gift: () => LocalizedString
			/**
			 * La récupération de votre cadeau est <b>soumise à condition d'achat</b>.
			 */
			subject_to_purchase: () => LocalizedString
		}
		wof_live_tomorrow: {
			/**
			 * La roue de la fortune que vous avez programmée pour <b>{restaurantNames}</b> sera <b>activée demain</b>. Vos clients auront donc accès à votre roue via le lien et le QR code.
			 */
			scheduled_wof_live_tomorrow: (arg: { restaurantNames: string }) => LocalizedString
			/**
			 * Voir ma roue de la fortune
			 */
			see_my_wof: () => LocalizedString
			/**
			 * Vos totems sont également redirigés vers votre roue :
			 */
			totems: () => LocalizedString
		}
	}
	reviews: {
		download_reviews: {
			/**
			 * Export des Avis
			 */
			title: () => LocalizedString
			/**
			 * Vous n’avez pas reçu d’avis sur vos établissements.
			 */
			no_reviews_to_download: () => LocalizedString
			/**
			 * ({reviewsCount} Avis)
			 */
			reviews: (arg: { reviewsCount: number }) => LocalizedString
		}
		/**
		 * Modifié
		 */
		modified: () => LocalizedString
		/**
		 * *Traduit de la langue : {language}*
		 */
		translated_from: (arg: { language: string }) => LocalizedString
		intelligent_subjects: {
			/**
			 * Sujet sensible
			 */
			notificationTitle: () => LocalizedString
			/**
			 * Cher {receiver}, un sujet sensible a été détecté dans un avis sur 
			 */
			text: (arg: { receiver: unknown }) => LocalizedString
			/**
			 * Répondre à l'avis
			 */
			primary_button: () => LocalizedString
		}
	}
	platforms: {
		mapstr_reminder: {
			/**
			 * Vous avez fait la demande de connexion entre Malou et Mapstr Premium.
			 */
			title: () => LocalizedString
			/**
			 * Si vous êtes clients Mapstr Premium, votre clé de connexion Malou est maintenant disponible dans votre dashboard Mapstr, page "Publications", et vous pouvez finaliser la connexion.
			 */
			description: () => LocalizedString
			/**
			 * Finaliser la connexion Mapstr Premium
			 */
			primary_button: () => LocalizedString
		}
	}
	restaurant_diagnostic: {
		/**
		 * Diagnostic
		 */
		title: () => LocalizedString
		/**
		 * Votre diagnostic de visibilité en ligne pour {restaurantName} est prêt !
		 */
		text: (arg: { restaurantName: string }) => LocalizedString
		/**
		 * Voir mon diagnostic complet
		 */
		check_diagnostic: () => LocalizedString
	}
	notifications: {
		common: {
			/**
			 * Notification
			 */
			notification: () => LocalizedString
			/**
			 * Diagnostic
			 */
			diagnostic: () => LocalizedString
		}
		holidays: {
			/**
			 * Le <b>{date}</b> aura lieu un évènement important :
			 */
			title: (arg: { date: string }) => LocalizedString
			/**
			 * Indiquez à vos clients si vous êtes ouvert. Cela aidera à <b>améliorer votre position sur Google!</b>
			 */
			subtitle: () => LocalizedString
			/**
			 * Confirmer mes horaires
			 */
			confirm_hours: () => LocalizedString
		}
		reviews: {
			negative_review: {
				/**
				 * Bientôt <b>72 heures que cet avis négatif est sans réponse</b> sur <b>{restaurantName}.</b>
				 */
				title: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * Vous avez <b>{unansweredReviewsCount} avis négatifs en attente de réponse</b> sur <b>{restaurantName}.</b>
				 */
				title_with_multiple_reviews: (arg: { restaurantName: string, unansweredReviewsCount: number }) => LocalizedString
				/**
				 * {platformName} favorise les établissements qui répondent rapidement aux avis.
				 */
				warning_text: (arg: { platformName: string }) => LocalizedString
				/**
				 * <b>{reviewsCount} autres avis négatifs en attente de réponse</b> sur {restaurantsName}.
				 */
				other_restaurants_reminder: (arg: { restaurantsName: string, reviewsCount: number }) => LocalizedString
				/**
				 * Répondre à vos avis
				 */
				reply_to_reviews: () => LocalizedString
				/**
				 * 1 autre avis négatif en attente de réponse sur {restaurantsCount} établissements
				 */
				remaining_restaurant: (arg: { restaurantsCount: number }) => LocalizedString
				/**
				 * {reviewsCount} autres avis négatifs en attente de réponse sur {restaurantsCount} établissements
				 */
				remaining_restaurants: (arg: { restaurantsCount: number, reviewsCount: number }) => LocalizedString
			}
		}
		event_post: {
			/**
			 * Vous n’avez pas posté sur {restaurants} depuis un moment.
			 */
			title: (arg: { restaurants: string }) => LocalizedString
			/**
			 * <strong>Vous n’avez pas posté sur {restaurants}, et {remainingRestaurantsCount} autres établissements</strong> depuis un moment.
			 */
			many_restaurants_title: (arg: { remainingRestaurantsCount: number, restaurants: string }) => LocalizedString
			/**
			 * Un nouvel évènement approche, pourquoi ne pas prévenir vos clients de ce que vous prévoyez pour l’occasion ?
			 */
			description: () => LocalizedString
			/**
			 * Créer un post
			 */
			create: () => LocalizedString
		}
		roi: {
			/**
			 * Que le temps passe vite, voilà plus de 4 mois que vous travaillez avec Malou!
			 */
			title: () => LocalizedString
			/**
			 * Nous avons estimé <strong> vos gains grâce au marketing </strong> pendant ce temps, qu’en dites-vous ?
			 */
			restaurants_with_roi_settings: () => LocalizedString
			/**
			 * Souhaitez-vous découvrir <strong> vos gains grâce au marketing </strong> effectués jusqu'ici pour {restaurantsNames}? Remplissez vos informations sans plus attendre.
			 */
			restaurants_without_roi_settings: (arg: { restaurantsNames: string }) => LocalizedString
			/**
			 * Voir l’estimation de mes gains
			 */
			see: () => LocalizedString
		}
		summary: {
			/**
			 * Des choses se sont passées depuis votre dernière connexion ! Voici quelques notifications que vous auriez pu rater :
			 */
			main_text: () => LocalizedString
			/**
			 * Voir les autres notifications
			 */
			notifications_link_text: () => LocalizedString
			/**
			 * Voir
			 */
			see: () => LocalizedString
			special_hour: {
				/**
				 * Indiquez à vos clients si vous êtes <strong> ouvert le {date} </strong> pour tous vos établissements <strong> ({eventName}). </strong>
				 */
				text: (arg: { date: string, eventName: string }) => LocalizedString
			}
			post_suggestion: {
				/**
				 * Un évènement approche : {name} le {date}.
				 */
				text: (arg: { date: string, name: string }) => LocalizedString
			}
			reviews: {
				/**
				 * {restaurantCount} de vos établissements ont reçu des avis
				 */
				title: (arg: { restaurantCount: number }) => LocalizedString
				/**
				 * Vous avez reçu {count} {{nouvel|nouveaux}} avis
				 */
				text: (arg: { count: number }) => LocalizedString
			}
			comments: {
				/**
				 * {restaurantCount} de vos établissements ont reçu des commentaires sur les réseaux sociaux
				 */
				multiple_restaurant_title: (arg: { restaurantCount: number }) => LocalizedString
				/**
				 * {restaurantName} a reçu {commentCount} commentaires sur les réseaux sociaux
				 */
				multiple_comments_title: (arg: { commentCount: number, restaurantName: string }) => LocalizedString
				/**
				 * {restaurantName} a reçu un commentaire sur les réseaux sociaux
				 */
				single_comment_title: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * @{authorDisplayName} : {text}
				 */
				single_comment_text: (arg: { authorDisplayName: string, text: string }) => LocalizedString
			}
			mentions: {
				/**
				 * {restaurantCount} de vos établissements ont été mentionnés
				 */
				multiple_restaurant_title: (arg: { restaurantCount: number }) => LocalizedString
				/**
				 * {restaurantName} a été mentionné
				 */
				single_mention_title: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * @{authorDisplayName} : {text}
				 */
				mention_text_with_author: (arg: { authorDisplayName: string, text: string }) => LocalizedString
			}
			messages: {
				/**
				 * {restaurantCount} de vos établissements ont reçu des messages
				 */
				multiple_restaurant_title: (arg: { restaurantCount: number }) => LocalizedString
				/**
				 * {restaurantName} a reçu {messageCount} messages
				 */
				multiple_message_title: (arg: { messageCount: number, restaurantName: string }) => LocalizedString
				/**
				 * {restaurantName} a reçu un messsage
				 */
				single_message_title: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * {senderName} : {text}
				 */
				single_message_text: (arg: { senderName: string, text: string }) => LocalizedString
			}
			post_error: {
				/**
				 * Une erreur est survenue
				 */
				error_occured: () => LocalizedString
				/**
				 * Votre post n'a pas pu être publié sur {restaurantName}
				 */
				text: (arg: { restaurantName: string }) => LocalizedString
			}
			info_error: {
				/**
				 * Erreur lors de la mise à jour de vos informations sur {restaurantName}
				 */
				text: (arg: { restaurantName: string }) => LocalizedString
			}
		}
	}
}

export type Formatters = {}
