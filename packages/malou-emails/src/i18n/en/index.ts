import type { Translation } from ':i18n/i18n-types';

const en = {
    common: {
        hello: 'Hi {name},',
        simple_hello: 'Hi',
        unsubscribe: 'Unsubscribe',
        goodbye: 'See you soon,',
        thank_you: 'Thank you!',
        langs: {
            af: 'Afrikaans',
            am: 'Amharic',
            ar: 'Arabic',
            az: 'Azerbaijani',
            be: 'Belarusian',
            bg: 'Bulgarian',
            bn: 'Bengali',
            bs: 'Bosnian',
            ca: 'Catalan',
            ceb: 'Cebuano',
            co: 'Corsican',
            cs: 'Czech',
            cy: 'Welsh',
            da: 'Danish',
            de: 'German',
            el: 'Greek',
            en: 'English',
            eo: 'Esperanto',
            es: 'Spanish',
            et: 'Estonian',
            eu: 'Basque',
            fa: 'Persian',
            fi: 'Finnish',
            fr: 'French',
            fy: 'Frisian',
            ga: 'Irish',
            gd: 'Scots gaelic',
            gl: 'Galician',
            gu: 'Gujarati',
            ha: 'Hausa',
            haw: 'Hawaiian',
            he: 'Hebrew',
            hi: 'Hindi',
            hmn: 'Hmong',
            hr: 'Croatian',
            ht: 'Haitian creole',
            hu: 'Hungarian',
            hy: 'Armenian',
            id: 'Indonesian',
            ig: 'Igbo',
            is: 'Icelandic',
            it: 'Italian',
            iw: 'Hebrew',
            ja: 'Japanese',
            jw: 'Javanese',
            ka: 'Georgian',
            kk: 'Kazakh',
            km: 'Khmer',
            kn: 'Kannada',
            ko: 'Korean',
            ku: 'Kurdish (kurmanji)',
            ky: 'Kyrgyz',
            la: 'Latin',
            lb: 'Luxembourgish',
            lo: 'Lao',
            lt: 'Lithuanian',
            lv: 'Latvian',
            mg: 'Malagasy',
            mi: 'Maori',
            mk: 'Macedonian',
            ml: 'Malayalam',
            mn: 'Mongolian',
            mr: 'Marathi',
            ms: 'Malay',
            mt: 'Maltese',
            my: 'Myanmar (burmese)',
            ne: 'Nepali',
            nl: 'Dutch',
            no: 'Norwegian',
            ny: 'Chichewa',
            or: 'Odia',
            pa: 'Punjabi',
            pl: 'Polish',
            ps: 'Pashto',
            pt: 'Portuguese',
            ro: 'Romanian',
            ru: 'Russian',
            sd: 'Sindhi',
            si: 'Sinhala',
            sk: 'Slovak',
            sl: 'Slovenian',
            sm: 'Samoan',
            sn: 'Shona',
            so: 'Somali',
            sq: 'Albanian',
            sr: 'Serbian',
            st: 'Sesotho',
            su: 'Sundanese',
            sv: 'Swedish',
            sw: 'Swahili',
            ta: 'Tamil',
            te: 'Telugu',
            tg: 'Tajik',
            th: 'Thai',
            tl: 'Filipino',
            tr: 'Turkish',
            ug: 'Uyghur',
            uk: 'Ukrainian',
            undetermined: 'Undetermined',
            ur: 'Urdu',
            uz: 'Uzbek',
            vi: 'Vietnamese',
            xh: 'Xhosa',
            yi: 'Yiddish',
            yo: 'Yoruba',
            'zh-CN': 'Chinese (simplified)',
            'zh-TW': 'Chinese (traditional)',
            zu: 'Zulu',
        },
    },
    user: {
        password_reset: {
            change: 'Change password',
            title: 'You have requested to change your password.',
            content: 'Click the link below to enter your new password:',
            warning: 'Please note that this link is only valid once and within the next hour!',
        },
        account_confirmation: {
            title: 'A MalouApp account has been created for you.',
            cta: 'Log in',
            warning: 'Please note that this link is only valid once and within the next hour!',
        },
        wrong_platform_access: {
            content: 'The access credentials you provided for {platformName} could not be verified.',
            cta: 'Provide new credentials',
            thanks: 'Thank you and we remain available if needed!',
        },
    },
    feedback: {
        user_commented:
            '{name} added a new note to your post {postStatus} for {date} at {time} on {fullPlatformKeys} for {restaurantName}.',
        check_feedback: 'Reply to feedback',
        user_closed_feedback_thread:
            '{name} closed the discussion on your post {postStatus} for {date} at {time} on {fullPlatformKeys} for {restaurantName}.',
        check_post: 'View the post',
        wish_to_unsubscribe: 'Do you no longer want to receive emails regarding feedback?',
        unsubscribe: 'Unsubscribe',
        user_opened_feedback_thread:
            '{name} reopened the discussion on your post {postStatus} for {date} at {time} on {fullPlatformKeys} for {restaurantName}.',
    },
    permissions: {
        revoked_connection: {
            title: 'The {platformName} platform of {restaurantName} seems to have been disconnected.',
            title_multiple_restaurants: 'The {platformName} platform seems to have been disconnected on your locations: {restaurantNames}.',
            content: 'The features related to these platforms may not work properly.',
            cta: 'Reconnect',
        },
    },
    review_booster: {
        verification: {
            content: 'Please click on the link below to validate your email address for sending your campaign on MalouApp.',
        },
        client_review_booster: {
            title: 'Your review on: {restaurantName}!',
            no_more_messages: 'Do you no longer want to receive messages from {restaurantName}?',
            content:
                'We hope you enjoyed your experience with us and we would be grateful if you could share your feedback! It only takes a few seconds, and all you have to do is click on the link below.',
            content2:
                "Your feedback will be greatly appreciated, whether it's to praise our teams or to address any concerns and ensure your complete satisfaction on your next visit.",
        },
    },
    posts: {
        expired_location: {
            content: 'You had scheduled a post for {publicationDate} on {platformName}.',
            content2:
                'The post was successfully published, however, the location you tagged in the post was not found, so the post was published without an associated location. We recommend tagging locations in your posts to improve your local SEO.',
            cta: 'View the post',
            content3: 'Go to the',
            content4: 'to check your upcoming scheduled posts.',
        },
        error_publication: {
            title: 'An error occurred while publishing your post.',
            storyTitle: 'An error occurred while publishing your story.',
            storyContent: 'Your story could not be published on {fullPlatformKey} for {restaurantName}.',
            storyContentWithDateTime: 'Your story of {date} at {time} could not be published on {fullPlatformKey} for {restaurantName}.',
            content: 'Your post could not be published on {fullPlatformKey} for {restaurantName}.',
            contentWithDateTime: 'Your story of {date} at {time} could not be published on {fullPlatformKey} for {restaurantName}.',
        },
    },
    ai: {
        api_hard_limit: {
            content:
                'You have {hardLimit} AI usages per month for your {restaurantName} establishment! If you need more credits, discuss it with your dedicated contact at Malou.',
        },
    },
    reports: {
        common: {
            notification: 'Notification',
            concerned_restaurants_number: {
                one: '1 location',
                multiple: '{data} locations',
            },
            global_focus: {
                one: 'Focus on 1 locations',
                multiple: 'Focus on {data} locations',
            },
            daily_reviews_report: 'Daily Review Report',
            weekly_reviews_report: 'Weekly Review Report',
            weekly_performance_report: {
                normal: ' Weekly Performance Report',
                grouped: 'Grouped Weekly Performance Report',
            },
            monthly_performance_report: {
                normal: 'Monthly Performance Report',
                grouped: 'Grouped Monthly Performance Report',
            },
            previous_period: 'Previous period: {data} reviews',
            to_this_time: 'at this time',
        },
        events: {
            title: { one: '1 event in the next few days', multiple: '{data} events in the next few days' },
            holiday_reminder: {
                one: "Don't forget to update your opening hours for {date} which is a holiday.",
                multiple: "Don't forget to update your opening hours for {dates} and {lastDate} which are holidays.",
                subtitle: 'Move up the search results by confirming to Google that you are open for business',
            },
            holiday: 'Holiday',
        },
        reviews: {
            title: 'Reviews',
            no_daily_reviews: 'You did not get any reviews yesterday.',
            no_weekly_reviews: 'You did not get any reviews last week.',
            best_restaurant: {
                one: 'Your location that got the best reviews',
                multiple: 'Your {data} locations that got the best reviews',
            },
            worst_restaurant: {
                one: 'Your location that got the worst reviews',
                multiple: 'Your {data} locations that got the worst reviews',
            },
            answer_client_reviews_text: 'Gain more reviews and reply quickly to them to improve your SEO.',
            low_reviews_description: '(including {totalLowReviews} reviews, 1,2 or 3 stars)',
            not_answered_title: 'Reviews not answered',
            not_answered: { one: '(1 not answered)', multiple: '({data} not answered)' },
            average: 'average',
            semantic_analysis: 'Semantic analysis of reviews by category',
            sentiments: { positive: 'Positive sentiments', negative: 'Negative sentiments' },
            weekly: {
                gained: 'You have gained more reviews than last week',
                lost: 'You gained less reviews than last week',
                same: "You've gained as many reviews as last week",
                none: 'You got no reviews this week',
            },
            monthly: {
                gained: "You've gained more reviews than last month",
                lost: 'You gained fewer reviews than last month',
                same: 'You gained as many reviews as last month',
                none: 'You got no reviews this month',
            },
            received: { one: '1 review', multiple: '{data} reviews' },
            pending: '{data} reviews are waiting for a reply',
            total_received: 'out of {data} reviews received',
            all_answered: 'All your reviews were answered',
        },
        booster: {
            totems: 'Totems',
            notice_try: 'Have you tried totems to gain more reviews?',
            notice_use: 'Are you using totems to boost your review collection?',
            with: {
                totems: 'your totems',
                both: 'your boosters',
                wof: 'your wheels of fortune',
            },
            reviews_gained: 'You gained {reviewsNumber} more reviews thanks to {type}',
            best_restaurant: 'Your location that got the most reviews thanks to {type}',
            worst_restaurant: 'Your location that got the least reviews thanks to {type}',
        },
        platforms: {
            disappointed_client: {
                one: 'Your most disappointed customer on {platformName}',
                many: 'Your {clientsCount} most disappointed customers on {platformName}',
            },
            best_reviews: {
                one: 'Your best review on {platformName}',
                many: 'Your {clientsCount} best reviews on {platformName}',
            },
            manage_requests_text: 'Make sure you respond to all reservation or information requests.',
            subscribers: 'Followers',
            impressions: 'Impressions',
            engagement: {
                title: 'Engagement',
                rate: {
                    up: 'Your engagement rate on {socialType} has increased',
                    down: 'Your engagement rate on {socialType} has decreased',
                    stable: 'Your {socialType} engagement rate is stable',
                },
                best_restaurant: 'The restaurant with the best engagement rate',
                worst_restaurant: 'The establishment with the lowest engagement rate',
            },
            messages: {
                title: 'Messages received last week',
                not_answered: {
                    one: 'You have 1 unanswered messages',
                    multiple: 'You have {data} unanswered messages ',
                },
            },
            publications: {
                posted_this_week: 'Posts published this week',
                weekly_best: {
                    post: {
                        one: 'Your best post this week',
                        multiple: 'Your best post this week among the {data} posts you made',
                    },
                    reel: {
                        one: 'Your best reel this week',
                        multiple: 'Your best reel this week among the {data} reels you made',
                    },
                },
                monthly_best: {
                    post: {
                        one: 'Your best post this month',
                        multiple: 'Your best post this month among the {data} posts you made',
                    },
                    reel: {
                        one: 'Your best reel this month',
                        multiple: 'Your best reel this month among the {data} reels you made',
                    },
                },
            },
            google: {
                best_restaurant: 'Your location that appeared the most on Google',
                worst_restaurant: 'Your location that appeared the least on Google',
                visibility: 'Impressions of your locations on Google',
                conversion_rate_title: 'Conversion rate',
                conversion_rate_sub_text: 'Visitors who take an action',
                actions: 'Actions',
                positioning_improvement: 'Changes in keywords ranking',
                conversion_rate: {
                    up: 'Your Google conversion rate has increased',
                    down: 'Your Google conversion rate has decreased',
                    stable: 'Your Google conversion rate is stable',
                },
            },
        },
        average_rating: {
            title: 'Average rating',
            long_title: 'Average review rating',
            description: '(on all your platforms)',
            long_title_variant: 'average',
        },
        rank: {
            first: '1st',
            second: '2nd',
            third: '3rd',
            other: '{rank}th',
        },
        buttons: {
            answer_short: 'Answer',
            answer_reviews: 'Answer reviews',
            check_reviews: 'Go to reviews',
            more_details: 'View details',
            update_time: 'Update opening hours',
            create_post: 'Create a post',
            learn_more: 'Find out more',
        },
        feedback: {
            title: 'What do you think of this report?',
            subtitle: 'Give us your opinion to improve it according to your needs!',
            button_text: 'Send my feedback',
        },
    },
    wheels_of_fortune: {
        empty_stock: {
            edit_stocks: 'Edit stocks',
            your_gift_stock_is_empty:
                'Your gift "{giftName}" is out of stock on your wheel of fortune for your restaurant "{restaurantName}". If you want your customers to continue to benefit from it, modify your inventory in the advanced options.',
        },
        gift_expires_soon: {
            your_giveaway_expires_soon: 'Your gift voucher for {gift} is expiring soon!',
            meet_at_your_restaurant: 'You won it at <b>{restaurantName}, {restaurantAddress}.</b>',
            conditions: 'Conditions apply: {conditions}.',
            expiration_warning: 'Just a reminder, your gift expires on {date}.',
            access_gift: 'Access my gift',
            subject_to_purchase: 'Retrieving your gift is <b>subject to purchase</b>.',
        },
        retrieve_gift: {
            you_have_won:
                'Congratulations, you have won : <b>{gift}</b> during your visit to the business <b>{businessName}, {businessAddress}</b>.',
            conditions: 'Conditions apply: {conditions}.',
            dates_reminder: 'Remember, your gift is available from {retrievalStartDate} and expires on {retrievalEndDate}.',
            pick_up_my_gift: 'Pick up my gift',
            subject_to_purchase: 'Retrieving your gift is <b>subject to purchase</b>.',
        },
        wof_live_tomorrow: {
            scheduled_wof_live_tomorrow:
                "The wheel of fortune you've programmed for <b>{restaurantNames}</b> will be <b>activated tomorrow</b>. Your customers will then have access to your wheel via the link and QR code.",
            see_my_wof: 'See my wheel of fortune',
            totems: 'Your totems are also redirected to your wheel:',
        },
    },
    reviews: {
        download_reviews: {
            title: 'Review Extract',
            no_reviews_to_download: 'You have not received any reviews on your establishments.',
            reviews: '({reviewsCount} Review)',
        },
        modified: 'Modified',
        translated_from: '*Translated from language: {language}*',
        intelligent_subjects: {
            notificationTitle: 'Sensitive topic',
            text: 'Dear {receiver}, A sensitive topic was detected in a review on ',
            primary_button: 'Reply to review',
        },
    },
    platforms: {
        mapstr_reminder: {
            title: 'You have requested a connection between Malou and Mapstr Premium.',
            description:
                'If you are a Mapstr Premium customer, your Malou connection key is now available in your Mapstr dashboard, "Posts" page, and you can finalize the connection.',
            primary_button: 'Finalize Mapstr Premium connection',
        },
    },
    restaurant_diagnostic: {
        title: 'Diagnostic',
        text: 'Your online visibility diagnostic for {restaurantName} is ready!',
        check_diagnostic: 'View my complete diagnostic',
    },
    notifications: {
        common: {
            notification: 'Notification',
            diagnostic: 'Diagnostic',
        },
        holidays: {
            title: 'An event is coming on <b>{date}</b>:',
            subtitle: "Let your customers know if you're open. This helps <b>improve your Google ranking!</b>",
            confirm_hours: 'Confirm Opening Hours',
        },
        reviews: {
            negative_review: {
                title: 'It’s been <b>almost 72 hours since this review has been posted</b> on <b>{restaurantName}.</b>',
                title_with_multiple_reviews:
                    'There are <b>{unansweredReviewsCount} reviews</b> on <b>{restaurantName}</b> that need a response.',
                warning_text: '{platformName} favors establishments that respond quickly to reviews.',
                other_restaurants_reminder: 'There are <b>{reviewsCount} other reviews</b> that need a response on {restaurantsName}.',
                reply_to_reviews: 'Reply to reviews',
                remaining_restaurant: 'There is 1 other review that need a response on {restaurantsCount} locations',
                remaining_restaurants: 'There are {reviewsCount} other reviews that need a response on {restaurantsCount} locations',
            },
        },
        event_post: {
            title: 'You haven’t posted on {restaurants} for a while.',
            many_restaurants_title:
                '<strong>You haven’t posted on {restaurants}, and {remainingRestaurantsCount} other locations</strong> for a while.',
            description: 'An event is coming, why not tell your customers how you plan to celebrate?',
            create: 'Create a post',
        },
        roi: {
            title: 'Happy 4 months with Malou! 🎉 Today marks 4 months since our collaboration.',
            restaurants_with_roi_settings:
                'Here’s something we think you might appreciate, we’ve made a <strong>quick estimation of the customers you’ve acquired, and the time you’ve saved</strong> since our collaboration. Let us know what you think! 😉',
            restaurants_without_roi_settings:
                'We <strong>estimated the number of customers who went to {{restaurantsNames}} since then</strong>. We’ll just need a few things from you to make it more accurate!',
            see: 'View Estimated Earnings',
        },
        summary: {
            main_text: 'It’s been a while! Here are some notifications you might have missed:',
            notifications_link_text: 'View all other notifications',
            see: 'View',
            special_hour: {
                text: 'Let your customers know if you are <strong> open on {date} </strong> ! <strong> ({eventName}). </strong>',
            },
            post_suggestion: {
                text: 'An event is approaching: {name} on {date}.',
            },
            reviews: {
                title: '{restaurantCount} of your locations have received reviews',
                text: 'You have received {count} new reviews',
            },
            comments: {
                multiple_restaurant_title: '{restaurantCount} of your locations have received comments on social media',
                multiple_comments_title: '{restaurantName} has received {commentCount} comments on social media',
                single_comment_title: '{restaurantName} has received a comment on social media',
                single_comment_text: '@{authorDisplayName}: {text}',
            },
            mentions: {
                multiple_restaurant_title: '{restaurantCount} of your locations were mentioned on social media',
                single_mention_title: '{restaurantName} has been mentioned',
                mention_text_with_author: '@{authorDisplayName}: {text}',
            },
            messages: {
                multiple_restaurant_title: '{restaurantCount} of your locations have received messages',
                multiple_message_title: '{restaurantName} has received {messageCount} messages',
                single_message_title: '{restaurantName} received a message',
                single_message_text: '{senderName}: {text}',
            },
            post_error: {
                error_occured: 'An error has occurred',
                text: 'Your post could not be published on {restaurantName}',
            },
            info_error: {
                text: 'Error updating your information on {restaurantName}',
            },
        },
    },
} satisfies Translation;

export default en;
