import { Container, Text } from '@react-email/components';

import { ReviewIntelligentSubjectProps } from '@malou-io/package-dto';

import { BasicReviewBox, PrimaryButton } from ':shared/components';
import { Translation } from ':shared/services';

export const Content = (props: ReviewIntelligentSubjectProps) => {
    const { locale, receiver, link, restaurantName } = props;

    const translator = new Translation(locale).getTranslator();

    return (
        <Container className="px-4 mb-8">
            <Container className="m-0 mt-12 mb-3">
                <Text className="text-[12px] inline text-malou-color-text-2">
                    {translator.reviews.intelligent_subjects.text({ receiver })}
                </Text>
                <Text className="text-[12px] inline text-malou-color-text-1 font-bold">{restaurantName}</Text>
            </Container>

            <BasicReviewBox {...props}></BasicReviewBox>

            <PrimaryButton link={link} text={translator.reviews.intelligent_subjects.primary_button()}></PrimaryButton>
        </Container>
    );
};

export default Content;
