import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

import { EntityRepository } from './entity.repository';

// Test schema for the upsert race condition fix
const testSchema = new mongoose.Schema({
    key: { type: String, required: true, unique: true },
    value: { type: String, required: true },
    expiresAt: { type: Date, required: true },
});

interface ITestEntity {
    _id: mongoose.Types.ObjectId;
    key: string;
    value: string;
    expiresAt: Date;
}

const TestModel = mongoose.model<ITestEntity>('TestEntity', testSchema);

class TestRepository extends EntityRepository<ITestEntity> {
    constructor() {
        super(TestModel);
    }
}

describe('EntityRepository upsert race condition fix', () => {
    let mongoServer: MongoMemoryServer;
    let repository: TestRepository;

    beforeAll(async () => {
        mongoServer = await MongoMemoryServer.create();
        const mongoUri = mongoServer.getUri();
        await mongoose.connect(mongoUri);
        repository = new TestRepository();
    });

    afterAll(async () => {
        await mongoose.disconnect();
        await mongoServer.stop();
    });

    beforeEach(async () => {
        await TestModel.deleteMany({});
    });

    it('should handle concurrent upserts without duplicate key errors', async () => {
        const testKey = 'test-concurrent-key';
        const testValue1 = 'value1';
        const testValue2 = 'value2';
        const expiresAt = new Date(Date.now() + 60000);

        // Simulate concurrent upsert operations with the same key
        const upsertPromises = [
            repository.upsert({
                filter: { key: testKey },
                update: { key: testKey, value: testValue1, expiresAt },
            }),
            repository.upsert({
                filter: { key: testKey },
                update: { key: testKey, value: testValue2, expiresAt },
            }),
        ];

        // Both operations should complete without throwing duplicate key errors
        const results = await Promise.all(upsertPromises);

        // Verify that both operations completed successfully
        expect(results).toHaveLength(2);
        expect(results[0]).toBeDefined();
        expect(results[1]).toBeDefined();

        // Verify that only one document exists in the database
        const documents = await TestModel.find({ key: testKey });
        expect(documents).toHaveLength(1);

        // The final value should be one of the two values (last write wins)
        expect([testValue1, testValue2]).toContain(documents[0].value);
    });

    it('should handle multiple concurrent upserts with different keys', async () => {
        const expiresAt = new Date(Date.now() + 60000);
        const upsertPromises = Array.from({ length: 10 }, (_, i) =>
            repository.upsert({
                filter: { key: `test-key-${i}` },
                update: { key: `test-key-${i}`, value: `value-${i}`, expiresAt },
            })
        );

        // All operations should complete without errors
        const results = await Promise.all(upsertPromises);

        expect(results).toHaveLength(10);
        results.forEach((result) => {
            expect(result).toBeDefined();
            expect(result.key).toMatch(/^test-key-\d+$/);
        });

        // Verify that all documents were created
        const documents = await TestModel.find({});
        expect(documents).toHaveLength(10);
    });

    it('should update existing documents correctly', async () => {
        const testKey = 'existing-key';
        const initialValue = 'initial-value';
        const updatedValue = 'updated-value';
        const expiresAt = new Date(Date.now() + 60000);

        // Create initial document
        const initial = await repository.upsert({
            filter: { key: testKey },
            update: { key: testKey, value: initialValue, expiresAt },
        });

        expect(initial.value).toBe(initialValue);

        // Update the document
        const updated = await repository.upsert({
            filter: { key: testKey },
            update: { key: testKey, value: updatedValue, expiresAt },
        });

        expect(updated.value).toBe(updatedValue);
        expect(updated._id.toString()).toBe(initial._id.toString());

        // Verify only one document exists
        const documents = await TestModel.find({ key: testKey });
        expect(documents).toHaveLength(1);
        expect(documents[0].value).toBe(updatedValue);
    });

    it('should handle the cache scenario specifically', async () => {
        // Simulate the exact scenario from the KEYWORDS_SCORE cache
        const cacheKey = 'KEYWORDS_SCORE:/api/v1/keywords/score@1f05d70e59246a0e7df0b3eb2858d8a5f067608c';
        const cacheValue = JSON.stringify({ data: { score: 85, keywords: ['test'] } });
        const expiresAt = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000); // 5 days

        // Simulate multiple concurrent cache writes (as would happen with concurrent requests)
        const cachePromises = Array.from({ length: 5 }, () =>
            repository.upsert({
                filter: { key: cacheKey },
                update: { key: cacheKey, value: cacheValue, expiresAt },
            })
        );

        // All operations should complete without duplicate key errors
        const results = await Promise.allSettled(cachePromises);

        // All promises should resolve (no rejections due to duplicate key errors)
        results.forEach((result) => {
            expect(result.status).toBe('fulfilled');
        });

        // Verify only one cache entry exists
        const cacheEntries = await TestModel.find({ key: cacheKey });
        expect(cacheEntries).toHaveLength(1);
        expect(cacheEntries[0].value).toBe(cacheValue);
    });
});
