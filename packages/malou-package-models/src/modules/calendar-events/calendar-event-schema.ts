import { countryCodeWithAllOption } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const calendarEventJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'CalendarEvent',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        startDate: {
            type: 'string',
            format: 'date-time',
        },
        emoji: {
            type: 'string',
        },
        key: {
            type: 'string',
        },
        country: {
            enum: countryCodeWithAllOption,
            default: 'FR',
        },
        name: {
            $ref: '#/definitions/Name',
        },
        byDefault: {
            type: 'boolean',
            default: false,
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        example: {
            $ref: '#/definitions/Example',
        },
        ideas: {
            $ref: '#/definitions/Example',
        },
        isBankHoliday: {
            type: 'boolean',
        },
        shouldSuggestSpecialHourUpdate: {
            type: 'boolean',
            default: false,
        },
        shouldSuggestToPost: {
            type: 'object',
            additionalProperties: false,
            properties: {
                active: {
                    type: 'boolean',
                },
                concernedRestaurantCategories: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                },
            },
            required: ['active', 'concernedRestaurantCategories'],

            default: {
                active: false,
                concernedRestaurantCategories: [],
            },
        },
    },
    required: [
        '_id',
        'key',
        'byDefault',
        'createdAt',
        'name',
        'startDate',
        'updatedAt',
        'shouldSuggestSpecialHourUpdate',
        'shouldSuggestToPost',
    ],
    definitions: {
        Example: {
            type: 'object',
            additionalProperties: false,
            properties: {
                fr: {
                    type: 'string',
                },
                en: {
                    type: 'string',
                },
            },
            required: [],
            title: 'Example',
        },
        Name: {
            type: 'object',
            additionalProperties: false,
            properties: {
                fr: {
                    type: 'string',
                    nullable: true,
                },
                en: {
                    type: 'string',
                    nullable: true,
                },
                es: {
                    type: 'string',
                    nullable: true,
                },
                it: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: [],
            title: 'Name',
        },
    },
} as const satisfies JSONSchemaExtraProps;
