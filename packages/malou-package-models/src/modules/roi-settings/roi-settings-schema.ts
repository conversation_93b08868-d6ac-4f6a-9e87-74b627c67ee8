import { Currency } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const roiSettingsJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'RoiSettings',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        currency: {
            enum: Object.values(Currency),
            default: Currency.EUR,
        },
        averageTicket: {
            type: 'number',
        },
        minRevenue: {
            type: 'number',
        },
        maxRevenue: {
            type: 'number',
        },
        duplicatedFromRestaurantId: {
            type: 'string',
            format: 'objectId',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    required: ['_id', 'restaurantId', 'averageTicket', 'currency', 'minRevenue', 'maxRevenue', 'createdAt', 'updatedAt'],
} as const satisfies JSONSchemaExtraProps;
