import {
    ApplicationLanguage,
    DAYS,
    descriptionSize,
    emailRegex,
    FoundStatusOnPlatform,
    PlatformKey,
    timeRegex,
    urlRegex,
    withOrWithoutHttpPartUrlRegex,
} from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const platformJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Platform',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        // TODO rename platformkey
        key: {
            enum: Object.values(PlatformKey),
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        apiEndpoint: {
            type: 'string',
            nullable: true,
        },
        apiEndpointV2: {
            type: 'string',
        },
        attributes: {
            type: 'array',
            nullable: true,
            items: {
                type: 'string',
            },
        },
        categoryList: {
            type: 'array',
            nullable: true,
            items: {
                type: 'string',
                format: 'objectId',
                ref: 'Category',
            },
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        credentials: {
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
                ref: 'Credential',
            },
            default: [],
        },
        descriptions: {
            type: 'array',
            items: {
                $ref: '#/definitions/Description',
            },
        },
        lockedFields: {
            type: 'array',
            items: {
                type: 'string',
            },
            default: [],
        },
        menu: {
            $ref: '#/definitions/Menu',
            nullable: true,
        },
        menuUrl: {
            type: 'string',
            format: 'uri',
            match: withOrWithoutHttpPartUrlRegex,
            nullable: true,
            default: null,
        },
        platformPropertiesToUpdate: {
            type: 'array',
            items: {
                type: 'string',
            },
            default: [],
        },
        regularHours: {
            type: 'array',
            nullable: true,
            items: {
                $ref: '#/definitions/RegularHour',
            },
        },
        socialId: {
            type: 'string',
            nullable: true,
        },
        specialHours: {
            type: 'array',
            items: {
                $ref: '#/definitions/SpecialHour',
            },
        },
        unmappedCategories: {
            type: 'array',
            nullable: true,
            items: {
                type: 'string',
            },
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        watchedAccounts: {
            type: 'array',
            items: {
                $ref: '#/definitions/WatchedAccount',
            },
        },
        address: {
            $ref: '#/definitions/Address',
            nullable: true,
        },
        category: {
            anyOf: [
                { type: 'string', format: 'objectId', ref: 'Category' },
                {
                    type: 'null',
                },
            ],
        },
        isClosedTemporarily: {
            type: 'boolean',
            nullable: true,
        },
        name: {
            type: 'string',
            nullable: true,
        },
        openingDate: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time',
                },
                {
                    type: 'null',
                },
            ],
        },
        phone: {
            $ref: '#/definitions/Phone',
            nullable: true,
        },
        rating: {
            type: 'number',
            nullable: true,
        },
        website: {
            type: 'string',
            format: 'uri',
            match: withOrWithoutHttpPartUrlRegex,
            nullable: true,
        },
        socialLink: {
            type: 'string',
            format: 'uri',
            match: urlRegex,
            nullable: true,
        },
        businessSocialLinks: {
            $ref: '#/definitions/BusinessSocialLinks',
            nullable: true,
        },
        isClaimed: {
            type: 'boolean',
        },
        latlng: {
            $ref: '#/definitions/Latlng',
            nullable: true,
        },
        email: {
            type: 'string',
            nullable: true,
            match: emailRegex,
        },
        hasTransitionedToNewPageExperience: {
            type: 'boolean',
        },
        unmappedHours: {
            type: 'string',
            nullable: true,
        },
        parentSocialId: {
            type: 'string',
        },
        lastSubscribedToFacebookWebhookAt: {
            type: 'string',
            format: 'date-time',
        },
        foundStatusOnPlatform: {
            enum: Object.values(FoundStatusOnPlatform),
            default: FoundStatusOnPlatform.FOUND,
        },
        profilePictureUrl: {
            type: 'string',
            format: 'uri',
            match: urlRegex,
            nullable: true,
        },
        drnId: {
            type: 'string',
            description: 'Only for Deliveroo, used to retrieve public reviews',
        },
    },
    required: ['_id', 'createdAt', 'key', 'restaurantId', 'updatedAt'],
    definitions: {
        Address: {
            type: 'object',
            additionalProperties: false,
            properties: {
                country: {
                    type: 'string',
                    nullable: true,
                },
                formattedAddress: {
                    type: 'string',
                },
                locality: {
                    type: 'string',
                },
                postalCode: {
                    type: 'string',
                    nullable: true,
                },
                regionCode: {
                    type: 'string',
                    nullable: true,
                },
                route: {
                    type: 'string',
                },
                streetNumber: {
                    type: 'string',
                },
                administrativeArea: {
                    type: 'string',
                },
            },
            required: [],
            title: 'Address',
        },
        BusinessSocialLinks: {
            type: 'object',
            additionalProperties: false,
            properties: {
                home: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                },
                media: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                },
            },
            required: ['home', 'media'],
            title: 'BusinessSocialLinks',
        },
        Description: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                language: {
                    enum: [null, ...Object.values(ApplicationLanguage)],
                    nullable: true,
                },
                size: {
                    enum: [descriptionSize.LONG.key, descriptionSize.SHORT.key],
                },
                text: {
                    type: 'string',
                    trim: true,
                },
                active: {
                    type: 'boolean',
                },
                createdAt: {
                    type: 'string',
                    format: 'date-time',
                },
                updatedAt: {
                    type: 'string',
                    format: 'date-time',
                },
            },
            required: ['_id', 'createdAt', 'text', 'updatedAt', 'size'],
            title: 'Description',
        },
        Latlng: {
            type: 'object',
            additionalProperties: false,
            properties: {
                lat: {
                    type: 'number',
                },
                lng: {
                    type: 'number',
                },
                latitude: {
                    type: 'number',
                },
                longitude: {
                    type: 'number',
                },
            },
            required: [],
            title: 'Latlng',
        },
        Menu: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                sections: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/Section',
                    },
                },
                socialId: {
                    type: 'string',
                },
                updatedAt: {
                    type: 'string',
                    format: 'date-time',
                },
                createdAt: {
                    type: 'string',
                    format: 'date-time',
                },
                url: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    trim: true,
                },
            },
            required: ['_id', 'createdAt', 'sections', 'updatedAt', 'socialId'],
            title: 'Menu',
        },
        Section: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                label: {
                    type: 'string',
                    trim: true,
                },
                socialId: {
                    type: 'string',
                },
                items: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/Item',
                    },
                },
            },
            required: ['_id', 'items', 'label', 'socialId'],
            title: 'Section',
        },
        Item: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                label: {
                    type: 'string',
                },
                description: {
                    type: 'string',
                },
                socialId: {
                    type: 'string',
                },
                price: {
                    $ref: '#/definitions/Price',
                },
            },
            required: ['_id', 'label', 'price', 'socialId'],
            title: 'Item',
        },
        Price: {
            type: 'object',
            additionalProperties: false,
            properties: {
                currencyCode: {
                    type: 'string',
                    minLength: 3,
                    maxLength: 3,
                },
                units: {
                    type: 'integer',
                },
            },
            required: ['currencyCode', 'units'],
            title: 'Price',
        },
        Phone: {
            type: 'object',
            additionalProperties: false,
            properties: {
                prefix: {
                    type: 'integer',
                    nullable: true,
                },
                digits: {
                    type: 'integer',
                    nullable: true,
                },
            },
            required: [],
            title: 'Phone',
        },
        RegularHour: {
            type: 'object',
            additionalProperties: false,
            properties: {
                openDay: {
                    enum: DAYS,
                },
                openTime: {
                    type: 'string',
                    nullable: true,
                    match: timeRegex,
                },
                closeDay: {
                    enum: DAYS,
                },
                closeTime: {
                    type: 'string',
                    nullable: true,
                    match: timeRegex,
                },
                isClosed: {
                    type: 'boolean',
                    default: true,
                },
            },
            required: ['isClosed', 'openDay', 'closeDay'],
            title: 'RegularHour',
        },
        SpecialHour: {
            type: 'object',
            additionalProperties: false,
            properties: {
                startDate: {
                    $ref: '#/definitions/Date',
                },
                openTime: {
                    type: 'string',
                    nullable: true,
                    default: null,
                    match: timeRegex,
                },
                endDate: {
                    $ref: '#/definitions/Date',
                },
                closeTime: {
                    type: 'string',
                    nullable: true,
                    default: null,
                    match: timeRegex,
                },
                isClosed: {
                    type: 'boolean',
                    default: false,
                },
            },
            required: ['isClosed', 'startDate', 'endDate'],
            title: 'SpecialHour',
        },
        Date: {
            type: 'object',
            additionalProperties: false,
            properties: {
                day: {
                    type: 'integer',
                },
                year: {
                    type: 'integer',
                },
                month: {
                    type: 'integer',
                },
            },
            required: ['day', 'month', 'year'],
            title: 'Date',
        },
        WatchedAccount: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                },
                userName: {
                    type: 'string',
                },
                profilePicUrl: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                },
                name: {
                    type: 'string',
                },
                followersCount: {
                    type: 'integer',
                },
                mediaCount: {
                    type: 'integer',
                },
                biography: {
                    type: 'string',
                },
            },
            required: ['_id', 'followersCount', 'mediaCount', 'profilePicUrl', 'userName'],
            title: 'WatchedAccount',
        },
    },
} as const satisfies JSONSchemaExtraProps;
